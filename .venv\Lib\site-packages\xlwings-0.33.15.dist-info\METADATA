Metadata-Version: 2.4
Name: xlwings
Version: 0.33.15
Summary: Make Excel fly: Interact with Excel from Python and vice versa.
Home-page: https://www.xlwings.org
Author: Zoomer Analytics LLC
Author-email: <PERSON> <felix.zu<PERSON><PERSON>@zoomeranalytics.com>
License: BSD 3-clause
Project-URL: Homepage, https://www.xlwings.org
Project-URL: Documentation, https://docs.xlwings.org
Project-URL: Funding, https://www.xlwings.org/pricing
Project-URL: Source, https://github.com/xlwings/xlwings
Project-URL: Changelog, https://docs.xlwings.org/en/stable/whatsnew.html
Keywords: xls,excel,spreadsheet,workbook,vba,macro
Classifier: Development Status :: 4 - Beta
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Office/Business :: Financial :: Spreadsheet
Classifier: License :: OSI Approved :: BSD License
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
License-File: LICENSE_PRO.txt
Requires-Dist: pywin32>=224; platform_system == "Windows"
Requires-Dist: psutil>=2.0.0; platform_system == "Darwin"
Requires-Dist: appscript>=1.0.1; platform_system == "Darwin"
Provides-Extra: reports
Requires-Dist: Jinja2; extra == "reports"
Requires-Dist: pdfrw; extra == "reports"
Requires-Dist: mistune; extra == "reports"
Provides-Extra: all
Requires-Dist: black; extra == "all"
Requires-Dist: isort; extra == "all"
Requires-Dist: Jinja2; extra == "all"
Requires-Dist: pandas; extra == "all"
Requires-Dist: matplotlib; extra == "all"
Requires-Dist: plotly; extra == "all"
Requires-Dist: flask; extra == "all"
Requires-Dist: requests; extra == "all"
Requires-Dist: pdfrw; extra == "all"
Requires-Dist: pytest; extra == "all"
Requires-Dist: mistune; extra == "all"
Provides-Extra: vba-edit
Requires-Dist: watchgod; extra == "vba-edit"
Dynamic: author
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-python

xlwings - Make Excel fly with Python!
=====================================

xlwings (Open Source)
---------------------

xlwings is a `BSD-licensed <http://opensource.org/licenses/BSD-3-Clause>`_ Python library that makes it easy to call Python from Excel and vice versa:

* **Scripting**: Automate/interact with Excel from Python using a syntax that is close to VBA.
* **Macros**: Replace your messy VBA macros with clean and powerful Python code.
* **UDFs**: Write User Defined Functions (UDFs) in Python (Windows only).

**Numpy arrays** and **Pandas Series/DataFrames** are fully supported. xlwings-powered workbooks are easy to distribute and work
on **Windows** and **macOS**.

xlwings includes all files in the xlwings package except the ``pro`` folder, i.e., the ``xlwings.pro`` subpackage.

xlwings Lite
------------

`xlwings Lite <https://lite.xlwings.org>`_ is the easiest way to get started with xlwings. Simply install xlwings Lite from Excel's `add-in store <https://appsource.microsoft.com/en-us/product/office/WA200008175>`_ and build something amazing!

* No local Python installation required
* Works on Windows, macOS, and Excel on the web (incl. the free version of Excel)
* Custom Functions
* Access to the Excel object model
* Python code is stored inside the workbook
* Free for personal and commercial use

xlwings PRO
-----------

xlwings PRO offers additional functionality on top of xlwings (Open Source), including:

* xlwings Server: No local Python installation required, supports Excel on the web and Google Sheets in addition to Excel on Windows and macOS. Integrates with VBA, Office Scripts and Office.js and supports custom functions on all platforms. See `GitHub repo <https://github.com/xlwings/xlwings-server>`_ and `xlwings Server docs <https://server.xlwings.org/>`_.
* xlwings Reports: the flexible, template-based reporting system
* xlwings Reader: A faster and more feature-rich alternative for ``pandas.read_excel()`` (no Excel installation required)
* Easy deployment via 1-click installer and embedded code
* See the `full list of PRO features <https://www.xlwings.org/pricing>`_

xlwings PRO is `source available <https://en.wikipedia.org/wiki/Source-available_software>`_ and dual-licensed under one of the following licenses:

* `PolyForm Noncommercial License 1.0.0 <https://polyformproject.org/licenses/noncommercial/1.0.0>`_ (noncommercial use is free)
* `xlwings PRO License <https://github.com/xlwings/xlwings/blob/main/LICENSE_PRO.txt>`_ (commercial use requires a `paid plan <https://www.xlwings.org/pricing>`_)

**License Key**

To use xlwings PRO, you need to install a license key on a Terminal/Command Prompt like so (alternatively, set the env var ``XLWINGS_LICENSE_KEY``::

    xlwings license update -k YOUR_LICENSE_KEY

See `the docs <https://docs.xlwings.org/en/latest/pro/license_key.html>`_ for more details.

**License key for noncommercial purpose**:

* To use xlwings PRO for free in a noncommercial context, use the following license key: ``noncommercial``.

**License key for commercial purpose**:

* To try xlwings PRO for free in a commercial context, request a trial license key: https://www.xlwings.org/trial
* To use xlwings PRO in a commercial context beyond the trial, you need to enroll in a paid plan (they include additional services like support and the ability to create one-click installers): https://www.xlwings.org/pricing

xlwings PRO licenses are developer licenses, are verified offline (i.e., no telemetry/license server involved) and allow royalty-free deployments to unlimited internal and external end-users and servers for a hassle-free management. Deployments use deploy keys that don't expire but instead are bound to a specific version of xlwings.

Links
-----

* Homepage: https://www.xlwings.org
* Quickstart: https://docs.xlwings.org/en/stable/quickstart.html
* Documentation: https://docs.xlwings.org
* Book (O'Reilly, 2021): https://www.xlwings.org/book
* Video Course: https://training.xlwings.org/p/xlwings
* Source Code: https://github.com/xlwings/xlwings
* xltrail (Version control for Excel files): https://www.xltrail.com 
