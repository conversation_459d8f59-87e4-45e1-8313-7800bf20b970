# KANVAS Spreadsheet Requirements & MITRE Data

## 📋 MITRE ATT&CK Framework Data

### Available MITRE Tactics (39 total)
The database contains 39 MITRE tactics across Enterprise, Mobile, and ICS domains:

**Enterprise Tactics:**
- TA0001-Initial Access (Enterprise)
- TA0002-Execution (Enterprise)  
- TA0003-Persistence (Enterprise)
- TA0004-Privilege Escalation (Enterprise)
- TA0005-Defense Evasion (Enterprise)
- TA0006-Credential Access (Enterprise)
- TA0007-Discovery (Enterprise)
- TA0008-Lateral Movement (Enterprise)
- TA0009-Collection (Enterprise)
- TA0010-Exfiltration (Enterprise)
- TA0011-Command and Control (Enterprise)
- TA0040-Impact (Enterprise)
- TA0042-Resource Development (Enterprise)
- TA0043-Reconnaissance (Enterprise)

**Mobile Tactics:**
- TA0027-Initial Access (Mobile)
- TA0028-Persistence (Mobile)
- TA0029-Privilege Escalation (Mobile)
- TA0030-Defense Evasion (Mobile)
- TA0031-Credential Access (Mobile)
- TA0032-Discovery (Mobile)
- TA0033-Lateral Movement (Mobile)
- TA0034-Impact (Mobile)
- TA0035-Collection (Mobile)
- TA0036-Exfiltration (Mobile)
- TA0037-Command and Control (Mobile)
- TA0041-Execution (Mobile)

**ICS Tactics:**
- TA0100-Collection (ICS)
- TA0101-Command and Control (ICS)
- TA0102-Discovery (ICS)
- TA0103-Evasion (ICS)
- TA0104-Execution (ICS)
- TA0105-Impact (ICS)
- TA0106-Impair Process Control (ICS)
- TA0107-Inhibit Response Function (ICS)
- TA0108-Initial Access (ICS)
- TA0109-Lateral Movement (ICS)
- TA0110-Persistence (ICS)
- TA0111-Privilege Escalation (ICS)

### Available MITRE Techniques (1,109 total)
The database contains 1,109 MITRE techniques in format: `T1234-Technique Name - Description`

**Example techniques:**
- T1078-Valid Accounts - Valid Accounts
- T1078.001-Default Accounts - Default Accounts
- T1078.002-Domain Accounts - Domain Accounts
- T1189-Drive-by Compromise - Drive-by Compromise
- T1190-Exploit Public-Facing Application - Exploit Public-Facing Application

## 🎯 Timeline Visualization Requirements

### Required Columns (at least one from each group):

**📅 Timestamp Column (REQUIRED):**
- `Timestamp_UTC_0`
- `Timestamp`
- `Date/Time`
- `DateTime`

**📝 Description Column (REQUIRED):**
- `Activity`
- `Description`
- `Event Description`

**🎯 MITRE Tactic Column (OPTIONAL):**
- `MITRE Tactic`
- `ATT&CK Tactic`
- `MITRE ATT&CK Tactic`

**🖥️ Event System Column (OPTIONAL):**
- `Event System`
- `Source System`
- `Source`

**👁️ Visualize Column (OPTIONAL):**
- `Visualize`
- `Include`
- `Show`
- Default: "Yes" if column doesn't exist

## 🌐 Network/Lateral Movement Visualization Requirements

### Required Columns (at least one from each group):

**🖥️ Event System Column (REQUIRED):**
- `Event System`
- `Source System`
- `Source`
- `System`

**🎯 Remote System Column (REQUIRED):**
- `Remote System`
- `Destination System`
- `Target`
- `Destination`

**↔️ Direction Column (REQUIRED):**
- `<->`
- `Direction`
- `Flow`
- `Connection Type`

**Values:** `->`, `<-`, or `<->`

**👁️ Visualize Column (REQUIRED):**
- `Visualize`
- `Include`
- `Show`
- `Display`

**Values:** "Yes" to include in visualization

### System Type Requirements
Network visualization requires an **Evidence Management** sheet with:
- System Name column
- System Type column with values like:
  - `Server-DC`, `Server-Generic`, `Server-Application`, `Server-Web`
  - `Desktop`, `Mobile`, `Attacker-Machine`
  - `Gateway-Firewall`, `Gateway-Router`, `Gateway-VPN`

## 🎯 MITRE ATT&CK Mapping Requirements

### Required Columns in Timeline Sheet:

**🎯 MITRE Tactic Column:**
- `MITRE Tactic`
- `ATT&CK Tactic`
- `MITRE ATT&CK Tactic`

**🔧 MITRE Techniques Column:**
- `MITRE Techniques`
- `ATT&CK Techniques`
- `MITRE ATT&CK Techniques`

## 📊 Current Sheet Configuration

### Evidence Management Sheet:
- **Headers:** Row 4
- **Dropdowns:** Row 5
- **Data starts:** Row 6

### Timeline Sheets (Master/Analyst):
- **Headers:** Row 12
- **Dropdowns:** Row 13
- **Data starts:** Row 14

## 💡 Implementation Tips

### For Excel Dropdowns:
1. **MITRE Tactics:** Copy content from `mitre_tactics_dropdown.txt` into row 5 (Evidence Management) or row 13 (Timeline sheets)
2. **MITRE Techniques:** Use CSV files to create dependent dropdowns or use sample from `mitre_techniques_dropdown_sample.txt`
3. **Data Validation:** Create dropdown lists using Excel's Data Validation feature

### For Visualizations:
1. **Timeline:** Ensure timestamp format is recognizable (YYYY-MM-DD HH:MM:SS)
2. **Network:** Use exact direction symbols: `->`, `<-`, `<->`
3. **Visualize:** Set to "Yes" for rows to include in visualizations
4. **MITRE Data:** Use exact format from database (e.g., "T1055 - Process Injection")

### Column Name Matching:
- KANVAS searches for column names in the order listed
- Use exact spelling and capitalization
- First match found will be used
- If no match found, visualization will show error

## 📁 Generated Files

- `mitre_tactics.csv` - All MITRE tactics
- `mitre_techniques.csv` - All MITRE techniques  
- `mitre_techniques_by_tactic.csv` - Techniques organized by tactic
- `mitre_data.json` - Complete data in JSON format
- `mitre_tactics_dropdown.txt` - Comma-separated tactics for Excel
- `mitre_techniques_dropdown_sample.txt` - Sample techniques for Excel
