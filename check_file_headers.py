#!/usr/bin/env python3
"""Check file headers and content types"""

import zipfile
import os

def check_file_headers():
    print("🔍 File Header and Content-Type Analysis")
    print("=" * 45)
    
    xlsm_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(xlsm_file):
        print(f"❌ File not found: {xlsm_file}")
        return
    
    try:
        # Check file signature (first few bytes)
        with open(xlsm_file, 'rb') as f:
            header = f.read(50)
            print(f"📄 File signature: {header[:10]}")
            print(f"📄 Header hex: {header[:20].hex()}")
            
            # ZIP files should start with 'PK'
            if header[:2] == b'PK':
                print(f"✅ Valid ZIP signature")
            else:
                print(f"❌ Invalid ZIP signature")
        
        # Check Content_Types.xml
        with zipfile.ZipFile(xlsm_file, 'r') as zip_file:
            if "[Content_Types].xml" in zip_file.namelist():
                content_types = zip_file.read("[Content_Types].xml").decode('utf-8')
                print(f"\n📋 Content Types XML:")
                
                # Look for macro-related content types
                if "vbaProject" in content_types:
                    print(f"✅ VBA Project content type found")
                else:
                    print(f"❌ VBA Project content type MISSING")
                
                if "xlsm" in content_types:
                    print(f"✅ XLSM content type found")
                else:
                    print(f"❌ XLSM content type missing")
                
                # Show relevant lines
                lines = content_types.split('\n')
                for line in lines:
                    if 'vba' in line.lower() or 'macro' in line.lower() or 'xlsm' in line.lower():
                        print(f"  📝 {line.strip()}")
            
            # Check for VBA project file
            if "xl/vbaProject.bin" in zip_file.namelist():
                vba_info = zip_file.getinfo("xl/vbaProject.bin")
                print(f"\n✅ VBA Project file found: {vba_info.file_size} bytes")
            else:
                print(f"\n❌ VBA Project file missing!")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_file_headers()
