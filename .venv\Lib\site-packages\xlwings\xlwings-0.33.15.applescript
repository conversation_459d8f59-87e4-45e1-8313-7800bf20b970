# Allows to run the script from Script Editor for testing
<PERSON>baHandler("TestString")

on VbaHandler(ParameterString)
	set {PYTHON<PERSON>TH, PythonInterpreter, PythonCommand, WorkbookName, ApplicationFullName, LOG_FILE} to SplitString(ParameterString, "|")
	set ShellCommand to PythonInterpreter & " -B -u -W ignore -c \"import xlwings.utils;xlwings.utils.prepare_sys_path('" & PYTHONPATH & "');" & ¬
		PythonCommand & " \" \"--wb=" & WorkbookName & "\" \"--from_xl=1\" \"--app=" & ApplicationFullName & "\" > /dev/null 2>\"" & LOG_FILE & "\" & "
	try
		do shell script "source ~/.bash_profile"
		return do shell script "source ~/.bash_profile;" & ShellCommand
	on error errMsg number errNumber
		try
			# Try again without sourcing .bash_profile
			return do shell script <PERSON>Command
		on error errMsg number errNumber
			return 1
		end try
	end try
end <PERSON>ba<PERSON>and<PERSON>

on SplitString(TheBigString, fieldSeparator)
	# From <PERSON>'s "Mail from Excel 2016 with Mac Mail example": www.rondebruin.nl
	tell AppleScript
		set oldTID to text item delimiters
		set text item delimiters to fieldSeparator
		set theItems to text items of TheBigString
		set text item delimiters to oldTID
	end tell
	return theItems
end SplitString
