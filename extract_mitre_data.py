#!/usr/bin/env python3
"""
Extract MITRE ATT&CK Framework data from KANVAS database for use in Excel dropdowns.
This script will generate lists of MITRE tactics and techniques that can be used
to populate dropdown values in the .xlsm workbook.
"""

import sqlite3
import csv
import json
import os
from collections import defaultdict

def extract_mitre_data(db_path="kanvas.db"):
    """Extract MITRE tactics and techniques from the database"""
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found!")
        print("   Make sure you have run KANVAS at least once to create the database.")
        return None, None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Extracting MITRE ATT&CK data from database...")
        
        # Get all tactics (PID column contains tactic names)
        cursor.execute("SELECT DISTINCT PID FROM mitre_techniques WHERE PID IS NOT NULL ORDER BY PID")
        tactics = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Found {len(tactics)} MITRE Tactics:")
        for i, tactic in enumerate(tactics, 1):
            print(f"  {i:2d}. {tactic}")
        
        # Get all techniques organized by tactic
        techniques_by_tactic = defaultdict(list)
        all_techniques = []
        
        for tactic in tactics:
            cursor.execute("SELECT ID, Name FROM mitre_techniques WHERE PID = ? ORDER BY ID", (tactic,))
            tactic_techniques = cursor.fetchall()
            
            for tech_id, tech_name in tactic_techniques:
                technique_full = f"{tech_id} - {tech_name}" if tech_name else tech_id
                techniques_by_tactic[tactic].append(technique_full)
                all_techniques.append(technique_full)
        
        print(f"\n🎯 Found {len(all_techniques)} MITRE Techniques total")
        print("📊 Techniques per tactic:")
        for tactic, techniques in techniques_by_tactic.items():
            print(f"  {tactic}: {len(techniques)} techniques")
        
        conn.close()
        return tactics, techniques_by_tactic, all_techniques
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return None, None, None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None, None, None

def save_mitre_data_to_files(tactics, techniques_by_tactic, all_techniques):
    """Save MITRE data to various file formats"""
    
    print("\n💾 Saving MITRE data to files...")
    
    # 1. Save tactics to CSV
    with open("mitre_tactics.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["MITRE_Tactic"])
        for tactic in tactics:
            writer.writerow([tactic])
    print("✅ Saved MITRE tactics to: mitre_tactics.csv")
    
    # 2. Save all techniques to CSV
    with open("mitre_techniques.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["MITRE_Technique_ID"])
        for technique in all_techniques:
            writer.writerow([technique])
    print("✅ Saved MITRE techniques to: mitre_techniques.csv")
    
    # 3. Save techniques by tactic to CSV
    with open("mitre_techniques_by_tactic.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["Tactic", "Technique_ID"])
        for tactic, techniques in techniques_by_tactic.items():
            for technique in techniques:
                writer.writerow([tactic, technique])
    print("✅ Saved MITRE techniques by tactic to: mitre_techniques_by_tactic.csv")
    
    # 4. Save as JSON for easy import
    mitre_data = {
        "tactics": tactics,
        "techniques": all_techniques,
        "techniques_by_tactic": dict(techniques_by_tactic)
    }
    
    with open("mitre_data.json", "w", encoding="utf-8") as f:
        json.dump(mitre_data, f, indent=2, ensure_ascii=False)
    print("✅ Saved MITRE data to: mitre_data.json")
    
    # 5. Create dropdown-ready format (comma-separated for Excel)
    with open("mitre_tactics_dropdown.txt", "w", encoding="utf-8") as f:
        f.write(", ".join(tactics))
    print("✅ Saved tactics dropdown format to: mitre_tactics_dropdown.txt")
    
    # 6. Create sample techniques dropdown (first 20 for Excel cell limit)
    sample_techniques = all_techniques[:20]
    with open("mitre_techniques_dropdown_sample.txt", "w", encoding="utf-8") as f:
        f.write(", ".join(sample_techniques))
    print("✅ Saved sample techniques dropdown to: mitre_techniques_dropdown_sample.txt")

def print_visualization_requirements():
    """Print the requirements for KANVAS visualizations"""
    
    print("\n" + "="*80)
    print("📊 KANVAS VISUALIZATION REQUIREMENTS")
    print("="*80)
    
    print("\n🎯 TIMELINE VISUALIZATION REQUIREMENTS:")
    print("   Required columns (at least one name from each group):")
    print("   📅 Timestamp: 'Timestamp_UTC_0', 'Timestamp', 'Date/Time', 'DateTime'")
    print("   📝 Description: 'Activity', 'Description', 'Event Description'")
    print("   🎯 MITRE Tactic: 'MITRE Tactic', 'ATT&CK Tactic', 'MITRE ATT&CK Tactic'")
    print("   🖥️  Event System: 'Event System', 'Source System', 'Source' (optional)")
    print("   👁️  Visualize: 'Visualize', 'Include', 'Show' (optional, defaults to 'Yes')")
    
    print("\n🌐 NETWORK/LATERAL MOVEMENT VISUALIZATION REQUIREMENTS:")
    print("   Required columns (at least one name from each group):")
    print("   🖥️  Event System: 'Event System', 'Source System', 'Source', 'System'")
    print("   🎯 Remote System: 'Remote System', 'Destination System', 'Target', 'Destination'")
    print("   ↔️  Direction: '<->', 'Direction', 'Flow', 'Connection Type'")
    print("   👁️  Visualize: 'Visualize', 'Include', 'Show', 'Display'")
    print("   🏷️  System Type: Requires 'Evidence Management' sheet with system types")
    
    print("\n📋 EVIDENCE MANAGEMENT SHEET REQUIREMENTS:")
    print("   For network visualization, you need a sheet with system types:")
    print("   🖥️  System Name column")
    print("   🏷️  System Type column with values like:")
    print("      - Server-DC, Server-Generic, Server-Application, Server-Web")
    print("      - Desktop, Mobile, Attacker-Machine")
    print("      - Gateway-Firewall, Gateway-Router, Gateway-VPN")
    print("      - etc.")
    
    print("\n🎯 MITRE ATT&CK MAPPING REQUIREMENTS:")
    print("   Required columns in Timeline sheet:")
    print("   🎯 MITRE Tactic: 'MITRE Tactic', 'ATT&CK Tactic', 'MITRE ATT&CK Tactic'")
    print("   🔧 MITRE Techniques: 'MITRE Techniques', 'ATT&CK Techniques', 'MITRE ATT&CK Techniques'")
    
    print("\n💡 TIPS FOR YOUR SPREADSHEET:")
    print("   1. Use exact column names from the lists above")
    print("   2. Timeline data should have timestamps in recognizable date/time format")
    print("   3. Direction column should use: '->', '<-', or '<->' for connections")
    print("   4. Visualize column should contain 'Yes' for rows to include in visualization")
    print("   5. MITRE technique IDs should match the format from the database (e.g., 'T1055 - Process Injection')")

def main():
    print("🚀 KANVAS MITRE ATT&CK Data Extractor")
    print("="*50)
    
    # Extract MITRE data
    tactics, techniques_by_tactic, all_techniques = extract_mitre_data()
    
    if tactics is None:
        print("❌ Failed to extract MITRE data. Exiting.")
        return
    
    # Save to files
    save_mitre_data_to_files(tactics, techniques_by_tactic, all_techniques)
    
    # Print visualization requirements
    print_visualization_requirements()
    
    print("\n" + "="*80)
    print("✅ EXTRACTION COMPLETE!")
    print("="*80)
    print("\n📁 Files created:")
    print("   • mitre_tactics.csv - List of all MITRE tactics")
    print("   • mitre_techniques.csv - List of all MITRE techniques")
    print("   • mitre_techniques_by_tactic.csv - Techniques organized by tactic")
    print("   • mitre_data.json - Complete data in JSON format")
    print("   • mitre_tactics_dropdown.txt - Comma-separated tactics for Excel dropdown")
    print("   • mitre_techniques_dropdown_sample.txt - Sample techniques for Excel dropdown")
    
    print("\n💡 To use in Excel:")
    print("   1. Copy content from mitre_tactics_dropdown.txt into your dropdown row")
    print("   2. For techniques, use the CSV files or create dependent dropdowns")
    print("   3. Follow the visualization requirements printed above")

if __name__ == "__main__":
    main()
