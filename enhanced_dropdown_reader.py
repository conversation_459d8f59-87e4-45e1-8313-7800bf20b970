#!/usr/bin/env python3
"""
Enhanced dropdown reader that tries multiple methods to extract dropdown values from Excel files.
This includes reading from Data Validation, Named Ranges, and dedicated dropdown sheets.
"""

import openpyxl
import sys
import os

class EnhancedDropdownReader:
    def __init__(self, workbook_path):
        self.workbook_path = workbook_path
        self.workbook = None
        self.dropdown_values = {}
        
    def load_workbook(self):
        """Load the Excel workbook"""
        try:
            self.workbook = openpyxl.load_workbook(self.workbook_path, data_only=False)
            print(f"✅ Loaded workbook: {self.workbook_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to load workbook: {e}")
            return False
    
    def read_all_dropdown_methods(self):
        """Try all methods to read dropdown values"""
        if not self.workbook:
            return {}
        
        all_dropdowns = {}
        
        # Method 1: Data Validation (openpyxl)
        print("\n🔍 Method 1: Reading Data Validation rules...")
        dv_dropdowns = self.read_data_validation_dropdowns()
        if dv_dropdowns:
            all_dropdowns.update(dv_dropdowns)
            print(f"  ✅ Found {len(dv_dropdowns)} dropdowns from Data Validation")
        else:
            print("  ⚠️  No Data Validation dropdowns found")
        
        # Method 2: Named Ranges
        print("\n🔍 Method 2: Reading Named Ranges...")
        nr_dropdowns = self.read_named_ranges()
        if nr_dropdowns:
            all_dropdowns.update(nr_dropdowns)
            print(f"  ✅ Found {len(nr_dropdowns)} dropdowns from Named Ranges")
        else:
            print("  ⚠️  No Named Range dropdowns found")
        
        # Method 3: Dedicated dropdown sheets
        print("\n🔍 Method 3: Reading dedicated dropdown sheets...")
        sheet_dropdowns = self.read_dropdown_sheets()
        if sheet_dropdowns:
            all_dropdowns.update(sheet_dropdowns)
            print(f"  ✅ Found {len(sheet_dropdowns)} dropdowns from dedicated sheets")
        else:
            print("  ⚠️  No dedicated dropdown sheets found")
        
        # Method 4: Look for common dropdown patterns
        print("\n🔍 Method 4: Looking for common dropdown patterns...")
        pattern_dropdowns = self.read_dropdown_patterns()
        if pattern_dropdowns:
            all_dropdowns.update(pattern_dropdowns)
            print(f"  ✅ Found {len(pattern_dropdowns)} dropdowns from patterns")
        else:
            print("  ⚠️  No dropdown patterns found")
        
        return all_dropdowns
    
    def read_data_validation_dropdowns(self):
        """Read dropdowns from Excel Data Validation rules"""
        dropdowns = {}
        
        try:
            for sheet_name in self.workbook.sheetnames:
                sheet = self.workbook[sheet_name]
                
                if hasattr(sheet, 'data_validations') and sheet.data_validations:
                    print(f"    📋 Checking {sheet_name}: {len(sheet.data_validations)} validation rules")
                    
                    for dv in sheet.data_validations:
                        if dv.type == "list" and dv.formula1:
                            # Get affected cells
                            for cell_range in dv.cells:
                                for cell in cell_range:
                                    col_letter = cell.column_letter
                                    
                                    # Try to get header (assuming row 12 for Timeline sheets, row 4 for Evidence Management)
                                    header_row = 12 if "Timeline" in sheet_name else 4
                                    header_cell = sheet.cell(row=header_row, column=cell.column)
                                    header = header_cell.value if header_cell.value else f"Column_{col_letter}"
                                    
                                    # Parse formula
                                    values = self.parse_validation_formula(dv.formula1, sheet)
                                    if values:
                                        key = f"{sheet_name}_{header}"
                                        dropdowns[key] = {
                                            'sheet': sheet_name,
                                            'column': col_letter,
                                            'header': header,
                                            'values': values,
                                            'method': 'data_validation'
                                        }
                                        print(f"      ✅ {header} ({col_letter}): {len(values)} values")
        
        except Exception as e:
            print(f"    ❌ Error reading data validation: {e}")
        
        return dropdowns
    
    def read_named_ranges(self):
        """Read dropdowns from Named Ranges"""
        dropdowns = {}
        
        try:
            if hasattr(self.workbook, 'defined_names'):
                print(f"    📋 Found {len(self.workbook.defined_names)} named ranges")
                
                for named_range in self.workbook.defined_names:
                    name = named_range.name
                    if any(keyword in name.lower() for keyword in ['dropdown', 'list', 'values', 'options']):
                        try:
                            # Get the range reference
                            destinations = named_range.destinations
                            for sheet_name, cell_range in destinations:
                                sheet = self.workbook[sheet_name]
                                values = []
                                
                                # Read values from the range
                                for row in sheet[cell_range]:
                                    for cell in row:
                                        if cell.value:
                                            values.append(str(cell.value))
                                
                                if values:
                                    dropdowns[name] = {
                                        'sheet': sheet_name,
                                        'range': cell_range,
                                        'header': name,
                                        'values': values,
                                        'method': 'named_range'
                                    }
                                    print(f"      ✅ {name}: {len(values)} values")
                        except Exception as e:
                            print(f"      ❌ Error reading named range {name}: {e}")
        
        except Exception as e:
            print(f"    ❌ Error reading named ranges: {e}")
        
        return dropdowns
    
    def read_dropdown_sheets(self):
        """Look for sheets that might contain dropdown lists"""
        dropdowns = {}
        
        # Look for sheets with names suggesting they contain dropdown data
        dropdown_sheet_names = ['dropdown', 'lists', 'values', 'options', 'picklist', 'salesforcepicklists']
        
        for sheet_name in self.workbook.sheetnames:
            if any(keyword in sheet_name.lower() for keyword in dropdown_sheet_names):
                print(f"    📋 Found potential dropdown sheet: {sheet_name}")
                
                try:
                    sheet = self.workbook[sheet_name]
                    
                    # Try to read column-based dropdowns
                    for col in range(1, min(sheet.max_column + 1, 20)):  # Check first 20 columns
                        header_cell = sheet.cell(row=1, column=col)
                        if header_cell.value:
                            header = str(header_cell.value)
                            values = []
                            
                            # Read values from this column
                            for row in range(2, sheet.max_row + 1):
                                cell = sheet.cell(row=row, column=col)
                                if cell.value:
                                    values.append(str(cell.value))
                            
                            if values:
                                key = f"{sheet_name}_{header}"
                                dropdowns[key] = {
                                    'sheet': sheet_name,
                                    'column': openpyxl.utils.get_column_letter(col),
                                    'header': header,
                                    'values': values,
                                    'method': 'dropdown_sheet'
                                }
                                print(f"      ✅ {header}: {len(values)} values")
                
                except Exception as e:
                    print(f"    ❌ Error reading dropdown sheet {sheet_name}: {e}")
        
        return dropdowns
    
    def read_dropdown_patterns(self):
        """Look for common dropdown patterns in main sheets"""
        dropdowns = {}
        
        # Check specific columns that commonly have dropdowns
        target_columns = {
            'Timeline (Master)': {
                'D': 'Artifact',
                'I': 'Mitre Technique'
            },
            'Evidence Management': {
                'D': 'Collection Type',
                'G': 'Operating System'
            }
        }
        
        for sheet_name, columns in target_columns.items():
            if sheet_name in self.workbook.sheetnames:
                sheet = self.workbook[sheet_name]
                print(f"    📋 Checking {sheet_name} for dropdown patterns")
                
                for col_letter, expected_header in columns.items():
                    try:
                        col_num = openpyxl.utils.column_index_from_string(col_letter)
                        
                        # Check header
                        header_row = 12 if "Timeline" in sheet_name else 4
                        header_cell = sheet.cell(row=header_row, column=col_num)
                        actual_header = header_cell.value if header_cell.value else f"Column_{col_letter}"
                        
                        # Look for unique values in this column (potential dropdown options)
                        data_start_row = 14 if "Timeline" in sheet_name else 6
                        values = set()
                        
                        for row in range(data_start_row, min(sheet.max_row + 1, data_start_row + 100)):
                            cell = sheet.cell(row=row, column=col_num)
                            if cell.value and str(cell.value).strip():
                                values.add(str(cell.value).strip())
                        
                        if values and len(values) <= 50:  # Reasonable number for dropdown
                            sorted_values = sorted(list(values))
                            key = f"{sheet_name}_{actual_header}"
                            dropdowns[key] = {
                                'sheet': sheet_name,
                                'column': col_letter,
                                'header': actual_header,
                                'values': sorted_values,
                                'method': 'pattern_analysis'
                            }
                            print(f"      ✅ {actual_header} ({col_letter}): {len(sorted_values)} unique values")
                    
                    except Exception as e:
                        print(f"      ❌ Error checking column {col_letter}: {e}")
        
        return dropdowns
    
    def parse_validation_formula(self, formula, sheet):
        """Parse Excel data validation formula"""
        try:
            formula = formula.strip('"\'')
            
            # Handle different formula types
            if ',' in formula:
                return [v.strip() for v in formula.split(',') if v.strip()]
            elif ';' in formula:
                return [v.strip() for v in formula.split(';') if v.strip()]
            elif formula.startswith('$') or '!' in formula:
                # Range reference - would need more complex parsing
                return []
            else:
                return [formula] if formula else []
        
        except Exception as e:
            print(f"      ❌ Error parsing formula '{formula}': {e}")
            return []

def main():
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print("🚀 Enhanced Dropdown Reader Test")
    print("=" * 50)
    
    reader = EnhancedDropdownReader(test_file)
    
    if reader.load_workbook():
        dropdowns = reader.read_all_dropdown_methods()
        
        print("\n" + "=" * 50)
        print("📊 SUMMARY OF FOUND DROPDOWNS")
        print("=" * 50)
        
        if dropdowns:
            for key, dropdown in dropdowns.items():
                print(f"\n🔽 {dropdown['header']} ({dropdown['sheet']} - {dropdown['column']})")
                print(f"   Method: {dropdown['method']}")
                print(f"   Values: {dropdown['values'][:10]}{'...' if len(dropdown['values']) > 10 else ''}")
                print(f"   Total: {len(dropdown['values'])} options")
        else:
            print("❌ No dropdowns found using any method")
            print("\n💡 Suggestions:")
            print("1. Check if the Excel file uses a different dropdown method")
            print("2. Try using xlwings library for direct Excel integration")
            print("3. Manually specify dropdown values in the application")

if __name__ == "__main__":
    main()
