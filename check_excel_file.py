#!/usr/bin/env python3
"""Check Excel file integrity and basic info"""

import os
import openpyxl

def check_excel_file():
    print("🔍 Excel File Integrity Check")
    print("=" * 40)
    
    excel_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        return
    
    # Check file size
    file_size = os.path.getsize(excel_file)
    print(f"📁 File exists: {excel_file}")
    print(f"📊 File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    # Try to open with openpyxl
    try:
        print(f"\n🔍 Testing openpyxl access...")
        workbook = openpyxl.load_workbook(excel_file)
        print(f"✅ openpyxl can read the file")
        print(f"📋 Sheets found: {workbook.sheetnames}")
        workbook.close()
    except Exception as e:
        print(f"❌ openpyxl error: {e}")
    
    # Check file permissions
    try:
        print(f"\n🔍 Testing file permissions...")
        with open(excel_file, 'rb') as f:
            first_bytes = f.read(100)
        print(f"✅ File is readable")
        print(f"📄 File header: {first_bytes[:20]}")
    except Exception as e:
        print(f"❌ File permission error: {e}")
    
    # Check if file is locked
    try:
        print(f"\n🔍 Testing file lock status...")
        with open(excel_file, 'r+b') as f:
            pass
        print(f"✅ File is not locked")
    except Exception as e:
        print(f"⚠️ File might be locked: {e}")

if __name__ == "__main__":
    check_excel_file()
