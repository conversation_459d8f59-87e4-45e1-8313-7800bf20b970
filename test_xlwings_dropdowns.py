#!/usr/bin/env python3
"""
Test script to verify xlwings dropdown functionality.
This script will test reading Excel Data Validation dropdowns using xlwings.
"""

import sys
import os

# Try to import xlwings
try:
    import xlwings as xw
    XLWINGS_AVAILABLE = True
    print("✅ xlwings is available")
except ImportError:
    XLWINGS_AVAILABLE = False
    print("❌ xlwings is not available")
    print("   Install with: pip install xlwings")
    sys.exit(1)

def test_xlwings_dropdowns():
    """Test reading dropdowns using xlwings"""
    print("\n🔍 Testing xlwings Dropdown Reading...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        print(f"📁 Opening workbook: {test_file}")
        
        # Try to connect to existing Excel instance or create new one
        try:
            app = xw.apps.active
            print("  ✅ Connected to existing Excel instance")
        except:
            app = xw.App(visible=False)
            print("  ✅ Created new Excel instance")
        
        # Open the workbook
        wb = app.books.open(os.path.abspath(test_file))
        print(f"  ✅ Opened workbook")
        
        # Test specific sheets
        test_sheets = ["Timeline (Master)", "Evidence Management"]
        
        for sheet_name in test_sheets:
            if sheet_name in [ws.name for ws in wb.sheets]:
                print(f"\n📋 Testing sheet: {sheet_name}")
                ws = wb.sheets[sheet_name]
                
                # Get sheet dimensions
                used_range = ws.used_range
                if used_range:
                    max_row = used_range.last_cell.row
                    max_col = used_range.last_cell.column
                    print(f"  📊 Sheet size: {max_row} rows x {max_col} columns")
                    
                    # Check for data validation in specific columns
                    target_columns = [4, 9] if sheet_name == "Timeline (Master)" else [3, 4, 6, 7]  # D, I for Timeline; C, D, F, G for Evidence
                    
                    for col in target_columns:
                        if col <= max_col:
                            print(f"\n  🔍 Checking column {col}:")
                            
                            # Get header
                            header_row = 12 if "Timeline" in sheet_name else 4
                            header_cell = ws.range((header_row, col))
                            header = header_cell.value if header_cell.value else f"Column {col}"
                            print(f"    Header: {header}")
                            
                            # Check for data validation
                            data_start_row = 14 if "Timeline" in sheet_name else 6
                            validation_found = False
                            
                            for check_row in range(data_start_row, min(data_start_row + 5, max_row + 1)):
                                try:
                                    cell = ws.range((check_row, col))
                                    excel_cell = cell.api
                                    
                                    if hasattr(excel_cell, 'Validation'):
                                        validation = excel_cell.Validation
                                        
                                        if validation.Type == 3:  # xlValidateList = 3
                                            print(f"    ✅ Found data validation at row {check_row}")
                                            print(f"    Formula: {validation.Formula1}")
                                            
                                            # Try to parse the formula
                                            formula = str(validation.Formula1).strip()
                                            if formula.startswith('='):
                                                formula = formula[1:]
                                            
                                            values = []
                                            if ',' in formula and not ('!' in formula or ':' in formula):
                                                values = [v.strip().strip('"\'') for v in formula.split(',') if v.strip()]
                                            elif '!' in formula and ':' in formula:
                                                print(f"    📍 References range: {formula}")
                                                # Try to resolve the range
                                                try:
                                                    if '!' in formula:
                                                        sheet_part, range_part = formula.split('!', 1)
                                                        ref_sheet_name = sheet_part.strip()
                                                        ref_ws = wb.sheets[ref_sheet_name]
                                                        range_values = ref_ws.range(range_part).value
                                                        
                                                        if isinstance(range_values, list):
                                                            for item in range_values:
                                                                if isinstance(item, list):
                                                                    values.extend([str(x) for x in item if x is not None])
                                                                elif item is not None:
                                                                    values.append(str(item))
                                                        elif range_values is not None:
                                                            values.append(str(range_values))
                                                except Exception as e:
                                                    print(f"    ❌ Could not resolve range: {e}")
                                            else:
                                                if formula:
                                                    values = [formula.strip('"\'')]
                                            
                                            if values:
                                                print(f"    📝 Dropdown values ({len(values)}): {values[:10]}{'...' if len(values) > 10 else ''}")
                                            else:
                                                print(f"    ⚠️  Could not parse dropdown values")
                                            
                                            validation_found = True
                                            break
                                
                                except Exception as e:
                                    continue
                            
                            if not validation_found:
                                print(f"    ⚠️  No data validation found")
                else:
                    print(f"  ⚠️  Could not determine sheet size")
            else:
                print(f"  ❌ Sheet '{sheet_name}' not found")
        
        # Close the workbook
        wb.close()
        print(f"\n✅ Closed workbook")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_availability():
    """Test if Excel is available and working"""
    print("\n🔍 Testing Excel Availability...")
    
    try:
        # Try to create Excel application
        app = xw.App(visible=False)
        print("  ✅ Excel application created successfully")
        
        # Try to create a simple workbook
        wb = app.books.add()
        ws = wb.sheets[0]
        ws.range('A1').value = "Test"
        test_value = ws.range('A1').value
        
        if test_value == "Test":
            print("  ✅ Excel read/write operations working")
        else:
            print("  ❌ Excel read/write operations failed")
        
        # Close without saving
        wb.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Excel not available: {e}")
        return False

def main():
    print("🚀 xlwings Dropdown Testing")
    print("=" * 50)
    
    if not XLWINGS_AVAILABLE:
        return
    
    # Test Excel availability first
    excel_available = test_excel_availability()
    
    if excel_available:
        # Test dropdown reading
        success = test_xlwings_dropdowns()
        
        if success:
            print("\n🎉 xlwings dropdown testing completed successfully!")
            print("\n💡 Integration Notes:")
            print("  • xlwings can read Excel Data Validation dropdowns")
            print("  • Use xlwings for dropdown reading, keep openpyxl for data operations")
            print("  • Excel must be installed on the system")
            print("  • Consider fallback to openpyxl if xlwings fails")
        else:
            print("\n💥 xlwings dropdown testing failed")
    else:
        print("\n💥 Excel is not available - xlwings cannot be used")
        print("   Fallback to openpyxl-only approach recommended")

if __name__ == "__main__":
    main()
