#!/usr/bin/env python3
"""
Test script to verify dropdown functionality for different sheet types in KANVAS application.
This script tests the new sheet-specific configuration system.
"""

import sys
import os
import openpyxl
from PySide6.QtWidgets import QApplication
import logging

# Add the current directory to the path so we can import kanvas modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kanvas import MainApp

def test_sheet_configurations():
    """Test the sheet configuration system"""
    print("Testing KANVAS Sheet Configuration System...")

    # Create sheet configurations directly (without MainApp to avoid QApplication issues)
    sheet_configs = {
        "Evidence Management": {
            "header_row": 4,
            "dropdown_row": 5,
            "data_start_row": 6
        },
        "Timeline (Master)": {
            "header_row": 12,
            "dropdown_row": 13,
            "data_start_row": 14
        },
        "Timeline (Analyst)": {
            "header_row": 12,
            "dropdown_row": 13,
            "data_start_row": 14
        },
        "Timeline": {
            "header_row": 12,
            "dropdown_row": 13,
            "data_start_row": 14
        },
        "default": {
            "header_row": 1,
            "dropdown_row": 2,
            "data_start_row": 2
        }
    }

    def get_sheet_config(sheet_name):
        if sheet_name in sheet_configs:
            return sheet_configs[sheet_name]
        else:
            print(f"Using default configuration for sheet: {sheet_name}")
            return sheet_configs["default"]
    
    # Test 1: Check sheet configurations
    print("\n📋 Testing Sheet Configurations:")
    
    test_sheets = ["Evidence Management", "Timeline (Master)", "Timeline (Analyst)", "Timeline", "Unknown Sheet"]
    
    for sheet_name in test_sheets:
        config = get_sheet_config(sheet_name)
        print(f"  {sheet_name}:")
        print(f"    Header Row: {config['header_row']}")
        print(f"    Dropdown Row: {config['dropdown_row']}")
        print(f"    Data Start Row: {config['data_start_row']}")
    
    # Test 2: Test with actual .xlsm file if available
    test_files = ["ABZ_CompanyName_IR_Project_Workbook_Template.xlsm", "New_Case.xlsm", "New_Case2.xlsx"]
    test_file = None

    for file in test_files:
        if os.path.exists(file):
            test_file = file
            break
    
    if test_file:
        print(f"\n📁 Testing with file: {test_file}")
        
        try:
            workbook = openpyxl.load_workbook(test_file)
            print(f"📋 Available sheets: {workbook.sheetnames}")
            
            # Test each sheet that has a configuration
            for sheet_name in workbook.sheetnames:
                if sheet_name in sheet_configs:
                    print(f"\n🔍 Testing sheet: {sheet_name}")
                    config = get_sheet_config(sheet_name)

                    sheet = workbook[sheet_name]

                    # Test header reading
                    print(f"  Reading headers from row {config['header_row']}:")
                    headers = []
                    for col in range(1, min(sheet.max_column + 1, 11)):  # Limit to first 10 columns for display
                        cell = sheet.cell(row=config['header_row'], column=col)
                        header_value = cell.value if cell.value else f"Column {col}"
                        headers.append(str(header_value))
                    print(f"    Headers: {headers}")

                    # Test dropdown reading (simplified version)
                    print(f"  Reading dropdowns from row {config['dropdown_row']}:")
                    dropdown_found = False
                    for col in range(1, min(sheet.max_column + 1, 11)):
                        cell = sheet.cell(row=config['dropdown_row'], column=col)
                        if cell.value:
                            header_cell = sheet.cell(row=config['header_row'], column=col)
                            header = header_cell.value if header_cell.value else f"Column {col}"
                            print(f"    {header}: {cell.value}")
                            dropdown_found = True

                    if not dropdown_found:
                        print("    No dropdown values found")

                    # Test data reading
                    print(f"  Data starts from row {config['data_start_row']}")
                    if sheet.max_row >= config['data_start_row']:
                        print(f"    Available data rows: {sheet.max_row - config['data_start_row'] + 1}")
                    else:
                        print("    No data rows available")
            
            print("\n✅ File testing completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ ERROR: Failed to test with file {test_file}: {e}")
            return False
    else:
        print("⚠️  WARNING: No test Excel files found.")
        print("   Available files should be: ABZ_CompanyName_IR_Project_Workbook_Template.xlsm, New_Case.xlsm, or New_Case2.xlsx")
        return False

def test_dropdown_parsing():
    """Test the dropdown text parsing functionality"""
    print("\n🔧 Testing Dropdown Text Parsing:")

    # Simple dropdown parsing function (without MainApp)
    def parse_dropdown_text(dropdown_text):
        try:
            dropdown_text = dropdown_text.strip('"\'')
            separators = [',', ';', '|', '\n']
            values = []

            for separator in separators:
                if separator in dropdown_text:
                    values = [v.strip() for v in dropdown_text.split(separator) if v.strip()]
                    break

            if not values and dropdown_text:
                values = [dropdown_text]

            cleaned_values = []
            seen = set()
            for value in values:
                cleaned_value = value.strip()
                if cleaned_value and cleaned_value not in seen:
                    cleaned_values.append(cleaned_value)
                    seen.add(cleaned_value)

            return cleaned_values
        except Exception as e:
            print(f"Error parsing dropdown text '{dropdown_text}': {e}")
            return []
    
    test_cases = [
        "Option1, Option2, Option3",
        "Option1; Option2; Option3",
        "Option1|Option2|Option3",
        "Option1\nOption2\nOption3",
        "Single Option",
        '"Quoted Option1", "Quoted Option2"',
        "Option1,  Option2  , Option3  ",  # With extra spaces
        "",  # Empty
        "Option1, Option1, Option2",  # With duplicates
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        result = parse_dropdown_text(test_text)
        print(f"  Test {i}: '{test_text}' -> {result}")
    
    print("✅ Dropdown parsing tests completed!")
    return True

if __name__ == "__main__":
    print("🚀 Starting KANVAS Dropdown Functionality Tests...\n")
    
    success1 = test_sheet_configurations()
    success2 = test_dropdown_parsing()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Dropdown functionality is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the issues above.")
        sys.exit(1)
