#!/usr/bin/env python3
"""Create a clean backup of the Excel file"""

import openpyxl
import os
from datetime import datetime

def create_backup():
    print("🔧 Creating Clean Excel Backup")
    print("=" * 40)
    
    original_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    backup_file = f"ABZ_CompanyName_IR_Project_Workbook_Template_BACKUP_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    try:
        # Load the original file
        print(f"📖 Loading original file: {original_file}")
        workbook = openpyxl.load_workbook(original_file)
        
        # Remove SalesforcePicklists sheet if it exists
        if "SalesforcePicklists" in workbook.sheetnames:
            print(f"🗑️ Removing SalesforcePicklists sheet")
            del workbook["SalesforcePicklists"]
        
        # Save as .xlsx (without macros) for compatibility
        print(f"💾 Saving backup as: {backup_file}")
        workbook.save(backup_file)
        workbook.close()
        
        print(f"✅ Backup created successfully!")
        print(f"📁 File: {backup_file}")
        print(f"📊 Size: {os.path.getsize(backup_file):,} bytes")
        
        # Verify the backup
        print(f"\n🔍 Verifying backup...")
        backup_workbook = openpyxl.load_workbook(backup_file)
        print(f"📋 Backup sheets: {backup_workbook.sheetnames}")
        backup_workbook.close()
        
        print(f"\n💡 Try opening the backup file: {backup_file}")
        print(f"   This is a clean .xlsx file without macros")
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")

if __name__ == "__main__":
    create_backup()
