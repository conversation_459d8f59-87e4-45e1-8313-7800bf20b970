#!/usr/bin/env python3
"""
Test script to check if KANVAS imports correctly and identify any issues.
"""

import sys
import traceback

def test_imports():
    """Test importing KANVAS and its dependencies"""
    print("🔍 Testing KANVAS imports...")
    
    try:
        print("  📦 Testing basic imports...")
        import openpyxl
        print("    ✅ openpyxl imported")
        
        from PySide6.QtWidgets import QApplication
        print("    ✅ PySide6 imported")
        
        from PySide6.QtCore import QFileSystemWatcher
        print("    ✅ QFileSystemWatcher imported")
        
        print("  📦 Testing KANVAS import...")
        import kanvas
        print("    ✅ KANVAS imported successfully")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Import failed: {e}")
        print("    📋 Full traceback:")
        traceback.print_exc()
        return False

def test_kanvas_creation():
    """Test creating KANVAS MainApp instance"""
    print("\n🔍 Testing KANVAS MainApp creation...")
    
    try:
        from PySide6.QtWidgets import QApplication
        import kanvas
        
        # Create Qt application
        app = QApplication([])
        print("    ✅ Qt Application created")
        
        # Try to create MainApp instance
        main_app = kanvas.MainApp()
        print("    ✅ MainApp instance created")
        
        return True
        
    except Exception as e:
        print(f"    ❌ MainApp creation failed: {e}")
        print("    📋 Full traceback:")
        traceback.print_exc()
        return False

def main():
    print("🚀 KANVAS Import Test")
    print("=" * 30)
    
    # Test 1: Basic imports
    import_success = test_imports()
    
    if import_success:
        # Test 2: MainApp creation
        creation_success = test_kanvas_creation()
        
        if creation_success:
            print("\n🎉 All tests passed! KANVAS should work correctly.")
        else:
            print("\n💥 MainApp creation failed - there's an issue in the KANVAS code.")
    else:
        print("\n💥 Import failed - missing dependencies or syntax errors.")

if __name__ == "__main__":
    main()
