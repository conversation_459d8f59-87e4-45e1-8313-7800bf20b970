{"tactics": ["Inhibit Response Function (ICS)", "TA0001-Initial Access (Enterprise)", "TA0002-Execution (Enterprise)", "TA0003-Persistence (Enterprise)", "TA0004-Privilege Escalation (Enterprise)", "TA0005-Defense Evasion (Enterprise)", "TA0006-Credential Access (Enterprise)", "TA0007-Discovery (Enterprise)", "TA0008-Lateral Movement (Enterprise)", "TA0009-Collection (Enterprise)", "TA0010-Exfiltration (Enterprise)", "TA0011-Command and Control (Enterprise)", "TA0027-Initial Access (Mobile)", "TA0028-Persistence (Mobile)", "TA0029-Privilege Escalation (Mobile)", "TA0030-Defense Evasion (Mobile)", "TA0031-Credential Access (Mobile)", "TA0032-Discovery (Mobile)", "TA0033-Lateral Movement (Mobile)", "TA0034-Impact (Mobile)", "TA0035-Collection (Mobile)", "TA0036-Exfiltration (Mobile)", "TA0037-Command and Control (Mobile)", "TA0040-Impact (Enterprise)", "TA0041-Execution (Mobile)", "TA0042-Resource Development (Enterprise)", "TA0043-Reconnaissance (Enterprise)", "TA0100-Collection (ICS)", "TA0101-Command and Control (ICS)", "TA0102-Discovery (ICS)", "TA0103-Evasion (ICS)", "TA0104-Execution (ICS)", "TA0105-Impact (ICS)", "TA0106-Impair Process Control (ICS)", "TA0107-Inhibit Response Function (ICS)", "TA0108-Initial Access (ICS)", "TA0109-Lateral Movement (ICS)", "TA0110-Persistence (ICS)", "TA0111-Privilege Escalation (ICS)"], "techniques": ["T0800-Activate Firmware Update Mode - Activate Firmware Update Mode", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1091-Replication Through Removable Media - Replication Through Removable Media", "T1133-External Remote Services - External Remote Services", "T1189-Drive-by Compromise - Drive-by Compromise", "T1189-Drive-by Compromise - Drive-by Compromise", "T1190-Exploit Public-Facing Application - Exploit Public-Facing Application", "T1195-Supply Chain Compromise - Supply Chain Compromise", "T1195.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools", "T1195.002-Compromise Software Supply Chain - Compromise Software Supply Chain", "T1195.003-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain", "T1199-Trusted Relationships - Trusted Relationships", "T1200-Hardware Additions - Hardware Additions", "T1566-<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "T1566.001-Spearphishing Attachment - Spearphishing Attachment", "T1566.002-<PERSON><PERSON><PERSON><PERSON> Link - Spearphishing Link", "T1566.003-Spearphishing via Service - Spearphishing via Service", "T1566.004-<PERSON><PERSON>phishing Voice - Spearphishing Voice", "T1659-Content Injection - Content Injection", "T1659-Content Injection - Content Injection", "T1669-Wi-Fi Networks - Wi-Fi Networks", "T1047-Windows Management Instrumentation - Windows Management Instrumentation", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1059-Command and Scripting Interpreter - Command and Scripting Interpreter", "T1059.001-PowerShell - PowerShell", "T1059.002-AppleScript - AppleScript", "T1059.003-Windows Command Shell - Windows Command Shell", "T1059.004-Unix Shell - Unix Shell", "T1059.005-Visual Basic - Visual Basic", "T1059.006-Python - Python", "T1059.007-JavaScript - JavaScript", "T1059.008-Network Device CLI - Network Device CLI", "T1059.009-Cloud API - Cloud API", "T1059.010-AutoHotKey & AutoIT - AutoHotKey & AutoIT", "T1059.011-<PERSON><PERSON> - <PERSON><PERSON>", "T1059.012-Hypervisor CLI - Hypervisor CLI", "T1072-Software Deployment Tools - Software Deployment Tools", "T1106-Native API - Native API", "T1129-Shared <PERSON> - Shared <PERSON>", "T1203-Exploitation for Client Execution - Exploitation for Client Execution", "T1204-User Execution - User Execution", "T1204.001-Malicious Link - Malicious Link", "T1204.002-Malicious File - Malicious File", "T1204.003-Malicious Image - Malicious Image", "T1204.004-Malicious Copy and Paste - Malicious Copy and Paste", "T1559-Inter-Process Communication - Inter-Process Communication", "T1559.001-Component Object Model - Component Object Model", "T1559.002-Dynamic Data Exchange - Dynamic Data Exchange", "T1559.003-XPC Services - XPC Services", "T1569-System Services - System Services", "T1569.001-Launchctl - Launchctl", "T1569.002-Service Execution - Service Execution", "T1569.003-Systemctl - Systemctl", "T1609-Container Administration Command - Container Administration Command", "T1610-Deploy Container - Deploy Container", "T1648-Serverless Execution - Serverless Execution", "T1651-Cloud Administration Command - Cloud Administration Command", "T1674-Input Injection - Input Injection", "T1675-ESXi Administration Command - ESXi Administration Command", "T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1037.001-<PERSON><PERSON> (Windows) - <PERSON><PERSON> (Windows)", "T1037.002-<PERSON><PERSON> Hook - Login Hook", "T1037.003-Network Logon Script - Network Logon Script", "T1037.004-<PERSON> - <PERSON>", "T1037.005-Startup Items - Startup Items", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1098-Account Manipulation - Account Manipulation", "T1098.001-Additional Cloud Credentials - Additional Cloud Credentials", "T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions", "T1098.003-Additional Cloud Roles - Additional Cloud Roles", "T1098.004-SSH Authorized Keys - SSH Authorized Keys", "T1098.005-Device Registration - Device Registration", "T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles", "T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups", "T1112-Modify Registry - Modify Registry", "T1133-External Remote Services - External Remote Services", "T1136-Create Account - Create Account", "T1136.001-Local Account - Local Account", "T1136.002-Domain Account - Domain Account", "T1136.003-<PERSON> Account - Cloud Account", "T1137-Office Application Startup - Office Application Startup", "T1137.001-Office Template Macros - Office Template <PERSON>ros", "T1137.002-Office Test - Office Test", "T1137.003-Outlook Forms - Outlook Forms", "T1137.004-Outlook Home Page - Outlook Home Page", "T1137.005-Outlook Rules - Outlook Rules", "T1137.006-Add-ins - Add-ins", "T1176-Software Extensions - Software Extensions", "T1176.001-Browser Extensions - Browser Extensions", "T1176.002-IDE Extensions - IDE Extensions", "T1197-<PERSON><PERSON><PERSON> Jobs - BITS Jobs", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1505-Server Software Component - Server Software Component", "T1505.001-SQL Stored Procedures - SQL Stored Procedures", "T1505.002-Transport Agent - Transport Agent", "T1505.003-Web Shell - Web Shell", "T1505.004-IIS Components - IIS Components", "T1505.005-Terminal Services DLL - Terminal Services DLL", "T1505.006-vSphere Installation Bundles - vSphere Installation Bundles", "T1525-Implant Internal Image - Implant Internal Image", "T1542-Pre-OS Boot - Pre-OS Boot", "T1542.001-System Firmware - System Firmware", "T1542.002-Component Firmware - Component Firmware", "T1542.003-Bootkit - Bootkit", "T1542.004-ROMMONkit - ROMMONkit", "T1542.005-TFTP Boot - TFTP Boot", "T1543-Create or Modify System Process - Create or Modify System Process", "T1543.001-Launch Agent - Launch Agent", "T1543.002-Systemd Service - Systemd Service", "T1543.003-Windows Service - Windows Service", "T1543.004-Launch Daemon - Launch Daemon", "T1543.005-Container Service - Container Service", "T1546-Event Triggered Execution - Event Triggered Execution", "T1546.001-Change Default File Association - Change Default File Association", "T1546.002-Screensaver - Screensaver", "T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription", "T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification", "T1546.005-Trap - Trap", "T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition", "T1546.007-<PERSON><PERSON> Helper DLL - <PERSON><PERSON> Helper DLL", "T1546.008-Accessibility Features - Accessibility Features", "T1546.009-AppCert DLLs - AppCert DLLs", "T1546.010-AppInit DLLs - AppInit DLLs", "T1546.011-Application Shimming - Application Shimming", "T1546.012-Image File Execution Options Injection - Image File Execution Options Injection", "T1546.013-PowerShell Profile - PowerShell Profile", "T1546.014-<PERSON><PERSON> <PERSON> <PERSON>ond", "T1546.015-Component Object Model Hijacking - Component Object Model Hijacking", "T1546.016-Installer Packages - Installer Packages", "T1546.017-Udev Rules - Udev Rules", "T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution", "T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder", "T1547.002-Authentication Package - Authentication Package", "T1547.003-Time Providers - Time Providers", "T1547.004-Winlogon Helper DLL - Winlogon Helper DLL", "T1547.005-Security Support Provider - Security Support Provider", "T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions", "T1547.007-Re-opened Applications - Re-opened Applications", "T1547.008-<PERSON><PERSON><PERSON> Driver - LSASS Driver", "T1547.009-Shortcut Modification - Shortcut Modification", "T1547.010-Port Monitors - Port Monitors", "T1547.012-Print Processors - Print Processors", "T1547.013-XDG Autostart Entries - XDG Autostart Entries", "T1547.014-Active Setup - Active Setup", "T1547.015-<PERSON><PERSON> Items - Login Items", "T1554-Compromise Host Software Binary - Compromise Host Software Binary", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1653-Power Settings - Power Settings", "T1668-Exclusive Control - Exclusive Control", "T1671-Cloud Application Integration - Cloud Application Integration", "T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1037.001-<PERSON><PERSON> (Windows) - <PERSON><PERSON> (Windows)", "T1037.002-<PERSON><PERSON> Hook - Login Hook", "T1037.003-Network Logon Script - Network Logon Script", "T1037.004-<PERSON> - <PERSON>", "T1037.005-Startup Items - Startup Items", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1055-Process Injection - Process Injection", "T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection", "T1055.002-Portable Executable Injection - Portable Executable Injection", "T1055.003-Thread Execution Hijacking - Thread Execution Hijacking", "T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call", "T1055.005-Thread Local Storage - Thread Local Storage", "T1055.008-Ptrace System Calls - Ptrace System Calls", "T1055.009-Proc Memory - Proc Memory", "T1055.011-Extra Window Memory Injection - Extra Window Memory Injection", "T1055.012-Process Hollowing - Process Hollowing", "T1055.013-Process Doppelgänging - Process Doppelgänging", "T1055.014-VDSO Hijacking - VDSO Hijacking", "T1055.015-ListPlanting - ListPlanting", "T1068-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1098-Account Manipulation - Account Manipulation", "T1098.001-Additional Cloud Credentials - Additional Cloud Credentials", "T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions", "T1098.003-Additional Cloud Roles - Additional Cloud Roles", "T1098.004-SSH Authorized Keys - SSH Authorized Keys", "T1098.005-Device Registration - Device Registration", "T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles", "T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups", "T1134-Access Token Manipulation - Access Token Manipulation", "T1134.001-Token Impersonation/Theft - Token Impersonation/Theft", "T1134.002-Create Process with Token - Create Process with Token", "T1134.003-Make and Impersonate Token - Make and Impersonate Token", "T1134.004-Parent PID Spoofing - Parent PID Spoofing", "T1134.005-SID-History Injection - SID-History Injection", "T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification", "T1484.001-Group Policy Modification - Group Policy Modification", "T1484.002-Trust Modification - Trust Modification", "T1543-Create or Modify System Process - Create or Modify System Process", "T1543.001-Launch Agent - Launch Agent", "T1543.002-Systemd Service - Systemd Service", "T1543.003-Windows Service - Windows Service", "T1543.004-Launch Daemon - Launch Daemon", "T1543.005-Container Service - Container Service", "T1546-Event Triggered Execution - Event Triggered Execution", "T1546.001-Change Default File Association - Change Default File Association", "T1546.002-Screensaver - Screensaver", "T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription", "T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification", "T1546.005-Trap - Trap", "T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition", "T1546.007-<PERSON><PERSON> Helper DLL - <PERSON><PERSON> Helper DLL", "T1546.008-Accessibility Features - Accessibility Features", "T1546.009-AppCert DLLs - AppCert DLLs", "T1546.010-AppInit DLLs - AppInit DLLs", "T1546.011-Application Shimming - Application Shimming", "T1546.012-Image File Execution Options Injection - Image File Execution Options Injection", "T1546.013-PowerShell Profile - PowerShell Profile", "T1546.014-<PERSON><PERSON> <PERSON> <PERSON>ond", "T1546.015-Component Object Model Hijacking - Component Object Model Hijacking", "T1546.016-Installer Packages - Installer Packages", "T1546.017-Udev Rules - Udev Rules", "T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution", "T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder", "T1547.002-Authentication Package - Authentication Package", "T1547.003-Time Providers - Time Providers", "T1547.004-Winlogon Helper DLL - Winlogon Helper DLL", "T1547.005-Security Support Provider - Security Support Provider", "T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions", "T1547.007-Re-opened Applications - Re-opened Applications", "T1547.008-<PERSON><PERSON><PERSON> Driver - LSASS Driver", "T1547.009-Shortcut Modification - Shortcut Modification", "T1547.010-Port Monitors - Port Monitors", "T1547.012-Print Processors - Print Processors", "T1547.013-XDG Autostart Entries - XDG Autostart Entries", "T1547.014-Active Setup - Active Setup", "T1547.015-<PERSON><PERSON> Items - Login Items", "T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1548.001-Setuid and Setgid - Setuid and Setgid", "T1548.002-Bypass User Account Control - Bypass User Account Control", "T1548.003-<PERSON><PERSON> and <PERSON><PERSON> - <PERSON><PERSON> and <PERSON><PERSON>", "T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt", "T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access", "T1548.006-TCC Manipulation - TCC Manipulation", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1611-Escape to Host - Escape to Host", "T1006-Direct Volume Access - Direct Volume Access", "T1014-Rootkit - Rootkit", "T1027-Obfuscated Files or Information - Obfuscated Files or Information", "T1027.001-<PERSON><PERSON> Padding - Binary Padding", "T1027.002-Software Packing - Software Packing", "T1027.003-Steganography - Steganography", "T1027.004-Compile After Delivery - Compile After Delivery", "T1027.005-Indicator Removal from Tools - Indicator Removal from Tools", "T1027.006-HTM<PERSON> Smuggling - HTML Smuggling", "T1027.007-Dynamic API Resolution - Dynamic API Resolution", "T1027.008-Stripped Payloads - Stripped Payloads", "T1027.009-Embedded Payloads - Embedded Payloads", "T1027.010-Command Obfuscation - Command Obfuscation", "T1027.011-Fileless Storage - Fileless Storage", "T1027.012-LNK Icon Smuggling - LNK Icon Smuggling", "T1027.013-Encrypted/Encoded File - Encrypted/Encoded File", "T1027.014-Polymorphic Code - Polymorphic Code", "T1027.015-Compression - Compression", "T1027.016-Junk Code Insertion - Junk Code Insertion", "T1027.017-<PERSON><PERSON> Smuggling - SVG Smuggling", "T1036-Masquerading - Masquerading", "T1036.001-Invalid Code Signature - Invalid Code Signature", "T1036.002-Right-to-Left Override - Right-to-Left Override", "T1036.003-Rename Legitimate Utilities - Rename Legitimate Utilities", "T1036.004-Masquerade Task or Service - Masquerade Task or Service", "T1036.005-Match Legitimate Resource Name or Location - Match Legitimate Resource Name or Location", "T1036.006-Space after Filename - Space after Filename", "T1036.007-Double File Extension - Double File Extension", "T1036.008-Masquerade File Type - Masquerade File Type", "T1036.009-Break Process Trees - Break Process Trees", "T1036.010-Masquerade Account Name - Masquerade Account Name", "T1036.011-Overwrite Process Arguments - Overwrite Process Arguments", "T1055-Process Injection - Process Injection", "T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection", "T1055.002-Portable Executable Injection - Portable Executable Injection", "T1055.003-Thread Execution Hijacking - Thread Execution Hijacking", "T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call", "T1055.005-Thread Local Storage - Thread Local Storage", "T1055.008-Ptrace System Calls - Ptrace System Calls", "T1055.009-Proc Memory - Proc Memory", "T1055.011-Extra Window Memory Injection - Extra Window Memory Injection", "T1055.012-Process Hollowing - Process Hollowing", "T1055.013-Process Doppelgänging - Process Doppelgänging", "T1055.014-VDSO Hijacking - VDSO Hijacking", "T1055.015-ListPlanting - ListPlanting", "T1070-Indicator Removal - Indicator Removal", "T1070.001-Clear Windows Event Logs - Clear Windows Event Logs", "T1070.002-Clear Linux or Mac System Logs - Clear Linux or Mac System Logs", "T1070.003-Clear Command History - Clear Command History", "T1070.004-File Deletion - File Deletion", "T1070.005-Network Share Connection Removal - Network Share Connection Removal", "T1070.006-Timestomp - Timestomp", "T1070.007-Clear Network Connection History and Configurations - Clear Network Connection History and Configurations", "T1070.008-Clear Mailbox Data - Clear Mailbox Data", "T1070.009-Clear Persistence - Clear Persistence", "T1070.010-Relocate Malware - Relocate Malware", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1112-Modify Registry - Modify Registry", "T1127-Trusted Developer Utilities Proxy Execution - Trusted Developer Utilities Proxy Execution", "T1127.001-MSBuild - MSBuild", "T1127.002-Click<PERSON>nce - ClickOnce", "T1127.003-JamPlus - JamPlus", "T1134-Access Token Manipulation - Access Token Manipulation", "T1134.001-Token Impersonation/Theft - Token Impersonation/Theft", "T1134.002-Create Process with Token - Create Process with Token", "T1134.003-Make and Impersonate Token - Make and Impersonate Token", "T1134.004-Parent PID Spoofing - Parent PID Spoofing", "T1134.005-SID-History Injection - SID-History Injection", "T1140-Deobfuscate/Decode Files or Information - Deobfuscate/Decode Files or Information", "T1197-<PERSON><PERSON><PERSON> Jobs - BITS Jobs", "T1202-Indirect Command Execution - Indirect Command Execution", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1207-Rogue Domain Controller - Rogue Domain Controller", "T1211-Exploitation for Defense Evasion - Exploitation for Defense Evasion", "T1216-System Script Proxy Execution - System Script Proxy Execution", "T1216.001-PubPrn - PubPrn", "T1216.002-SyncAppvPublishingServer - SyncAppvPublishingServer", "T1218-System Binary Proxy Execution - System Binary Proxy Execution", "T1218.001-Compiled HTML File - Compiled HTML File", "T1218.002-Control Panel - Control Panel", "T1218.003-CMSTP - CMSTP", "T1218.004-InstallUtil - InstallUtil", "T1218.005-<PERSON><PERSON> - <PERSON><PERSON>", "T1218.007-<PERSON><PERSON><PERSON><PERSON> - Msiexe<PERSON>", "T1218.008-Odbcconf - Odbcconf", "T1218.009-Regsvcs/Regasm - Regsvcs/Regasm", "T1218.010-Regsvr32 - Regsvr32", "T1218.011-Rundll32 - Rundll32", "T1218.012-Verclsid - Verclsid", "T1218.013-Mavinject - Mavinject", "T1218.014-MMC - MMC", "T1218.015-Electron Applications - Electron Applications", "T1220-XSL Script Processing - XSL Script Processing", "T1221-Template Injection - Template Injection", "T1222-File and Directory Permissions Modification - File and Directory Permissions Modification", "T1222.001-Windows File and Directory Permissions Modification - Windows File and Directory Permissions Modification", "T1222.002-Linux and Mac File and Directory Permissions Modification - Linux and Mac File and Directory Permissions Modification", "T1480-Execution Guardrails - Execution Guardrails", "T1480.001-Environmental Keying - Environmental Keying", "T1480.002-Mutual Exclusion - Mutual Exclusion", "T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification", "T1484.001-Group Policy Modification - Group Policy Modification", "T1484.002-Trust Modification - Trust Modification", "T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1497.001-System Checks - System Checks", "T1497.002-User Activity Based Checks - User Activity Based Checks", "T1497.003-Time Based Evasion - Time Based Evasion", "T1535-Unused/Unsupported Cloud Regions - Unused/Unsupported Cloud Regions", "T1542-Pre-OS Boot - Pre-OS Boot", "T1542.001-System Firmware - System Firmware", "T1542.002-Component Firmware - Component Firmware", "T1542.003-Bootkit - Bootkit", "T1542.004-ROMMONkit - ROMMONkit", "T1542.005-TFTP Boot - TFTP Boot", "T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1548.001-Setuid and Setgid - Setuid and Setgid", "T1548.002-Bypass User Account Control - Bypass User Account Control", "T1548.003-<PERSON><PERSON> and <PERSON><PERSON> - <PERSON><PERSON> and <PERSON><PERSON>", "T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt", "T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access", "T1548.006-TCC Manipulation - TCC Manipulation", "T1550-Use Alternate Authentication Material - Use Alternate Authentication Material", "T1550.001-Application Access Token - Application Access Token", "T1550.002-Pass the Hash - Pass the Hash", "T1550.003-Pass the Ticket - Pass the Ticket", "T1550.004-Web Session <PERSON><PERSON> - Web Session <PERSON>ie", "T1553-Subvert Trust Controls - Subvert Trust Controls", "T1553.001-Gatekeeper Bypass - Gatekeeper Bypass", "T1553.002-Code Signing - Code Signing", "T1553.003-SIP and Trust Provider Hijacking - SIP and Trust Provider Hijacking", "T1553.004-Install Root Certificate - Install Root Certificate", "T1553.005-Mark-of-the-Web Bypass - Mark-of-the-Web Bypass", "T1553.006-Code Signing Policy Modification - Code Signing Policy Modification", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1562-Impair Defenses - Impair Defenses", "T1562.001-Disable or Modify Tools - Disable or Modify Tools", "T1562.002-Disable Windows Event Logging - Disable Windows Event Logging", "T1562.003-Impair Command History Logging - Impair Command History Logging", "T1562.004-Disable or Modify System Firewall - Disable or Modify System Firewall", "T1562.006-Indicator Blocking - Indicator Blocking", "T1562.007-Disable or Modify Cloud Firewall - Disable or Modify Cloud Firewall", "T1562.008-Disable or Modify Cloud Logs - Disable or Modify Cloud Logs", "T1562.009-Safe Mode Boot - Safe Mode Boot", "T1562.010-Downgrade Attack - Downgrade Attack", "T1562.011-Spoof Security Alerting - Spoof Security Alerting", "T1562.012-Disable or Modify Linux Audit System - Disable or Modify Linux Audit System", "T1564-Hide Artifacts - Hide Artifacts", "T1564.001-Hidden Files and Directories - Hidden Files and Directories", "T1564.002-Hidden Users - Hidden Users", "T1564.003-Hidden Window - Hidden Window", "T1564.004-NTFS File Attributes - NTFS File Attributes", "T1564.005-Hidden File System - Hidden File System", "T1564.006-Run Virtual Instance - Run Virtual Instance", "T1564.007-V<PERSON> Stomping - VBA Stomping", "T1564.008-Email Hiding Rules - Email Hiding Rules", "T1564.009-Resource Forking - Resource Forking", "T1564.010-Process Argument Spoofing - Process Argument Spoofing", "T1564.011-Ignore Process Interrupts - Ignore Process Interrupts", "T1564.012-File/Path Exclusions - File/Path Exclusions", "T1564.013-Bind Mounts - Bind Mounts", "T1564.014-Extended Attributes - Extended Attributes", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1578-Modify Cloud Compute Infrastructure - Modify Cloud Compute Infrastructure", "T1578.001-Create Snapshot - Create Snapshot", "T1578.002-Create Cloud Instance - Create Cloud Instance", "T1578.003-Delete Cloud Instance - Delete Cloud Instance", "T1578.004-Revert Cloud Instance - Revert Cloud Instance", "T1578.005-Modify Cloud Compute Configurations - Modify Cloud Compute Configurations", "T1599-Network Boundary Bridging - Network Boundary Bridging", "T1599.001-Network Address Translation Traversal - Network Address Translation Traversal", "T1600-Weaken Encryption - Weaken Encryption", "T1600.001-Reduce Key Space - Reduce Key Space", "T1600.002-Disable Crypto Hardware - Disable Crypto Hardware", "T1601-Modify System Image - Modify System Image", "T1601.001-Patch System Image - Patch System Image", "T1601.002-Downgrade System Image - Downgrade System Image", "T1610-Deploy Container - Deploy Container", "T1612-Build Image on Host - Build Image on Host", "T1620-Reflective Code Loading - Reflective Code Loading", "T1622-Debugger Evasion - Debugger Evasion", "T1647-Plist File Modification - Plist File Modification", "T1656-Impersonation - Impersonation", "T1666-Modify Cloud Resource Hierarchy - Modify Cloud Resource Hierarchy", "T1672-<PERSON><PERSON> Spoofing - <PERSON>ail Spoofing", "T1003-OS Credential Dumping - OS Credential Dumping", "T1003.001-LSASS Memory - LSASS Memory", "T1003.002-Security Account Manager - Security Account Manager", "T1003.003-NTDS - NTDS", "T1003.004-LSA Secrets - LSA Secrets", "T1003.005-Cached Domain Credentials - Cached Domain Credentials", "T1003.006-DCSync - DCSync", "T1003.007-Proc Filesystem - Proc Filesystem", "T1003.008-/etc/passwd and /etc/shadow - /etc/passwd and /etc/shadow", "T1040-Network Sniffing - Network Sniffing", "T1056-Input Capture - Input Capture", "T1056.001-Keylogging - Keylogging", "T1056.002-GUI Input Capture - GUI Input Capture", "T1056.003-Web Portal Capture - Web Portal Capture", "T1056.004-Credential API Hooking - Credential API Hooking", "T1110-Brute Force - Brute Force", "T1110.001-Password Guessing - Password Guessing", "T1110.002-Password Cracking - Password Cracking", "T1110.003-Password Spraying - Password Spraying", "T1110.004-Credential Stuffing - Credential Stuffing", "T1111-Multi-Factor Authentication Interception - Multi-Factor Authentication Interception", "T1187-Forced Authentication - Forced Authentication", "T1212-Exploitation for Credential Access - Exploitation for Credential Access", "T1528-Steal Application Access Token - Steal Application Access Token", "T1539-Steal Web Session <PERSON><PERSON> - Steal Web Session Cookie", "T1552-Unsecured Credentials - Unsecured Credentials", "T1552.001-Credentials In Files - Credentials In Files", "T1552.002-Credentials in Registry - Credentials in Registry", "T1552.003-Bash History - Bash History", "T1552.004-Private Keys - Private Keys", "T1552.005-Cloud Instance Metadata API - Cloud Instance Metadata API", "T1552.006-Group Policy Preferences - Group Policy Preferences", "T1552.007-Container API - Container API", "T1552.008-Chat Messages - Chat Messages", "T1555-Credentials from Password Stores - Credentials from Password Stores", "T1555.001-Keychain - Keychain", "T1555.002-Securityd Memory - Securityd Memory", "T1555.003-Credentials from Web Browsers - Credentials from Web Browsers", "T1555.004-Windows Credential Manager - Windows Credential Manager", "T1555.005-Password Managers - Password Managers", "T1555.006-Cloud Secrets Management Stores - Cloud Secrets Management Stores", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1557-Adversary-in-the-Middle - Adversary-in-the-Middle", "T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay", "T1557.002-ARP <PERSON><PERSON> Poisoning - ARP Cache Poisoning", "T1557.003-DHCP Spoofing - DHCP Spoofing", "T1557.004-Evil Twin - Evil Twin", "T1558-Steal or Forge Kerberos Tickets - Steal or Forge Kerberos Tickets", "T1558.001-Golden Ticket - Golden Ticket", "T1558.002-Silver Ticket - Silver Ticket", "T1558.003-Kerberoasting - Kerberoasting", "T1558.004-AS-REP Roasting - AS-REP Roasting", "T1558.005-Ccache Files - Ccache Files", "T1606-Forge Web Credentials - Forge Web Credentials", "T1606.001-Web Cookies - Web Cookies", "T1606.002-SAML Tokens - SAML Tokens", "T1621-Multi-Factor Authentication Request Generation - Multi-Factor Authentication Request Generation", "T1649-Steal or Forge Authentication Certificates - Steal or Forge Authentication Certificates", "T1007-System Service Discovery - System Service Discovery", "T1010-Application Window Discovery - Application Window Discovery", "T1012-Query Registry - Query Registry", "T1016-System Network Configuration Discovery - System Network Configuration Discovery", "T1016.001-Internet Connection Discovery - Internet Connection Discovery", "T1016.002-Wi-Fi Discovery - Wi-Fi Discovery", "T1018-Remote System Discovery - Remote System Discovery", "T1033-System Owner/User Discovery - System Owner/User Discovery", "T1040-Network Sniffing - Network Sniffing", "T1046-Network Service Discovery - Network Service Discovery", "T1049-System Network Connections Discovery - System Network Connections Discovery", "T1057-Process Discovery - Process Discovery", "T1069-Permission Groups Discovery - Permission Groups Discovery", "T1069.001-Local Groups - Local Groups", "T1069.002-Domain Groups - Domain Groups", "T1069.003-Cloud Groups - Cloud Groups", "T1082-System Information Discovery - System Information Discovery", "T1083-File and Directory Discovery - File and Directory Discovery", "T1087-Account <PERSON> - Account Discovery", "T1087.001-Local Account - Local Account", "T1087.002-Domain Account - Domain Account", "T1087.003-<PERSON><PERSON> Account - <PERSON><PERSON> Account", "T1087.004-<PERSON> Account - Cloud Account", "T1120-Peripheral Device Discovery - Peripheral Device Discovery", "T1124-System Time Discovery - System Time Discovery", "T1135-Network Share Discovery - Network Share Discovery", "T1201-Password Policy Discovery - Password Policy Discovery", "T1217-Browser Information Discovery - Browser Information Discovery", "T1482-Domain Trust Discovery - Domain Trust Discovery", "T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1497.001-System Checks - System Checks", "T1497.002-User Activity Based Checks - User Activity Based Checks", "T1497.003-Time Based Evasion - Time Based Evasion", "T1518-Software Discovery - Software Discovery", "T1518.001-Security Software Discovery - Security Software Discovery", "T1526-Cloud Service Discovery - Cloud Service Discovery", "T1538-Cloud Service Dashboard - Cloud Service Dashboard", "T1580-Cloud Infrastructure Discovery - Cloud Infrastructure Discovery", "T1613-Container and Resource Discovery - Container and Resource Discovery", "T1614-System Location Discovery - System Location Discovery", "T1614.001-System Language Discovery - System Language Discovery", "T1615-Group Policy Discovery - Group Policy Discovery", "T1619-Cloud Storage Object Discovery - Cloud Storage Object Discovery", "T1622-Debugger Evasion - Debugger Evasion", "T1652-<PERSON><PERSON> Driver Discovery - <PERSON><PERSON> Driver Discovery", "T1654-Log Enumeration - Log Enumeration", "T1673-Virtual Machine Discovery - Virtual Machine Discovery", "T1021-Remote Services - Remote Services", "T1021.001-Remote Desktop Protocol - Remote Desktop Protocol", "T1021.002-SMB/Windows Admin Shares - SMB/Windows Admin Shares", "T1021.003-Distributed Component Object Model - Distributed Component Object Model", "T1021.004-SSH - SSH", "T1021.005-VNC - VNC", "T1021.006-Windows Remote Management - Windows Remote Management", "T1021.007-Cloud Services - Cloud Services", "T1021.008-Direct Cloud VM Connections - Direct Cloud VM Connections", "T1072-Software Deployment Tools - Software Deployment Tools", "T1080-Taint Shared Content - Taint Shared Content", "T1091-Replication Through Removable Media - Replication Through Removable Media", "T1210-Exploitation of Remote Services - Exploitation of Remote Services", "T1534-Internal Spearphishing - Internal Spearphishing", "T1550-Use Alternate Authentication Material - Use Alternate Authentication Material", "T1550.001-Application Access Token - Application Access Token", "T1550.002-Pass the Hash - Pass the Hash", "T1550.003-Pass the Ticket - Pass the Ticket", "T1550.004-Web Session <PERSON><PERSON> - Web Session <PERSON>ie", "T1563-Remote Service Session Hijacking - Remote Service Session Hijacking", "T1563.001-SSH Hijacking - SSH Hijacking", "T1563.002-RDP Hijacking - RDP Hijacking", "T1570-Lateral Tool Transfer - Lateral Tool Transfer", "T1005-Data from Local System - Data from Local System", "T1025-Data from Removable Media - Data from Removable Media", "T1039-Data from Network Shared Drive - Data from Network Shared Drive", "T1056-Input Capture - Input Capture", "T1056.001-Keylogging - Keylogging", "T1056.002-GUI Input Capture - GUI Input Capture", "T1056.003-Web Portal Capture - Web Portal Capture", "T1056.004-Credential API Hooking - Credential API Hooking", "T1074-Data Staged - Data Staged", "T1074.001-Local Data Staging - Local Data Staging", "T1074.002-Remote Data Staging - Remote Data Staging", "T1113-Screen Capture - Screen Capture", "T1114-Email Collection - Email Collection", "T1114.001-Local Email Collection - Local Email Collection", "T1114.002-Remote Email Collection - Remote Email Collection", "T1114.003-Email Forwarding Rule - Email Forwarding Rule", "T1115-Clipboard Data - Clipboard Data", "T1119-Automated Collection - Automated Collection", "T1123-Audio Capture - Audio Capture", "T1125-Video Capture - Video Capture", "T1185-<PERSON>rows<PERSON> Session Hijacking - Browser Session Hijacking", "T1213-Data from Information Repositories - Data from Information Repositories", "T1213.001-Confluence - Confluence", "T1213.002-Sharepoint - Sharepoint", "T1213.003-Code Repositories - Code Repositories", "T1213.004-Customer Relationship Management Software - Customer Relationship Management Software", "T1213.005-Messaging Applications - Messaging Applications", "T1530-Data from Cloud Storage - Data from Cloud Storage", "T1557-Adversary-in-the-Middle - Adversary-in-the-Middle", "T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay", "T1557.002-ARP <PERSON><PERSON> Poisoning - ARP Cache Poisoning", "T1557.003-DHCP Spoofing - DHCP Spoofing", "T1557.004-Evil Twin - Evil Twin", "T1560-Archive Collected Data - Archive Collected Data", "T1560.001-Archive via Utility - Archive via Utility", "T1560.002-Archive via Library - Archive via Library", "T1560.003-Archive via Custom Method - Archive via Custom Method", "T1602-Data from Configuration Repository - Data from Configuration Repository", "T1602.001-SNMP (MIB Dump) - SNMP (MIB Dump)", "T1602.002-Network Device Configuration Dump - Network Device Configuration Dump", "T1011-Exfiltration Over Other Network Medium - Exfiltration Over Other Network Medium", "T1011.001-Exfiltration Over Bluetooth - Exfiltration Over Bluetooth", "T1020-Automated Exfiltration - Automated Exfiltration", "T1020.001-Traffic Duplication - Traffic Duplication", "T1029-Scheduled Transfer - Scheduled Transfer", "T1030-Data Transfer Size Limits - Data Transfer Size Limits", "T1041-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel", "T1048-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol", "T1048.001-Exfiltration Over Symmetric Encrypted Non-C2 Protocol - Exfiltration Over Symmetric Encrypted Non-C2 Protocol", "T1048.002-Exfiltration Over Asymmetric Encrypted Non-C2 Protocol - Exfiltration Over Asymmetric Encrypted Non-C2 Protocol", "T1048.003-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol", "T1052-Exfiltration Over Physical Medium - Exfiltration Over Physical Medium", "T1052.001-Exfiltration over USB - Exfiltration over USB", "T1537-Transfer Data to Cloud Account - Transfer Data to Cloud Account", "T1567-Exfiltration Over Web Service - Exfiltration Over Web Service", "T1567.001-Exfiltration to Code Repository - Exfiltration to Code Repository", "T1567.002-Exfiltration to Cloud Storage - Exfiltration to Cloud Storage", "T1567.003-Exfiltration to Text Storage Sites - Exfiltration to Text Storage Sites", "T1567.004-Exfiltration Over Webhook - Exfiltration Over Webhook", "T1001-Data Obfuscation - Data Obfuscation", "T1001.001-Junk Data - Junk Data", "T1001.002-Steganography - Steganography", "T1001.003-Protocol or Service Impersonation - Protocol or Service Impersonation", "T1008-Fallback Channels - Fallback Channels", "T1071-Application Layer Protocol - Application Layer Protocol", "T1071.001-Web Protocols - Web Protocols", "T1071.002-File Transfer Protocols - File Transfer Protocols", "T1071.003-Mail Protocols - Mail Protocols", "T1071.004-DNS - DNS", "T1071.005-Publish/Subscribe Protocols - Publish/Subscribe Protocols", "T1090-Proxy - Proxy", "T1090.001-Internal Proxy - Internal Proxy", "T1090.002-External Proxy - External Proxy", "T1090.003-Multi-hop Proxy - Multi-hop Proxy", "T1090.004-Domain Fronting - Domain Fronting", "T1092-Communication Through Removable Media - Communication Through Removable Media", "T1095-Non-Application Layer Protocol - Non-Application Layer Protocol", "T1102-Web Service - Web Service", "T1102.001-Dead Drop Resolver - Dead Drop Resolver", "T1102.002-Bidirectional Communication - Bidirectional Communication", "T1102.003-One-Way Communication - One-Way Communication", "T1104-Multi-Stage Channels - Multi-Stage Channels", "T1105-Ingress Tool Transfer - Ingress Tool Transfer", "T1132-Data Encoding - Data Encoding", "T1132.001-Standard Encoding - Standard Encoding", "T1132.002-Non-Standard Encoding - Non-Standard Encoding", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1219-Remote Access Tools - Remote Access Tools", "T1219.001-IDE Tunneling - IDE Tunneling", "T1219.002-Remote Desktop Software - Remote Desktop Software", "T1219.003-Remote Access Hardware - Remote Access Hardware", "T1568-Dynamic Resolution - Dynamic Resolution", "T1568.001-Fast Flux DNS - Fast Flux DNS", "T1568.002-Domain Generation Algorithms - Domain Generation Algorithms", "T1568.003-DNS Calculation - DNS Calculation", "T1571-Non-Standard Port - Non-Standard Port", "T1572-Protocol Tunneling - Protocol Tunneling", "T1573-Encrypted Channel - Encrypted Channel", "T1573.001-Symmetric Cryptography - Symmetric Cryptography", "T1573.002-Asymmetric Cryptography - Asymmetric Cryptography", "T1659-Content Injection - Content Injection", "T1665-Hide Infrastructure - Hide Infrastructure", "T1451-SIM Card Swap - SIM Card Swap", "T1456-Drive-By Compromise - Drive-By Compromise", "T1458-Replication Through Removable Media - Replication Through Removable Media", "T1461-Lockscreen Bypass - Lockscreen Bypass", "T1474-Supply Chain Compromise - Supply Chain Compromise", "T1474.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools", "T1474.002-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain", "T1474.003-Compromise Software Supply Chain - Compromise Software Supply Chain", "T1660-<PERSON><PERSON> - <PERSON><PERSON>", "T1661-Application Versioning - Application Versioning", "T1664-Exploitation for Initial Access - Exploitation for Initial Access", "T1398-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1541-Foreground Persistence - Foreground Persistence", "T1577-Compromise Application Executable - Compromise Application Executable", "T1603-Scheduled Task/Job - Scheduled Task/Job", "T1624-Event Triggered Execution - Event Triggered Execution", "T1624.001-Broadcast Receivers - Broadcast Receivers", "T1625-Hijack Execution Flow - Hijack Execution Flow", "T1625.001-System Runtime API Hijacking - System Runtime API Hijacking", "T1645-Compromise Client Software Binary - Compromise Client Software Binary", "T1404-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation", "T1626-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1626.001-Device Administrator Permissions - Device Administrator Permissions", "T1631-Process Injection - Process Injection", "T1631.001-Ptrace System Calls - Ptrace System Calls", "T1406-Obfuscated Files or Information - Obfuscated Files or Information", "T1406.001-Steganography - Steganography", "T1406.002-Software Packing - Software Packing", "T1407-Download New Code at Runtime - Download New Code at Runtime", "T1516-Input Injection - Input Injection", "T1541-Foreground Persistence - Foreground Persistence", "T1575-Native API - Native API", "T1604-Proxy Through Victim - Proxy Through Victim", "T1617-Hooking - Hooking", "T1627-Execution Guardrails - Execution Guardrails", "T1627.001-Geofencing - Geofencing", "T1628-Hide Artifacts - Hide Artifacts", "T1628.001-Suppress Application Icon - Suppress Application Icon", "T1628.002-User Evasion - User Evasion", "T1628.003-Conceal Multimedia Files - Conceal Multimedia Files", "T1629-Impair Defenses - Impair Defenses", "T1629.001-Prevent Application Removal - Prevent Application Removal", "T1629.002-<PERSON>ce Lockout - Device Lockout", "T1629.003-Disable or Modify Tools - Disable or Modify Tools", "T1630-Indicator <PERSON><PERSON><PERSON> on Host - Indicator <PERSON>mo<PERSON> on Host", "T1630.001-Uninstall Malicious Application - Uninstall Malicious Application", "T1630.002-File Deletion - File Deletion", "T1630.003-<PERSON><PERSON><PERSON><PERSON> Root/Jailbreak Indicators - Dis<PERSON>ise Root/Jailbreak Indicators", "T1631-Process Injection - Process Injection", "T1631.001-Ptrace System Calls - Ptrace System Calls", "T1632-Subvert Trust Controls - Subvert Trust Controls", "T1632.001-Code Signing Policy Modification - Code Signing Policy Modification", "T1633-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1633.001-System Checks - System Checks", "T1655-Masquerading - Masquerading", "T1655.001-Match Legitimate Name or Location - Match Legitimate Name or Location", "T1661-Application Versioning - Application Versioning", "T1670-Virtualization Solution - Virtualization Solution", "T1414-Clipboard Data - Clipboard Data", "T1417-Input Capture - Input Capture", "T1417.001-Keylogging - Keylogging", "T1417.002-GUI Input Capture - GUI Input Capture", "T1517-Access Notifications - Access Notifications", "T1634-Credentials from Password Store - Credentials from Password Store", "T1634.001-Keychain - Keychain", "T1635-Steal Application Access Token - Steal Application Access Token", "T1635.001-URI Hijacking - URI Hijacking", "T1418-Software Discovery - Software Discovery", "T1418.001-Security Software Discovery - Security Software Discovery", "T1420-File and Directory Discovery - File and Directory Discovery", "T1421-System Network Connections Discovery - System Network Connections Discovery", "T1422-System Network Configuration Discovery - System Network Configuration Discovery", "T1422.001-Internet Connection Discovery - Internet Connection Discovery", "T1422.002-Wi-Fi Discovery - Wi-Fi Discovery", "T1423-Network Service Scanning - Network Service Scanning", "T1424-Process Discovery - Process Discovery", "T1426-System Information Discovery - System Information Discovery", "T1430-Location Tracking - Location Tracking", "T1430.001-Remote Device Management Services - Remote Device Management Services", "T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes", "T1428-Exploitation of Remote Services - Exploitation of Remote Services", "T1458-Replication Through Removable Media - Replication Through Removable Media", "T1464-Network Denial of Service - Network Denial of Service", "T1471-Data Encrypted for Impact - Data Encrypted for Impact", "T1516-Input Injection - Input Injection", "T1582-SMS Control - SMS Control", "T1616-Call Control - Call Control", "T1640-Account Access Removal - Account Access Removal", "T1641-Data Manipulation - Data Manipulation", "T1641.001-Transmitted Data Manipulation - Transmitted Data Manipulation", "T1642-Endpoint Denial of Service - Endpoint Denial of Service", "T1643-Generate Traffic from Victim - Generate Traffic from Victim", "T1662-Data Destruction - Data Destruction", "T1409-Stored Application Data - Stored Application Data", "T1414-Clipboard Data - Clipboard Data", "T1417-Input Capture - Input Capture", "T1417.001-Keylogging - Keylogging", "T1417.002-GUI Input Capture - GUI Input Capture", "T1429-Audio Capture - Audio Capture", "T1430-Location Tracking - Location Tracking", "T1430.001-Remote Device Management Services - Remote Device Management Services", "T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes", "T1512-Video Capture - Video Capture", "T1513-Screen Capture - Screen Capture", "T1517-Access Notifications - Access Notifications", "T1532-Archive Collected Data - Archive Collected Data", "T1533-Data from Local System - Data from Local System", "T1616-Call Control - Call Control", "T1636-Protected User Data - Protected User Data", "T1636.001-Calendar Entries - Calendar Entries", "T1636.002-Call Log - Call Log", "T1636.003-Contact List - Contact List", "T1636.004-SMS Messages - SMS Messages", "T1638-Adversary-in-the-Middle - Adversary-in-the-Middle", "T1639-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol", "T1639.001-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol", "T1646-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel", "T1437-Application Layer Protocol - Application Layer Protocol", "T1437.001-Web Protocols - Web Protocols", "T1481-Web Service - Web Service", "T1481.001-Dead Drop Resolver - Dead Drop Resolver", "T1481.002-Bidirectional Communication - Bidirectional Communication", "T1481.003-One-Way Communication - One-Way Communication", "T1509-Non-Standard Port - Non-Standard Port", "T1521-Encrypted Channel - Encrypted Channel", "T1521.001-Symmetric Cryptography - Symmetric Cryptography", "T1521.002-Asymmetric Cryptography - Asymmetric Cryptography", "T1521.003-<PERSON><PERSON> - SSL <PERSON>nning", "T1544-Ingress Tool Transfer - Ingress Tool Transfer", "T1616-Call Control - Call Control", "T1637-Dynamic Resolution - Dynamic Resolution", "T1637.001-Domain Generation Algorithms - Domain Generation Algorithms", "T1644-Out of Band Data - Out of Band Data", "T1663-Remote Access Software - Remote Access Software", "T1485-Data Destruction - Data Destruction", "T1485.001-Lifecycle-Triggered Deletion - Lifecycle-Triggered Deletion", "T1486-Data Encrypted for Impact - Data Encrypted for Impact", "T1489-Service Stop - Service Stop", "T1490-Inhibit System Recovery - Inhibit System Recovery", "T1491-Defacement - Defacement", "T1491.001-Internal Defacement - Internal Defacement", "T1491.002-External Defacement - External Defacement", "T1495-Firmware Corruption - Firmware Corruption", "T1496-Resource Hijacking - Resource Hijacking", "T1496.001-Compute Hijacking - Compute Hijacking", "T1496.002-Bandwidth Hijacking - Bandwidth Hijacking", "T1496.003-SMS Pumping - SMS Pumping", "T1496.004-Cloud Service Hijacking - Cloud Service Hijacking", "T1498-Network Denial of Service - Network Denial of Service", "T1498.001-Direct Network Flood - Direct Network Flood", "T1498.002-Reflection Amplification - Reflection Amplification", "T1499-Endpoint Denial of Service - Endpoint Denial of Service", "T1499.001-OS Exhaustion Flood - OS Exhaustion Flood", "T1499.002-Service Exhaustion Flood - Service Exhaustion Flood", "T1499.003-Application Exhaustion Flood - Application Exhaustion Flood", "T1499.004-Application or System Exploitation - Application or System Exploitation", "T1529-System Shutdown/Reboot - System Shutdown/Reboot", "T1531-Account Access Removal - Account Access Removal", "T1561-<PERSON><PERSON> Wipe - Disk Wipe", "T1561.001-Disk Content Wipe - Disk Content Wipe", "T1561.002-Disk Structure Wipe - Disk Structure Wipe", "T1565-Data Manipulation - Data Manipulation", "T1565.001-Stored Data Manipulation - Stored Data Manipulation", "T1565.002-Transmitted Data Manipulation - Transmitted Data Manipulation", "T1565.003-Runtime Data Manipulation - Runtime Data Manipulation", "T1657-Financial Theft - Financial Theft", "T1667-<PERSON><PERSON> Bombing - <PERSON><PERSON> Bombing", "T1575-Native API - Native API", "T1603-Scheduled Task/Job - Scheduled Task/Job", "T1623-Command and Scripting Interpreter - Command and Scripting Interpreter", "T1623.001-Unix Shell - Unix Shell", "T1658-Exploitation for Client Execution - Exploitation for Client Execution", "T1583-Acquire Infrastructure - Acquire Infrastructure", "T1583.001-Domains - Domains", "T1583.002-DNS Server - DNS Server", "T1583.003-Virtual Private Server - Virtual Private Server", "T1583.004-Server - Server", "T1583.005-Botnet - Botnet", "T1583.006-Web Services - Web Services", "T1583.007-Serverless - Serverless", "T1583.008-Malvertising - Malvertising", "T1584-Compromise Infrastructure - Compromise Infrastructure", "T1584.001-Domains - Domains", "T1584.002-DNS Server - DNS Server", "T1584.003-Virtual Private Server - Virtual Private Server", "T1584.004-Server - Server", "T1584.005-Botnet - Botnet", "T1584.006-Web Services - Web Services", "T1584.007-Serverless - Serverless", "T1584.008-Network Devices - Network Devices", "T1585-Establish Accounts - Establish Accounts", "T1585.001-Social Media Accounts - Social Media Accounts", "T1585.002-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1585.003-<PERSON> Accounts - Cloud Accounts", "T1586-Compromise Accounts - Compromise Accounts", "T1586.001-Social Media Accounts - Social Media Accounts", "T1586.002-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1586.003-<PERSON> Accounts - Cloud Accounts", "T1587-Develop Capabilities - Develop Capabilities", "T1587.001-Malware - Malware", "T1587.002-Code Signing Certificates - Code Signing Certificates", "T1587.003-Digital Certificates - Digital Certificates", "T1587.004-Exploits - Exploits", "T1588-Obtain Capabilities - Obtain Capabilities", "T1588.001-Malware - Malware", "T1588.002-<PERSON>l - <PERSON><PERSON>", "T1588.003-Code Signing Certificates - Code Signing Certificates", "T1588.004-Digital Certificates - Digital Certificates", "T1588.005-Exploits - Exploits", "T1588.006-Vulnerabilities - Vulnerabilities", "T1588.007-Artificial Intelligence - Artificial Intelligence", "T1608-Stage Capabilities - Stage Capabilities", "T1608.001-Upload Malware - Upload Malware", "T1608.002-Upload Tool - Upload Tool", "T1608.003-Install Digital Certificate - Install Digital Certificate", "T1608.004-Drive-by Target - Drive-by Target", "T1608.005-Link Target - Link Target", "T1608.006-<PERSON>O Poisoning - SEO Poisoning", "T1650-Acquire Access - Acquire Access", "T1589-Gather Victim Identity Information - Gather Victim Identity Information", "T1589.001-Credentials - Credentials", "T1589.002-<PERSON><PERSON> Addresses - Email Addresses", "T1589.003-Employee Names - Employee Names", "T1590-Gather Victim Network Information - Gather Victim Network Information", "T1590.001-Domain Properties - Domain Properties", "T1590.002-DNS - DNS", "T1590.003-Network Trust Dependencies - Network Trust Dependencies", "T1590.004-Network Topology - Network Topology", "T1590.005-IP Addresses - IP Addresses", "T1590.006-Network Security Appliances - Network Security Appliances", "T1591-<PERSON>ather Victim Org Information - Gather Victim Org Information", "T1591.001-Determine Physical Locations - Determine Physical Locations", "T1591.002-Business Relationships - Business Relationships", "T1591.003-Identify Business Tempo - Identify Business Tempo", "T1591.004-Identify Roles - Identify Roles", "T1592-<PERSON><PERSON> Victim Host Information - Gather Victim Host Information", "T1592.001-Hardware - Hardware", "T1592.002-Software - Software", "T1592.003-Firmware - Firmware", "T1592.004-Client Configurations - Client Configurations", "T1593-Search Open Websites/Domains - Search Open Websites/Domains", "T1593.001-Social Media - Social Media", "T1593.002-Search Engines - Search Engines", "T1593.003-Code Repositories - Code Repositories", "T1594-Search Victim-Owned Websites - Search Victim-Owned Websites", "T1595-Active Scanning - Active Scanning", "T1595.001-Scanning IP Blocks - Scanning IP Blocks", "T1595.002-Vulnerability Scanning - Vulnerability Scanning", "T1595.003-Wordlist Scanning - Wordlist Scanning", "T1596-Search Open Technical Databases - Search Open Technical Databases", "T1596.001-DNS/Passive DNS - DNS/Passive DNS", "T1596.002-WHOIS - WHOIS", "T1596.003-Digital Certificates - Digital Certificates", "T1596.004-CDNs - CDNs", "T1596.005-Scan Databases - Scan Databases", "T1597-Search Closed Sources - Search Closed Sources", "T1597.001-Threat Intel Vendors - Threat Intel Vendors", "T1597.002-Purchase Technical Data - Purchase Technical Data", "T1598-Phishing for Information - Phishing for Information", "T1598.001-Spearphishing Service - Spearphishing Service", "T1598.002-Spearphishing Attachment - Spearphishing Attachment", "T1598.003-<PERSON><PERSON><PERSON><PERSON> Link - Spearphishing Link", "T1598.004-<PERSON><PERSON>phishing Voice - Spearphishing Voice", "T0801-Monitor Process State - Monitor Process State", "T0802-Automated Collection - Automated Collection", "T0811-Data from Information Repositories - Data from Information Repositories", "T0830-Adversary-in-the-Middle - Adversary-in-the-Middle", "T0845-Program Upload - Program Upload", "T0852-Screen Capture - Screen Capture", "T0861-Point & Tag Identification - Point & Tag Identification", "T0868-Detect Operating Mode - Detect Operating Mode", "T0877-I/O Image - I/O Image", "T0887-Wireless Sniffing - Wireless Sniffing", "T0893-Data from Local System - Data from Local System", "T0869-Standard Application Layer Protocol - Standard Application Layer Protocol", "T0884-Connection Proxy - Connection Proxy", "T0885-Commonly Used Port - Commonly Used Port", "T0840-Network Connection Enumeration - Network Connection Enumeration", "T0842-Network Sniffing - Network Sniffing", "T0846-Remote System Discovery - Remote System Discovery", "T0887-Wireless Sniffing - Wireless Sniffing", "T0888-Remote System Information Discovery - Remote System Information Discovery", "T0820-Exploitation for Evasion - Exploitation for Evasion", "T0849-Masquerading - Masquerading", "T0851-Rootkit - Rootkit", "T0856-Spoof Reporting Message - Spoof Reporting Message", "T0858-Change Operating Mode - Change Operating Mode", "T0872-Indicator <PERSON><PERSON><PERSON> on Host - Indicator <PERSON><PERSON><PERSON> on Host", "T0894-System Binary Proxy Execution - System Binary Proxy Execution", "T0807-Command-Line Interface - Command-Line Interface", "T0821-Modify Controller Tasking - Modify Controller Tasking", "T0823-Graphical User Interface - Graphical User Interface", "T0834-Native API - Native API", "T0853-Scripting - Scripting", "T0858-Change Operating Mode - Change Operating Mode", "T0863-User Execution - User Execution", "T0871-Execution through API - Execution through API", "T0874-Hooking - Hooking", "T0895-Autorun Image - Autorun Image", "T0813-Denial of Control - Denial of Control", "T0815-Denial of View - Denial of View", "T0826-Loss of Availability - Loss of Availability", "T0827-Loss of Control - Loss of Control", "T0828-Loss of Productivity and Revenue - Loss of Productivity and Revenue", "T0829-Loss of View - Loss of View", "T0831-Manipulation of Control - Manipulation of Control", "T0832-Manipulation of View - Manipulation of View", "T0837-Loss of Protection - Loss of Protection", "T0879-Damage to Property - Damage to Property", "T0880-Loss of Safety - Loss of Safety", "T0882-Theft of Operational Information - Theft of Operational Information", "T0806-Brute Force I/O - Brute Force I/O", "T0836-Modify Parameter - Modify Parameter", "T0839-Module Firmware - Module Firmware", "T0855-Unauthorized Command Message - Unauthorized Command Message", "T0856-Spoof Reporting Message - Spoof Reporting Message", "T0803-Block Command Message - Block Command Message", "T0804-Block Reporting Message - Block Reporting Message", "T0805-Block Serial COM - Block Serial COM", "T0809-Data Destruction - Data Destruction", "T0814-Denial of Service - Denial of Service", "T0816-<PERSON><PERSON>/Shutdown - <PERSON><PERSON>art/Shutdown", "T0835-Manipulate I/O Image - Manipulate I/O Image", "T0838-Modify Alarm Settings - Modify Alarm Settings", "T0851-Rootkit - Rootkit", "T0857-System Firmware - System Firmware", "T0878-Alarm Suppression - Alarm Suppression", "T0881-Service Stop - Service Stop", "T0892-Change Credential - Change Credential", "T0817-Drive-by Compromise - Drive-by Compromise", "T0819-Exploit Public-Facing Application - Exploit Public-Facing Application", "T0822-External Remote Services - External Remote Services", "T0847-Replication Through Removable Media - Replication Through Removable Media", "T0848-Rogue Master - Rogue Master", "T0860-Wireless Compromise - Wireless Compromise", "T0862-Supply Chain Compromise - Supply Chain Compromise", "T0864-Transient Cyber Asset - Transient Cyber Asset", "T0865-S<PERSON>phishing Attachment - Spearphishing Attachment", "T0866-Exploitation of Remote Services - Exploitation of Remote Services", "T0883-Internet Accessible Device - Internet Accessible Device", "T0886-Remote Services - Remote Services", "T0812-<PERSON><PERSON>ult Credentials - Default Credentials", "T0843-Program Download - Program Download", "T0859-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T0866-Exploitation of Remote Services - Exploitation of Remote Services", "T0867-Lateral Tool Transfer - Lateral Tool Transfer", "T0886-Remote Services - Remote Services", "T0891-Hardcoded Credentials - Hardcoded Credentials", "T0839-Module Firmware - Module Firmware", "T0857-System Firmware - System Firmware", "T0859-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T0873-Project File Infection - Project File Infection", "T0889-Modify Program - Modify Program", "T0891-Hardcoded Credentials - Hardcoded Credentials", "T0874-Hooking - Hooking", "T0890-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation"], "techniques_by_tactic": {"Inhibit Response Function (ICS)": ["T0800-Activate Firmware Update Mode - Activate Firmware Update Mode"], "TA0001-Initial Access (Enterprise)": ["T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1091-Replication Through Removable Media - Replication Through Removable Media", "T1133-External Remote Services - External Remote Services", "T1189-Drive-by Compromise - Drive-by Compromise", "T1189-Drive-by Compromise - Drive-by Compromise", "T1190-Exploit Public-Facing Application - Exploit Public-Facing Application", "T1195-Supply Chain Compromise - Supply Chain Compromise", "T1195.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools", "T1195.002-Compromise Software Supply Chain - Compromise Software Supply Chain", "T1195.003-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain", "T1199-Trusted Relationships - Trusted Relationships", "T1200-Hardware Additions - Hardware Additions", "T1566-<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "T1566.001-Spearphishing Attachment - Spearphishing Attachment", "T1566.002-<PERSON><PERSON><PERSON><PERSON> Link - Spearphishing Link", "T1566.003-Spearphishing via Service - Spearphishing via Service", "T1566.004-<PERSON><PERSON>phishing Voice - Spearphishing Voice", "T1659-Content Injection - Content Injection", "T1659-Content Injection - Content Injection", "T1669-Wi-Fi Networks - Wi-Fi Networks"], "TA0002-Execution (Enterprise)": ["T1047-Windows Management Instrumentation - Windows Management Instrumentation", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1059-Command and Scripting Interpreter - Command and Scripting Interpreter", "T1059.001-PowerShell - PowerShell", "T1059.002-AppleScript - AppleScript", "T1059.003-Windows Command Shell - Windows Command Shell", "T1059.004-Unix Shell - Unix Shell", "T1059.005-Visual Basic - Visual Basic", "T1059.006-Python - Python", "T1059.007-JavaScript - JavaScript", "T1059.008-Network Device CLI - Network Device CLI", "T1059.009-Cloud API - Cloud API", "T1059.010-AutoHotKey & AutoIT - AutoHotKey & AutoIT", "T1059.011-<PERSON><PERSON> - <PERSON><PERSON>", "T1059.012-Hypervisor CLI - Hypervisor CLI", "T1072-Software Deployment Tools - Software Deployment Tools", "T1106-Native API - Native API", "T1129-Shared <PERSON> - Shared <PERSON>", "T1203-Exploitation for Client Execution - Exploitation for Client Execution", "T1204-User Execution - User Execution", "T1204.001-Malicious Link - Malicious Link", "T1204.002-Malicious File - Malicious File", "T1204.003-Malicious Image - Malicious Image", "T1204.004-Malicious Copy and Paste - Malicious Copy and Paste", "T1559-Inter-Process Communication - Inter-Process Communication", "T1559.001-Component Object Model - Component Object Model", "T1559.002-Dynamic Data Exchange - Dynamic Data Exchange", "T1559.003-XPC Services - XPC Services", "T1569-System Services - System Services", "T1569.001-Launchctl - Launchctl", "T1569.002-Service Execution - Service Execution", "T1569.003-Systemctl - Systemctl", "T1609-Container Administration Command - Container Administration Command", "T1610-Deploy Container - Deploy Container", "T1648-Serverless Execution - Serverless Execution", "T1651-Cloud Administration Command - Cloud Administration Command", "T1674-Input Injection - Input Injection", "T1675-ESXi Administration Command - ESXi Administration Command"], "TA0003-Persistence (Enterprise)": ["T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1037.001-<PERSON><PERSON> (Windows) - <PERSON><PERSON> (Windows)", "T1037.002-<PERSON><PERSON> Hook - Login Hook", "T1037.003-Network Logon Script - Network Logon Script", "T1037.004-<PERSON> - <PERSON>", "T1037.005-Startup Items - Startup Items", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1098-Account Manipulation - Account Manipulation", "T1098.001-Additional Cloud Credentials - Additional Cloud Credentials", "T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions", "T1098.003-Additional Cloud Roles - Additional Cloud Roles", "T1098.004-SSH Authorized Keys - SSH Authorized Keys", "T1098.005-Device Registration - Device Registration", "T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles", "T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups", "T1112-Modify Registry - Modify Registry", "T1133-External Remote Services - External Remote Services", "T1136-Create Account - Create Account", "T1136.001-Local Account - Local Account", "T1136.002-Domain Account - Domain Account", "T1136.003-<PERSON> Account - Cloud Account", "T1137-Office Application Startup - Office Application Startup", "T1137.001-Office Template Macros - Office Template <PERSON>ros", "T1137.002-Office Test - Office Test", "T1137.003-Outlook Forms - Outlook Forms", "T1137.004-Outlook Home Page - Outlook Home Page", "T1137.005-Outlook Rules - Outlook Rules", "T1137.006-Add-ins - Add-ins", "T1176-Software Extensions - Software Extensions", "T1176.001-Browser Extensions - Browser Extensions", "T1176.002-IDE Extensions - IDE Extensions", "T1197-<PERSON><PERSON><PERSON> Jobs - BITS Jobs", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1505-Server Software Component - Server Software Component", "T1505.001-SQL Stored Procedures - SQL Stored Procedures", "T1505.002-Transport Agent - Transport Agent", "T1505.003-Web Shell - Web Shell", "T1505.004-IIS Components - IIS Components", "T1505.005-Terminal Services DLL - Terminal Services DLL", "T1505.006-vSphere Installation Bundles - vSphere Installation Bundles", "T1525-Implant Internal Image - Implant Internal Image", "T1542-Pre-OS Boot - Pre-OS Boot", "T1542.001-System Firmware - System Firmware", "T1542.002-Component Firmware - Component Firmware", "T1542.003-Bootkit - Bootkit", "T1542.004-ROMMONkit - ROMMONkit", "T1542.005-TFTP Boot - TFTP Boot", "T1543-Create or Modify System Process - Create or Modify System Process", "T1543.001-Launch Agent - Launch Agent", "T1543.002-Systemd Service - Systemd Service", "T1543.003-Windows Service - Windows Service", "T1543.004-Launch Daemon - Launch Daemon", "T1543.005-Container Service - Container Service", "T1546-Event Triggered Execution - Event Triggered Execution", "T1546.001-Change Default File Association - Change Default File Association", "T1546.002-Screensaver - Screensaver", "T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription", "T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification", "T1546.005-Trap - Trap", "T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition", "T1546.007-<PERSON><PERSON> Helper DLL - <PERSON><PERSON> Helper DLL", "T1546.008-Accessibility Features - Accessibility Features", "T1546.009-AppCert DLLs - AppCert DLLs", "T1546.010-AppInit DLLs - AppInit DLLs", "T1546.011-Application Shimming - Application Shimming", "T1546.012-Image File Execution Options Injection - Image File Execution Options Injection", "T1546.013-PowerShell Profile - PowerShell Profile", "T1546.014-<PERSON><PERSON> <PERSON> <PERSON>ond", "T1546.015-Component Object Model Hijacking - Component Object Model Hijacking", "T1546.016-Installer Packages - Installer Packages", "T1546.017-Udev Rules - Udev Rules", "T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution", "T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder", "T1547.002-Authentication Package - Authentication Package", "T1547.003-Time Providers - Time Providers", "T1547.004-Winlogon Helper DLL - Winlogon Helper DLL", "T1547.005-Security Support Provider - Security Support Provider", "T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions", "T1547.007-Re-opened Applications - Re-opened Applications", "T1547.008-<PERSON><PERSON><PERSON> Driver - LSASS Driver", "T1547.009-Shortcut Modification - Shortcut Modification", "T1547.010-Port Monitors - Port Monitors", "T1547.012-Print Processors - Print Processors", "T1547.013-XDG Autostart Entries - XDG Autostart Entries", "T1547.014-Active Setup - Active Setup", "T1547.015-<PERSON><PERSON> Items - Login Items", "T1554-Compromise Host Software Binary - Compromise Host Software Binary", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1653-Power Settings - Power Settings", "T1668-Exclusive Control - Exclusive Control", "T1671-Cloud Application Integration - Cloud Application Integration"], "TA0004-Privilege Escalation (Enterprise)": ["T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1037.001-<PERSON><PERSON> (Windows) - <PERSON><PERSON> (Windows)", "T1037.002-<PERSON><PERSON> Hook - Login Hook", "T1037.003-Network Logon Script - Network Logon Script", "T1037.004-<PERSON> - <PERSON>", "T1037.005-Startup Items - Startup Items", "T1053-Scheduled Task/Job - Scheduled Task/Job", "T1053.002-At - At", "T1053.003-Cron - Cron", "T1053.005-Scheduled Task - Scheduled Task", "T1053.006-Systemd Timers - Systemd Timers", "T1053.007-Container Orchestration Job - Container Orchestration Job", "T1055-Process Injection - Process Injection", "T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection", "T1055.002-Portable Executable Injection - Portable Executable Injection", "T1055.003-Thread Execution Hijacking - Thread Execution Hijacking", "T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call", "T1055.005-Thread Local Storage - Thread Local Storage", "T1055.008-Ptrace System Calls - Ptrace System Calls", "T1055.009-Proc Memory - Proc Memory", "T1055.011-Extra Window Memory Injection - Extra Window Memory Injection", "T1055.012-Process Hollowing - Process Hollowing", "T1055.013-Process Doppelgänging - Process Doppelgänging", "T1055.014-VDSO Hijacking - VDSO Hijacking", "T1055.015-ListPlanting - ListPlanting", "T1068-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1098-Account Manipulation - Account Manipulation", "T1098.001-Additional Cloud Credentials - Additional Cloud Credentials", "T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions", "T1098.003-Additional Cloud Roles - Additional Cloud Roles", "T1098.004-SSH Authorized Keys - SSH Authorized Keys", "T1098.005-Device Registration - Device Registration", "T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles", "T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups", "T1134-Access Token Manipulation - Access Token Manipulation", "T1134.001-Token Impersonation/Theft - Token Impersonation/Theft", "T1134.002-Create Process with Token - Create Process with Token", "T1134.003-Make and Impersonate Token - Make and Impersonate Token", "T1134.004-Parent PID Spoofing - Parent PID Spoofing", "T1134.005-SID-History Injection - SID-History Injection", "T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification", "T1484.001-Group Policy Modification - Group Policy Modification", "T1484.002-Trust Modification - Trust Modification", "T1543-Create or Modify System Process - Create or Modify System Process", "T1543.001-Launch Agent - Launch Agent", "T1543.002-Systemd Service - Systemd Service", "T1543.003-Windows Service - Windows Service", "T1543.004-Launch Daemon - Launch Daemon", "T1543.005-Container Service - Container Service", "T1546-Event Triggered Execution - Event Triggered Execution", "T1546.001-Change Default File Association - Change Default File Association", "T1546.002-Screensaver - Screensaver", "T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription", "T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification", "T1546.005-Trap - Trap", "T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition", "T1546.007-<PERSON><PERSON> Helper DLL - <PERSON><PERSON> Helper DLL", "T1546.008-Accessibility Features - Accessibility Features", "T1546.009-AppCert DLLs - AppCert DLLs", "T1546.010-AppInit DLLs - AppInit DLLs", "T1546.011-Application Shimming - Application Shimming", "T1546.012-Image File Execution Options Injection - Image File Execution Options Injection", "T1546.013-PowerShell Profile - PowerShell Profile", "T1546.014-<PERSON><PERSON> <PERSON> <PERSON>ond", "T1546.015-Component Object Model Hijacking - Component Object Model Hijacking", "T1546.016-Installer Packages - Installer Packages", "T1546.017-Udev Rules - Udev Rules", "T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution", "T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder", "T1547.002-Authentication Package - Authentication Package", "T1547.003-Time Providers - Time Providers", "T1547.004-Winlogon Helper DLL - Winlogon Helper DLL", "T1547.005-Security Support Provider - Security Support Provider", "T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions", "T1547.007-Re-opened Applications - Re-opened Applications", "T1547.008-<PERSON><PERSON><PERSON> Driver - LSASS Driver", "T1547.009-Shortcut Modification - Shortcut Modification", "T1547.010-Port Monitors - Port Monitors", "T1547.012-Print Processors - Print Processors", "T1547.013-XDG Autostart Entries - XDG Autostart Entries", "T1547.014-Active Setup - Active Setup", "T1547.015-<PERSON><PERSON> Items - Login Items", "T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1548.001-Setuid and Setgid - Setuid and Setgid", "T1548.002-Bypass User Account Control - Bypass User Account Control", "T1548.003-<PERSON><PERSON> and <PERSON><PERSON> - <PERSON><PERSON> and <PERSON><PERSON>", "T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt", "T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access", "T1548.006-TCC Manipulation - TCC Manipulation", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1611-Escape to Host - Escape to Host"], "TA0005-Defense Evasion (Enterprise)": ["T1006-Direct Volume Access - Direct Volume Access", "T1014-Rootkit - Rootkit", "T1027-Obfuscated Files or Information - Obfuscated Files or Information", "T1027.001-<PERSON><PERSON> Padding - Binary Padding", "T1027.002-Software Packing - Software Packing", "T1027.003-Steganography - Steganography", "T1027.004-Compile After Delivery - Compile After Delivery", "T1027.005-Indicator Removal from Tools - Indicator Removal from Tools", "T1027.006-HTM<PERSON> Smuggling - HTML Smuggling", "T1027.007-Dynamic API Resolution - Dynamic API Resolution", "T1027.008-Stripped Payloads - Stripped Payloads", "T1027.009-Embedded Payloads - Embedded Payloads", "T1027.010-Command Obfuscation - Command Obfuscation", "T1027.011-Fileless Storage - Fileless Storage", "T1027.012-LNK Icon Smuggling - LNK Icon Smuggling", "T1027.013-Encrypted/Encoded File - Encrypted/Encoded File", "T1027.014-Polymorphic Code - Polymorphic Code", "T1027.015-Compression - Compression", "T1027.016-Junk Code Insertion - Junk Code Insertion", "T1027.017-<PERSON><PERSON> Smuggling - SVG Smuggling", "T1036-Masquerading - Masquerading", "T1036.001-Invalid Code Signature - Invalid Code Signature", "T1036.002-Right-to-Left Override - Right-to-Left Override", "T1036.003-Rename Legitimate Utilities - Rename Legitimate Utilities", "T1036.004-Masquerade Task or Service - Masquerade Task or Service", "T1036.005-Match Legitimate Resource Name or Location - Match Legitimate Resource Name or Location", "T1036.006-Space after Filename - Space after Filename", "T1036.007-Double File Extension - Double File Extension", "T1036.008-Masquerade File Type - Masquerade File Type", "T1036.009-Break Process Trees - Break Process Trees", "T1036.010-Masquerade Account Name - Masquerade Account Name", "T1036.011-Overwrite Process Arguments - Overwrite Process Arguments", "T1055-Process Injection - Process Injection", "T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection", "T1055.002-Portable Executable Injection - Portable Executable Injection", "T1055.003-Thread Execution Hijacking - Thread Execution Hijacking", "T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call", "T1055.005-Thread Local Storage - Thread Local Storage", "T1055.008-Ptrace System Calls - Ptrace System Calls", "T1055.009-Proc Memory - Proc Memory", "T1055.011-Extra Window Memory Injection - Extra Window Memory Injection", "T1055.012-Process Hollowing - Process Hollowing", "T1055.013-Process Doppelgänging - Process Doppelgänging", "T1055.014-VDSO Hijacking - VDSO Hijacking", "T1055.015-ListPlanting - ListPlanting", "T1070-Indicator Removal - Indicator Removal", "T1070.001-Clear Windows Event Logs - Clear Windows Event Logs", "T1070.002-Clear Linux or Mac System Logs - Clear Linux or Mac System Logs", "T1070.003-Clear Command History - Clear Command History", "T1070.004-File Deletion - File Deletion", "T1070.005-Network Share Connection Removal - Network Share Connection Removal", "T1070.006-Timestomp - Timestomp", "T1070.007-Clear Network Connection History and Configurations - Clear Network Connection History and Configurations", "T1070.008-Clear Mailbox Data - Clear Mailbox Data", "T1070.009-Clear Persistence - Clear Persistence", "T1070.010-Relocate Malware - Relocate Malware", "T1078-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1078.001-<PERSON><PERSON><PERSON> Accounts - De<PERSON>ult Accounts", "T1078.002-Domain Accounts - Domain Accounts", "T1078.003-Local Accounts - Local Accounts", "T1078.004-<PERSON> Accounts - Cloud Accounts", "T1112-Modify Registry - Modify Registry", "T1127-Trusted Developer Utilities Proxy Execution - Trusted Developer Utilities Proxy Execution", "T1127.001-MSBuild - MSBuild", "T1127.002-Click<PERSON>nce - ClickOnce", "T1127.003-JamPlus - JamPlus", "T1134-Access Token Manipulation - Access Token Manipulation", "T1134.001-Token Impersonation/Theft - Token Impersonation/Theft", "T1134.002-Create Process with Token - Create Process with Token", "T1134.003-Make and Impersonate Token - Make and Impersonate Token", "T1134.004-Parent PID Spoofing - Parent PID Spoofing", "T1134.005-SID-History Injection - SID-History Injection", "T1140-Deobfuscate/Decode Files or Information - Deobfuscate/Decode Files or Information", "T1197-<PERSON><PERSON><PERSON> Jobs - BITS Jobs", "T1202-Indirect Command Execution - Indirect Command Execution", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1207-Rogue Domain Controller - Rogue Domain Controller", "T1211-Exploitation for Defense Evasion - Exploitation for Defense Evasion", "T1216-System Script Proxy Execution - System Script Proxy Execution", "T1216.001-PubPrn - PubPrn", "T1216.002-SyncAppvPublishingServer - SyncAppvPublishingServer", "T1218-System Binary Proxy Execution - System Binary Proxy Execution", "T1218.001-Compiled HTML File - Compiled HTML File", "T1218.002-Control Panel - Control Panel", "T1218.003-CMSTP - CMSTP", "T1218.004-InstallUtil - InstallUtil", "T1218.005-<PERSON><PERSON> - <PERSON><PERSON>", "T1218.007-<PERSON><PERSON><PERSON><PERSON> - Msiexe<PERSON>", "T1218.008-Odbcconf - Odbcconf", "T1218.009-Regsvcs/Regasm - Regsvcs/Regasm", "T1218.010-Regsvr32 - Regsvr32", "T1218.011-Rundll32 - Rundll32", "T1218.012-Verclsid - Verclsid", "T1218.013-Mavinject - Mavinject", "T1218.014-MMC - MMC", "T1218.015-Electron Applications - Electron Applications", "T1220-XSL Script Processing - XSL Script Processing", "T1221-Template Injection - Template Injection", "T1222-File and Directory Permissions Modification - File and Directory Permissions Modification", "T1222.001-Windows File and Directory Permissions Modification - Windows File and Directory Permissions Modification", "T1222.002-Linux and Mac File and Directory Permissions Modification - Linux and Mac File and Directory Permissions Modification", "T1480-Execution Guardrails - Execution Guardrails", "T1480.001-Environmental Keying - Environmental Keying", "T1480.002-Mutual Exclusion - Mutual Exclusion", "T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification", "T1484.001-Group Policy Modification - Group Policy Modification", "T1484.002-Trust Modification - Trust Modification", "T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1497.001-System Checks - System Checks", "T1497.002-User Activity Based Checks - User Activity Based Checks", "T1497.003-Time Based Evasion - Time Based Evasion", "T1535-Unused/Unsupported Cloud Regions - Unused/Unsupported Cloud Regions", "T1542-Pre-OS Boot - Pre-OS Boot", "T1542.001-System Firmware - System Firmware", "T1542.002-Component Firmware - Component Firmware", "T1542.003-Bootkit - Bootkit", "T1542.004-ROMMONkit - ROMMONkit", "T1542.005-TFTP Boot - TFTP Boot", "T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1548.001-Setuid and Setgid - Setuid and Setgid", "T1548.002-Bypass User Account Control - Bypass User Account Control", "T1548.003-<PERSON><PERSON> and <PERSON><PERSON> - <PERSON><PERSON> and <PERSON><PERSON>", "T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt", "T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access", "T1548.006-TCC Manipulation - TCC Manipulation", "T1550-Use Alternate Authentication Material - Use Alternate Authentication Material", "T1550.001-Application Access Token - Application Access Token", "T1550.002-Pass the Hash - Pass the Hash", "T1550.003-Pass the Ticket - Pass the Ticket", "T1550.004-Web Session <PERSON><PERSON> - Web Session <PERSON>ie", "T1553-Subvert Trust Controls - Subvert Trust Controls", "T1553.001-Gatekeeper Bypass - Gatekeeper Bypass", "T1553.002-Code Signing - Code Signing", "T1553.003-SIP and Trust Provider Hijacking - SIP and Trust Provider Hijacking", "T1553.004-Install Root Certificate - Install Root Certificate", "T1553.005-Mark-of-the-Web Bypass - Mark-of-the-Web Bypass", "T1553.006-Code Signing Policy Modification - Code Signing Policy Modification", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1562-Impair Defenses - Impair Defenses", "T1562.001-Disable or Modify Tools - Disable or Modify Tools", "T1562.002-Disable Windows Event Logging - Disable Windows Event Logging", "T1562.003-Impair Command History Logging - Impair Command History Logging", "T1562.004-Disable or Modify System Firewall - Disable or Modify System Firewall", "T1562.006-Indicator Blocking - Indicator Blocking", "T1562.007-Disable or Modify Cloud Firewall - Disable or Modify Cloud Firewall", "T1562.008-Disable or Modify Cloud Logs - Disable or Modify Cloud Logs", "T1562.009-Safe Mode Boot - Safe Mode Boot", "T1562.010-Downgrade Attack - Downgrade Attack", "T1562.011-Spoof Security Alerting - Spoof Security Alerting", "T1562.012-Disable or Modify Linux Audit System - Disable or Modify Linux Audit System", "T1564-Hide Artifacts - Hide Artifacts", "T1564.001-Hidden Files and Directories - Hidden Files and Directories", "T1564.002-Hidden Users - Hidden Users", "T1564.003-Hidden Window - Hidden Window", "T1564.004-NTFS File Attributes - NTFS File Attributes", "T1564.005-Hidden File System - Hidden File System", "T1564.006-Run Virtual Instance - Run Virtual Instance", "T1564.007-V<PERSON> Stomping - VBA Stomping", "T1564.008-Email Hiding Rules - Email Hiding Rules", "T1564.009-Resource Forking - Resource Forking", "T1564.010-Process Argument Spoofing - Process Argument Spoofing", "T1564.011-Ignore Process Interrupts - Ignore Process Interrupts", "T1564.012-File/Path Exclusions - File/Path Exclusions", "T1564.013-Bind Mounts - Bind Mounts", "T1564.014-Extended Attributes - Extended Attributes", "T1574-Hijack Execution Flow - Hijack Execution Flow", "T1574.001-DLL - DLL", "T1574.004-<PERSON><PERSON><PERSON> Hijacking - <PERSON><PERSON><PERSON> Hijacking", "T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness", "T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking", "T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable", "T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking", "T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path", "T1574.010-Services File Permissions Weakness - Services File Permissions Weakness", "T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness", "T1574.012-COR_PROFILER - COR_PROFILER", "T1574.013-KernelCallbackTable - KernelCallbackTable", "T1574.014-AppDomainManager - AppDomainManager", "T1578-Modify Cloud Compute Infrastructure - Modify Cloud Compute Infrastructure", "T1578.001-Create Snapshot - Create Snapshot", "T1578.002-Create Cloud Instance - Create Cloud Instance", "T1578.003-Delete Cloud Instance - Delete Cloud Instance", "T1578.004-Revert Cloud Instance - Revert Cloud Instance", "T1578.005-Modify Cloud Compute Configurations - Modify Cloud Compute Configurations", "T1599-Network Boundary Bridging - Network Boundary Bridging", "T1599.001-Network Address Translation Traversal - Network Address Translation Traversal", "T1600-Weaken Encryption - Weaken Encryption", "T1600.001-Reduce Key Space - Reduce Key Space", "T1600.002-Disable Crypto Hardware - Disable Crypto Hardware", "T1601-Modify System Image - Modify System Image", "T1601.001-Patch System Image - Patch System Image", "T1601.002-Downgrade System Image - Downgrade System Image", "T1610-Deploy Container - Deploy Container", "T1612-Build Image on Host - Build Image on Host", "T1620-Reflective Code Loading - Reflective Code Loading", "T1622-Debugger Evasion - Debugger Evasion", "T1647-Plist File Modification - Plist File Modification", "T1656-Impersonation - Impersonation", "T1666-Modify Cloud Resource Hierarchy - Modify Cloud Resource Hierarchy", "T1672-<PERSON><PERSON> Spoofing - <PERSON>ail Spoofing"], "TA0006-Credential Access (Enterprise)": ["T1003-OS Credential Dumping - OS Credential Dumping", "T1003.001-LSASS Memory - LSASS Memory", "T1003.002-Security Account Manager - Security Account Manager", "T1003.003-NTDS - NTDS", "T1003.004-LSA Secrets - LSA Secrets", "T1003.005-Cached Domain Credentials - Cached Domain Credentials", "T1003.006-DCSync - DCSync", "T1003.007-Proc Filesystem - Proc Filesystem", "T1003.008-/etc/passwd and /etc/shadow - /etc/passwd and /etc/shadow", "T1040-Network Sniffing - Network Sniffing", "T1056-Input Capture - Input Capture", "T1056.001-Keylogging - Keylogging", "T1056.002-GUI Input Capture - GUI Input Capture", "T1056.003-Web Portal Capture - Web Portal Capture", "T1056.004-Credential API Hooking - Credential API Hooking", "T1110-Brute Force - Brute Force", "T1110.001-Password Guessing - Password Guessing", "T1110.002-Password Cracking - Password Cracking", "T1110.003-Password Spraying - Password Spraying", "T1110.004-Credential Stuffing - Credential Stuffing", "T1111-Multi-Factor Authentication Interception - Multi-Factor Authentication Interception", "T1187-Forced Authentication - Forced Authentication", "T1212-Exploitation for Credential Access - Exploitation for Credential Access", "T1528-Steal Application Access Token - Steal Application Access Token", "T1539-Steal Web Session <PERSON><PERSON> - Steal Web Session Cookie", "T1552-Unsecured Credentials - Unsecured Credentials", "T1552.001-Credentials In Files - Credentials In Files", "T1552.002-Credentials in Registry - Credentials in Registry", "T1552.003-Bash History - Bash History", "T1552.004-Private Keys - Private Keys", "T1552.005-Cloud Instance Metadata API - Cloud Instance Metadata API", "T1552.006-Group Policy Preferences - Group Policy Preferences", "T1552.007-Container API - Container API", "T1552.008-Chat Messages - Chat Messages", "T1555-Credentials from Password Stores - Credentials from Password Stores", "T1555.001-Keychain - Keychain", "T1555.002-Securityd Memory - Securityd Memory", "T1555.003-Credentials from Web Browsers - Credentials from Web Browsers", "T1555.004-Windows Credential Manager - Windows Credential Manager", "T1555.005-Password Managers - Password Managers", "T1555.006-Cloud Secrets Management Stores - Cloud Secrets Management Stores", "T1556-Modify Authentication Process - Modify Authentication Process", "T1556.001-Domain Controller Authentication - Domain Controller Authentication", "T1556.002-Password Filter DLL - Password Filter DLL", "T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules", "T1556.004-Network Device Authentication - Network Device Authentication", "T1556.005-Reversible Encryption - Reversible Encryption", "T1556.006-Multi-Factor Authentication - Multi-Factor Authentication", "T1556.007-Hybrid Identity - Hybrid Identity", "T1556.008-Network Provider DLL - Network Provider DLL", "T1556.009-Conditional Access Policies - Conditional Access Policies", "T1557-Adversary-in-the-Middle - Adversary-in-the-Middle", "T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay", "T1557.002-ARP <PERSON><PERSON> Poisoning - ARP Cache Poisoning", "T1557.003-DHCP Spoofing - DHCP Spoofing", "T1557.004-Evil Twin - Evil Twin", "T1558-Steal or Forge Kerberos Tickets - Steal or Forge Kerberos Tickets", "T1558.001-Golden Ticket - Golden Ticket", "T1558.002-Silver Ticket - Silver Ticket", "T1558.003-Kerberoasting - Kerberoasting", "T1558.004-AS-REP Roasting - AS-REP Roasting", "T1558.005-Ccache Files - Ccache Files", "T1606-Forge Web Credentials - Forge Web Credentials", "T1606.001-Web Cookies - Web Cookies", "T1606.002-SAML Tokens - SAML Tokens", "T1621-Multi-Factor Authentication Request Generation - Multi-Factor Authentication Request Generation", "T1649-Steal or Forge Authentication Certificates - Steal or Forge Authentication Certificates"], "TA0007-Discovery (Enterprise)": ["T1007-System Service Discovery - System Service Discovery", "T1010-Application Window Discovery - Application Window Discovery", "T1012-Query Registry - Query Registry", "T1016-System Network Configuration Discovery - System Network Configuration Discovery", "T1016.001-Internet Connection Discovery - Internet Connection Discovery", "T1016.002-Wi-Fi Discovery - Wi-Fi Discovery", "T1018-Remote System Discovery - Remote System Discovery", "T1033-System Owner/User Discovery - System Owner/User Discovery", "T1040-Network Sniffing - Network Sniffing", "T1046-Network Service Discovery - Network Service Discovery", "T1049-System Network Connections Discovery - System Network Connections Discovery", "T1057-Process Discovery - Process Discovery", "T1069-Permission Groups Discovery - Permission Groups Discovery", "T1069.001-Local Groups - Local Groups", "T1069.002-Domain Groups - Domain Groups", "T1069.003-Cloud Groups - Cloud Groups", "T1082-System Information Discovery - System Information Discovery", "T1083-File and Directory Discovery - File and Directory Discovery", "T1087-Account <PERSON> - Account Discovery", "T1087.001-Local Account - Local Account", "T1087.002-Domain Account - Domain Account", "T1087.003-<PERSON><PERSON> Account - <PERSON><PERSON> Account", "T1087.004-<PERSON> Account - Cloud Account", "T1120-Peripheral Device Discovery - Peripheral Device Discovery", "T1124-System Time Discovery - System Time Discovery", "T1135-Network Share Discovery - Network Share Discovery", "T1201-Password Policy Discovery - Password Policy Discovery", "T1217-Browser Information Discovery - Browser Information Discovery", "T1482-Domain Trust Discovery - Domain Trust Discovery", "T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1497.001-System Checks - System Checks", "T1497.002-User Activity Based Checks - User Activity Based Checks", "T1497.003-Time Based Evasion - Time Based Evasion", "T1518-Software Discovery - Software Discovery", "T1518.001-Security Software Discovery - Security Software Discovery", "T1526-Cloud Service Discovery - Cloud Service Discovery", "T1538-Cloud Service Dashboard - Cloud Service Dashboard", "T1580-Cloud Infrastructure Discovery - Cloud Infrastructure Discovery", "T1613-Container and Resource Discovery - Container and Resource Discovery", "T1614-System Location Discovery - System Location Discovery", "T1614.001-System Language Discovery - System Language Discovery", "T1615-Group Policy Discovery - Group Policy Discovery", "T1619-Cloud Storage Object Discovery - Cloud Storage Object Discovery", "T1622-Debugger Evasion - Debugger Evasion", "T1652-<PERSON><PERSON> Driver Discovery - <PERSON><PERSON> Driver Discovery", "T1654-Log Enumeration - Log Enumeration", "T1673-Virtual Machine Discovery - Virtual Machine Discovery"], "TA0008-Lateral Movement (Enterprise)": ["T1021-Remote Services - Remote Services", "T1021.001-Remote Desktop Protocol - Remote Desktop Protocol", "T1021.002-SMB/Windows Admin Shares - SMB/Windows Admin Shares", "T1021.003-Distributed Component Object Model - Distributed Component Object Model", "T1021.004-SSH - SSH", "T1021.005-VNC - VNC", "T1021.006-Windows Remote Management - Windows Remote Management", "T1021.007-Cloud Services - Cloud Services", "T1021.008-Direct Cloud VM Connections - Direct Cloud VM Connections", "T1072-Software Deployment Tools - Software Deployment Tools", "T1080-Taint Shared Content - Taint Shared Content", "T1091-Replication Through Removable Media - Replication Through Removable Media", "T1210-Exploitation of Remote Services - Exploitation of Remote Services", "T1534-Internal Spearphishing - Internal Spearphishing", "T1550-Use Alternate Authentication Material - Use Alternate Authentication Material", "T1550.001-Application Access Token - Application Access Token", "T1550.002-Pass the Hash - Pass the Hash", "T1550.003-Pass the Ticket - Pass the Ticket", "T1550.004-Web Session <PERSON><PERSON> - Web Session <PERSON>ie", "T1563-Remote Service Session Hijacking - Remote Service Session Hijacking", "T1563.001-SSH Hijacking - SSH Hijacking", "T1563.002-RDP Hijacking - RDP Hijacking", "T1570-Lateral Tool Transfer - Lateral Tool Transfer"], "TA0009-Collection (Enterprise)": ["T1005-Data from Local System - Data from Local System", "T1025-Data from Removable Media - Data from Removable Media", "T1039-Data from Network Shared Drive - Data from Network Shared Drive", "T1056-Input Capture - Input Capture", "T1056.001-Keylogging - Keylogging", "T1056.002-GUI Input Capture - GUI Input Capture", "T1056.003-Web Portal Capture - Web Portal Capture", "T1056.004-Credential API Hooking - Credential API Hooking", "T1074-Data Staged - Data Staged", "T1074.001-Local Data Staging - Local Data Staging", "T1074.002-Remote Data Staging - Remote Data Staging", "T1113-Screen Capture - Screen Capture", "T1114-Email Collection - Email Collection", "T1114.001-Local Email Collection - Local Email Collection", "T1114.002-Remote Email Collection - Remote Email Collection", "T1114.003-Email Forwarding Rule - Email Forwarding Rule", "T1115-Clipboard Data - Clipboard Data", "T1119-Automated Collection - Automated Collection", "T1123-Audio Capture - Audio Capture", "T1125-Video Capture - Video Capture", "T1185-<PERSON>rows<PERSON> Session Hijacking - Browser Session Hijacking", "T1213-Data from Information Repositories - Data from Information Repositories", "T1213.001-Confluence - Confluence", "T1213.002-Sharepoint - Sharepoint", "T1213.003-Code Repositories - Code Repositories", "T1213.004-Customer Relationship Management Software - Customer Relationship Management Software", "T1213.005-Messaging Applications - Messaging Applications", "T1530-Data from Cloud Storage - Data from Cloud Storage", "T1557-Adversary-in-the-Middle - Adversary-in-the-Middle", "T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay", "T1557.002-ARP <PERSON><PERSON> Poisoning - ARP Cache Poisoning", "T1557.003-DHCP Spoofing - DHCP Spoofing", "T1557.004-Evil Twin - Evil Twin", "T1560-Archive Collected Data - Archive Collected Data", "T1560.001-Archive via Utility - Archive via Utility", "T1560.002-Archive via Library - Archive via Library", "T1560.003-Archive via Custom Method - Archive via Custom Method", "T1602-Data from Configuration Repository - Data from Configuration Repository", "T1602.001-SNMP (MIB Dump) - SNMP (MIB Dump)", "T1602.002-Network Device Configuration Dump - Network Device Configuration Dump"], "TA0010-Exfiltration (Enterprise)": ["T1011-Exfiltration Over Other Network Medium - Exfiltration Over Other Network Medium", "T1011.001-Exfiltration Over Bluetooth - Exfiltration Over Bluetooth", "T1020-Automated Exfiltration - Automated Exfiltration", "T1020.001-Traffic Duplication - Traffic Duplication", "T1029-Scheduled Transfer - Scheduled Transfer", "T1030-Data Transfer Size Limits - Data Transfer Size Limits", "T1041-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel", "T1048-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol", "T1048.001-Exfiltration Over Symmetric Encrypted Non-C2 Protocol - Exfiltration Over Symmetric Encrypted Non-C2 Protocol", "T1048.002-Exfiltration Over Asymmetric Encrypted Non-C2 Protocol - Exfiltration Over Asymmetric Encrypted Non-C2 Protocol", "T1048.003-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol", "T1052-Exfiltration Over Physical Medium - Exfiltration Over Physical Medium", "T1052.001-Exfiltration over USB - Exfiltration over USB", "T1537-Transfer Data to Cloud Account - Transfer Data to Cloud Account", "T1567-Exfiltration Over Web Service - Exfiltration Over Web Service", "T1567.001-Exfiltration to Code Repository - Exfiltration to Code Repository", "T1567.002-Exfiltration to Cloud Storage - Exfiltration to Cloud Storage", "T1567.003-Exfiltration to Text Storage Sites - Exfiltration to Text Storage Sites", "T1567.004-Exfiltration Over Webhook - Exfiltration Over Webhook"], "TA0011-Command and Control (Enterprise)": ["T1001-Data Obfuscation - Data Obfuscation", "T1001.001-Junk Data - Junk Data", "T1001.002-Steganography - Steganography", "T1001.003-Protocol or Service Impersonation - Protocol or Service Impersonation", "T1008-Fallback Channels - Fallback Channels", "T1071-Application Layer Protocol - Application Layer Protocol", "T1071.001-Web Protocols - Web Protocols", "T1071.002-File Transfer Protocols - File Transfer Protocols", "T1071.003-Mail Protocols - Mail Protocols", "T1071.004-DNS - DNS", "T1071.005-Publish/Subscribe Protocols - Publish/Subscribe Protocols", "T1090-Proxy - Proxy", "T1090.001-Internal Proxy - Internal Proxy", "T1090.002-External Proxy - External Proxy", "T1090.003-Multi-hop Proxy - Multi-hop Proxy", "T1090.004-Domain Fronting - Domain Fronting", "T1092-Communication Through Removable Media - Communication Through Removable Media", "T1095-Non-Application Layer Protocol - Non-Application Layer Protocol", "T1102-Web Service - Web Service", "T1102.001-Dead Drop Resolver - Dead Drop Resolver", "T1102.002-Bidirectional Communication - Bidirectional Communication", "T1102.003-One-Way Communication - One-Way Communication", "T1104-Multi-Stage Channels - Multi-Stage Channels", "T1105-Ingress Tool Transfer - Ingress Tool Transfer", "T1132-Data Encoding - Data Encoding", "T1132.001-Standard Encoding - Standard Encoding", "T1132.002-Non-Standard Encoding - Non-Standard Encoding", "T1205-Traffic Signaling - Traffic Signaling", "T1205.001-Port Knocking - Port Knocking", "T1205.002-Socket Filters - Socket Filters", "T1219-Remote Access Tools - Remote Access Tools", "T1219.001-IDE Tunneling - IDE Tunneling", "T1219.002-Remote Desktop Software - Remote Desktop Software", "T1219.003-Remote Access Hardware - Remote Access Hardware", "T1568-Dynamic Resolution - Dynamic Resolution", "T1568.001-Fast Flux DNS - Fast Flux DNS", "T1568.002-Domain Generation Algorithms - Domain Generation Algorithms", "T1568.003-DNS Calculation - DNS Calculation", "T1571-Non-Standard Port - Non-Standard Port", "T1572-Protocol Tunneling - Protocol Tunneling", "T1573-Encrypted Channel - Encrypted Channel", "T1573.001-Symmetric Cryptography - Symmetric Cryptography", "T1573.002-Asymmetric Cryptography - Asymmetric Cryptography", "T1659-Content Injection - Content Injection", "T1665-Hide Infrastructure - Hide Infrastructure"], "TA0027-Initial Access (Mobile)": ["T1451-SIM Card Swap - SIM Card Swap", "T1456-Drive-By Compromise - Drive-By Compromise", "T1458-Replication Through Removable Media - Replication Through Removable Media", "T1461-Lockscreen Bypass - Lockscreen Bypass", "T1474-Supply Chain Compromise - Supply Chain Compromise", "T1474.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools", "T1474.002-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain", "T1474.003-Compromise Software Supply Chain - Compromise Software Supply Chain", "T1660-<PERSON><PERSON> - <PERSON><PERSON>", "T1661-Application Versioning - Application Versioning", "T1664-Exploitation for Initial Access - Exploitation for Initial Access"], "TA0028-Persistence (Mobile)": ["T1398-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts", "T1541-Foreground Persistence - Foreground Persistence", "T1577-Compromise Application Executable - Compromise Application Executable", "T1603-Scheduled Task/Job - Scheduled Task/Job", "T1624-Event Triggered Execution - Event Triggered Execution", "T1624.001-Broadcast Receivers - Broadcast Receivers", "T1625-Hijack Execution Flow - Hijack Execution Flow", "T1625.001-System Runtime API Hijacking - System Runtime API Hijacking", "T1645-Compromise Client Software Binary - Compromise Client Software Binary"], "TA0029-Privilege Escalation (Mobile)": ["T1404-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation", "T1626-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism", "T1626.001-Device Administrator Permissions - Device Administrator Permissions", "T1631-Process Injection - Process Injection", "T1631.001-Ptrace System Calls - Ptrace System Calls"], "TA0030-Defense Evasion (Mobile)": ["T1406-Obfuscated Files or Information - Obfuscated Files or Information", "T1406.001-Steganography - Steganography", "T1406.002-Software Packing - Software Packing", "T1407-Download New Code at Runtime - Download New Code at Runtime", "T1516-Input Injection - Input Injection", "T1541-Foreground Persistence - Foreground Persistence", "T1575-Native API - Native API", "T1604-Proxy Through Victim - Proxy Through Victim", "T1617-Hooking - Hooking", "T1627-Execution Guardrails - Execution Guardrails", "T1627.001-Geofencing - Geofencing", "T1628-Hide Artifacts - Hide Artifacts", "T1628.001-Suppress Application Icon - Suppress Application Icon", "T1628.002-User Evasion - User Evasion", "T1628.003-Conceal Multimedia Files - Conceal Multimedia Files", "T1629-Impair Defenses - Impair Defenses", "T1629.001-Prevent Application Removal - Prevent Application Removal", "T1629.002-<PERSON>ce Lockout - Device Lockout", "T1629.003-Disable or Modify Tools - Disable or Modify Tools", "T1630-Indicator <PERSON><PERSON><PERSON> on Host - Indicator <PERSON>mo<PERSON> on Host", "T1630.001-Uninstall Malicious Application - Uninstall Malicious Application", "T1630.002-File Deletion - File Deletion", "T1630.003-<PERSON><PERSON><PERSON><PERSON> Root/Jailbreak Indicators - Dis<PERSON>ise Root/Jailbreak Indicators", "T1631-Process Injection - Process Injection", "T1631.001-Ptrace System Calls - Ptrace System Calls", "T1632-Subvert Trust Controls - Subvert Trust Controls", "T1632.001-Code Signing Policy Modification - Code Signing Policy Modification", "T1633-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion", "T1633.001-System Checks - System Checks", "T1655-Masquerading - Masquerading", "T1655.001-Match Legitimate Name or Location - Match Legitimate Name or Location", "T1661-Application Versioning - Application Versioning", "T1670-Virtualization Solution - Virtualization Solution"], "TA0031-Credential Access (Mobile)": ["T1414-Clipboard Data - Clipboard Data", "T1417-Input Capture - Input Capture", "T1417.001-Keylogging - Keylogging", "T1417.002-GUI Input Capture - GUI Input Capture", "T1517-Access Notifications - Access Notifications", "T1634-Credentials from Password Store - Credentials from Password Store", "T1634.001-Keychain - Keychain", "T1635-Steal Application Access Token - Steal Application Access Token", "T1635.001-URI Hijacking - URI Hijacking"], "TA0032-Discovery (Mobile)": ["T1418-Software Discovery - Software Discovery", "T1418.001-Security Software Discovery - Security Software Discovery", "T1420-File and Directory Discovery - File and Directory Discovery", "T1421-System Network Connections Discovery - System Network Connections Discovery", "T1422-System Network Configuration Discovery - System Network Configuration Discovery", "T1422.001-Internet Connection Discovery - Internet Connection Discovery", "T1422.002-Wi-Fi Discovery - Wi-Fi Discovery", "T1423-Network Service Scanning - Network Service Scanning", "T1424-Process Discovery - Process Discovery", "T1426-System Information Discovery - System Information Discovery", "T1430-Location Tracking - Location Tracking", "T1430.001-Remote Device Management Services - Remote Device Management Services", "T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes"], "TA0033-Lateral Movement (Mobile)": ["T1428-Exploitation of Remote Services - Exploitation of Remote Services", "T1458-Replication Through Removable Media - Replication Through Removable Media"], "TA0034-Impact (Mobile)": ["T1464-Network Denial of Service - Network Denial of Service", "T1471-Data Encrypted for Impact - Data Encrypted for Impact", "T1516-Input Injection - Input Injection", "T1582-SMS Control - SMS Control", "T1616-Call Control - Call Control", "T1640-Account Access Removal - Account Access Removal", "T1641-Data Manipulation - Data Manipulation", "T1641.001-Transmitted Data Manipulation - Transmitted Data Manipulation", "T1642-Endpoint Denial of Service - Endpoint Denial of Service", "T1643-Generate Traffic from Victim - Generate Traffic from Victim", "T1662-Data Destruction - Data Destruction"], "TA0035-Collection (Mobile)": ["T1409-Stored Application Data - Stored Application Data", "T1414-Clipboard Data - Clipboard Data", "T1417-Input Capture - Input Capture", "T1417.001-Keylogging - Keylogging", "T1417.002-GUI Input Capture - GUI Input Capture", "T1429-Audio Capture - Audio Capture", "T1430-Location Tracking - Location Tracking", "T1430.001-Remote Device Management Services - Remote Device Management Services", "T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes", "T1512-Video Capture - Video Capture", "T1513-Screen Capture - Screen Capture", "T1517-Access Notifications - Access Notifications", "T1532-Archive Collected Data - Archive Collected Data", "T1533-Data from Local System - Data from Local System", "T1616-Call Control - Call Control", "T1636-Protected User Data - Protected User Data", "T1636.001-Calendar Entries - Calendar Entries", "T1636.002-Call Log - Call Log", "T1636.003-Contact List - Contact List", "T1636.004-SMS Messages - SMS Messages", "T1638-Adversary-in-the-Middle - Adversary-in-the-Middle"], "TA0036-Exfiltration (Mobile)": ["T1639-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol", "T1639.001-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol", "T1646-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel"], "TA0037-Command and Control (Mobile)": ["T1437-Application Layer Protocol - Application Layer Protocol", "T1437.001-Web Protocols - Web Protocols", "T1481-Web Service - Web Service", "T1481.001-Dead Drop Resolver - Dead Drop Resolver", "T1481.002-Bidirectional Communication - Bidirectional Communication", "T1481.003-One-Way Communication - One-Way Communication", "T1509-Non-Standard Port - Non-Standard Port", "T1521-Encrypted Channel - Encrypted Channel", "T1521.001-Symmetric Cryptography - Symmetric Cryptography", "T1521.002-Asymmetric Cryptography - Asymmetric Cryptography", "T1521.003-<PERSON><PERSON> - SSL <PERSON>nning", "T1544-Ingress Tool Transfer - Ingress Tool Transfer", "T1616-Call Control - Call Control", "T1637-Dynamic Resolution - Dynamic Resolution", "T1637.001-Domain Generation Algorithms - Domain Generation Algorithms", "T1644-Out of Band Data - Out of Band Data", "T1663-Remote Access Software - Remote Access Software"], "TA0040-Impact (Enterprise)": ["T1485-Data Destruction - Data Destruction", "T1485.001-Lifecycle-Triggered Deletion - Lifecycle-Triggered Deletion", "T1486-Data Encrypted for Impact - Data Encrypted for Impact", "T1489-Service Stop - Service Stop", "T1490-Inhibit System Recovery - Inhibit System Recovery", "T1491-Defacement - Defacement", "T1491.001-Internal Defacement - Internal Defacement", "T1491.002-External Defacement - External Defacement", "T1495-Firmware Corruption - Firmware Corruption", "T1496-Resource Hijacking - Resource Hijacking", "T1496.001-Compute Hijacking - Compute Hijacking", "T1496.002-Bandwidth Hijacking - Bandwidth Hijacking", "T1496.003-SMS Pumping - SMS Pumping", "T1496.004-Cloud Service Hijacking - Cloud Service Hijacking", "T1498-Network Denial of Service - Network Denial of Service", "T1498.001-Direct Network Flood - Direct Network Flood", "T1498.002-Reflection Amplification - Reflection Amplification", "T1499-Endpoint Denial of Service - Endpoint Denial of Service", "T1499.001-OS Exhaustion Flood - OS Exhaustion Flood", "T1499.002-Service Exhaustion Flood - Service Exhaustion Flood", "T1499.003-Application Exhaustion Flood - Application Exhaustion Flood", "T1499.004-Application or System Exploitation - Application or System Exploitation", "T1529-System Shutdown/Reboot - System Shutdown/Reboot", "T1531-Account Access Removal - Account Access Removal", "T1561-<PERSON><PERSON> Wipe - Disk Wipe", "T1561.001-Disk Content Wipe - Disk Content Wipe", "T1561.002-Disk Structure Wipe - Disk Structure Wipe", "T1565-Data Manipulation - Data Manipulation", "T1565.001-Stored Data Manipulation - Stored Data Manipulation", "T1565.002-Transmitted Data Manipulation - Transmitted Data Manipulation", "T1565.003-Runtime Data Manipulation - Runtime Data Manipulation", "T1657-Financial Theft - Financial Theft", "T1667-<PERSON><PERSON> Bombing - <PERSON><PERSON> Bombing"], "TA0041-Execution (Mobile)": ["T1575-Native API - Native API", "T1603-Scheduled Task/Job - Scheduled Task/Job", "T1623-Command and Scripting Interpreter - Command and Scripting Interpreter", "T1623.001-Unix Shell - Unix Shell", "T1658-Exploitation for Client Execution - Exploitation for Client Execution"], "TA0042-Resource Development (Enterprise)": ["T1583-Acquire Infrastructure - Acquire Infrastructure", "T1583.001-Domains - Domains", "T1583.002-DNS Server - DNS Server", "T1583.003-Virtual Private Server - Virtual Private Server", "T1583.004-Server - Server", "T1583.005-Botnet - Botnet", "T1583.006-Web Services - Web Services", "T1583.007-Serverless - Serverless", "T1583.008-Malvertising - Malvertising", "T1584-Compromise Infrastructure - Compromise Infrastructure", "T1584.001-Domains - Domains", "T1584.002-DNS Server - DNS Server", "T1584.003-Virtual Private Server - Virtual Private Server", "T1584.004-Server - Server", "T1584.005-Botnet - Botnet", "T1584.006-Web Services - Web Services", "T1584.007-Serverless - Serverless", "T1584.008-Network Devices - Network Devices", "T1585-Establish Accounts - Establish Accounts", "T1585.001-Social Media Accounts - Social Media Accounts", "T1585.002-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1585.003-<PERSON> Accounts - Cloud Accounts", "T1586-Compromise Accounts - Compromise Accounts", "T1586.001-Social Media Accounts - Social Media Accounts", "T1586.002-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T1586.003-<PERSON> Accounts - Cloud Accounts", "T1587-Develop Capabilities - Develop Capabilities", "T1587.001-Malware - Malware", "T1587.002-Code Signing Certificates - Code Signing Certificates", "T1587.003-Digital Certificates - Digital Certificates", "T1587.004-Exploits - Exploits", "T1588-Obtain Capabilities - Obtain Capabilities", "T1588.001-Malware - Malware", "T1588.002-<PERSON>l - <PERSON><PERSON>", "T1588.003-Code Signing Certificates - Code Signing Certificates", "T1588.004-Digital Certificates - Digital Certificates", "T1588.005-Exploits - Exploits", "T1588.006-Vulnerabilities - Vulnerabilities", "T1588.007-Artificial Intelligence - Artificial Intelligence", "T1608-Stage Capabilities - Stage Capabilities", "T1608.001-Upload Malware - Upload Malware", "T1608.002-Upload Tool - Upload Tool", "T1608.003-Install Digital Certificate - Install Digital Certificate", "T1608.004-Drive-by Target - Drive-by Target", "T1608.005-Link Target - Link Target", "T1608.006-<PERSON>O Poisoning - SEO Poisoning", "T1650-Acquire Access - Acquire Access"], "TA0043-Reconnaissance (Enterprise)": ["T1589-Gather Victim Identity Information - Gather Victim Identity Information", "T1589.001-Credentials - Credentials", "T1589.002-<PERSON><PERSON> Addresses - Email Addresses", "T1589.003-Employee Names - Employee Names", "T1590-Gather Victim Network Information - Gather Victim Network Information", "T1590.001-Domain Properties - Domain Properties", "T1590.002-DNS - DNS", "T1590.003-Network Trust Dependencies - Network Trust Dependencies", "T1590.004-Network Topology - Network Topology", "T1590.005-IP Addresses - IP Addresses", "T1590.006-Network Security Appliances - Network Security Appliances", "T1591-<PERSON>ather Victim Org Information - Gather Victim Org Information", "T1591.001-Determine Physical Locations - Determine Physical Locations", "T1591.002-Business Relationships - Business Relationships", "T1591.003-Identify Business Tempo - Identify Business Tempo", "T1591.004-Identify Roles - Identify Roles", "T1592-<PERSON><PERSON> Victim Host Information - Gather Victim Host Information", "T1592.001-Hardware - Hardware", "T1592.002-Software - Software", "T1592.003-Firmware - Firmware", "T1592.004-Client Configurations - Client Configurations", "T1593-Search Open Websites/Domains - Search Open Websites/Domains", "T1593.001-Social Media - Social Media", "T1593.002-Search Engines - Search Engines", "T1593.003-Code Repositories - Code Repositories", "T1594-Search Victim-Owned Websites - Search Victim-Owned Websites", "T1595-Active Scanning - Active Scanning", "T1595.001-Scanning IP Blocks - Scanning IP Blocks", "T1595.002-Vulnerability Scanning - Vulnerability Scanning", "T1595.003-Wordlist Scanning - Wordlist Scanning", "T1596-Search Open Technical Databases - Search Open Technical Databases", "T1596.001-DNS/Passive DNS - DNS/Passive DNS", "T1596.002-WHOIS - WHOIS", "T1596.003-Digital Certificates - Digital Certificates", "T1596.004-CDNs - CDNs", "T1596.005-Scan Databases - Scan Databases", "T1597-Search Closed Sources - Search Closed Sources", "T1597.001-Threat Intel Vendors - Threat Intel Vendors", "T1597.002-Purchase Technical Data - Purchase Technical Data", "T1598-Phishing for Information - Phishing for Information", "T1598.001-Spearphishing Service - Spearphishing Service", "T1598.002-Spearphishing Attachment - Spearphishing Attachment", "T1598.003-<PERSON><PERSON><PERSON><PERSON> Link - Spearphishing Link", "T1598.004-<PERSON><PERSON>phishing Voice - Spearphishing Voice"], "TA0100-Collection (ICS)": ["T0801-Monitor Process State - Monitor Process State", "T0802-Automated Collection - Automated Collection", "T0811-Data from Information Repositories - Data from Information Repositories", "T0830-Adversary-in-the-Middle - Adversary-in-the-Middle", "T0845-Program Upload - Program Upload", "T0852-Screen Capture - Screen Capture", "T0861-Point & Tag Identification - Point & Tag Identification", "T0868-Detect Operating Mode - Detect Operating Mode", "T0877-I/O Image - I/O Image", "T0887-Wireless Sniffing - Wireless Sniffing", "T0893-Data from Local System - Data from Local System"], "TA0101-Command and Control (ICS)": ["T0869-Standard Application Layer Protocol - Standard Application Layer Protocol", "T0884-Connection Proxy - Connection Proxy", "T0885-Commonly Used Port - Commonly Used Port"], "TA0102-Discovery (ICS)": ["T0840-Network Connection Enumeration - Network Connection Enumeration", "T0842-Network Sniffing - Network Sniffing", "T0846-Remote System Discovery - Remote System Discovery", "T0887-Wireless Sniffing - Wireless Sniffing", "T0888-Remote System Information Discovery - Remote System Information Discovery"], "TA0103-Evasion (ICS)": ["T0820-Exploitation for Evasion - Exploitation for Evasion", "T0849-Masquerading - Masquerading", "T0851-Rootkit - Rootkit", "T0856-Spoof Reporting Message - Spoof Reporting Message", "T0858-Change Operating Mode - Change Operating Mode", "T0872-Indicator <PERSON><PERSON><PERSON> on Host - Indicator <PERSON><PERSON><PERSON> on Host", "T0894-System Binary Proxy Execution - System Binary Proxy Execution"], "TA0104-Execution (ICS)": ["T0807-Command-Line Interface - Command-Line Interface", "T0821-Modify Controller Tasking - Modify Controller Tasking", "T0823-Graphical User Interface - Graphical User Interface", "T0834-Native API - Native API", "T0853-Scripting - Scripting", "T0858-Change Operating Mode - Change Operating Mode", "T0863-User Execution - User Execution", "T0871-Execution through API - Execution through API", "T0874-Hooking - Hooking", "T0895-Autorun Image - Autorun Image"], "TA0105-Impact (ICS)": ["T0813-Denial of Control - Denial of Control", "T0815-Denial of View - Denial of View", "T0826-Loss of Availability - Loss of Availability", "T0827-Loss of Control - Loss of Control", "T0828-Loss of Productivity and Revenue - Loss of Productivity and Revenue", "T0829-Loss of View - Loss of View", "T0831-Manipulation of Control - Manipulation of Control", "T0832-Manipulation of View - Manipulation of View", "T0837-Loss of Protection - Loss of Protection", "T0879-Damage to Property - Damage to Property", "T0880-Loss of Safety - Loss of Safety", "T0882-Theft of Operational Information - Theft of Operational Information"], "TA0106-Impair Process Control (ICS)": ["T0806-Brute Force I/O - Brute Force I/O", "T0836-Modify Parameter - Modify Parameter", "T0839-Module Firmware - Module Firmware", "T0855-Unauthorized Command Message - Unauthorized Command Message", "T0856-Spoof Reporting Message - Spoof Reporting Message"], "TA0107-Inhibit Response Function (ICS)": ["T0803-Block Command Message - Block Command Message", "T0804-Block Reporting Message - Block Reporting Message", "T0805-Block Serial COM - Block Serial COM", "T0809-Data Destruction - Data Destruction", "T0814-Denial of Service - Denial of Service", "T0816-<PERSON><PERSON>/Shutdown - <PERSON><PERSON>art/Shutdown", "T0835-Manipulate I/O Image - Manipulate I/O Image", "T0838-Modify Alarm Settings - Modify Alarm Settings", "T0851-Rootkit - Rootkit", "T0857-System Firmware - System Firmware", "T0878-Alarm Suppression - Alarm Suppression", "T0881-Service Stop - Service Stop", "T0892-Change Credential - Change Credential"], "TA0108-Initial Access (ICS)": ["T0817-Drive-by Compromise - Drive-by Compromise", "T0819-Exploit Public-Facing Application - Exploit Public-Facing Application", "T0822-External Remote Services - External Remote Services", "T0847-Replication Through Removable Media - Replication Through Removable Media", "T0848-Rogue Master - Rogue Master", "T0860-Wireless Compromise - Wireless Compromise", "T0862-Supply Chain Compromise - Supply Chain Compromise", "T0864-Transient Cyber Asset - Transient Cyber Asset", "T0865-S<PERSON>phishing Attachment - Spearphishing Attachment", "T0866-Exploitation of Remote Services - Exploitation of Remote Services", "T0883-Internet Accessible Device - Internet Accessible Device", "T0886-Remote Services - Remote Services"], "TA0109-Lateral Movement (ICS)": ["T0812-<PERSON><PERSON>ult Credentials - Default Credentials", "T0843-Program Download - Program Download", "T0859-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T0866-Exploitation of Remote Services - Exploitation of Remote Services", "T0867-Lateral Tool Transfer - Lateral Tool Transfer", "T0886-Remote Services - Remote Services", "T0891-Hardcoded Credentials - Hardcoded Credentials"], "TA0110-Persistence (ICS)": ["T0839-Module Firmware - Module Firmware", "T0857-System Firmware - System Firmware", "T0859-<PERSON><PERSON> Accounts - <PERSON><PERSON> Accounts", "T0873-Project File Infection - Project File Infection", "T0889-Modify Program - Modify Program", "T0891-Hardcoded Credentials - Hardcoded Credentials"], "TA0111-Privilege Escalation (ICS)": ["T0874-Hooking - Hooking", "T0890-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation"]}}