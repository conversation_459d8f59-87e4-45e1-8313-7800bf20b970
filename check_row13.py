#!/usr/bin/env python3
"""Check what's actually in row 13 of the Excel file"""

import openpyxl
import os

def check_row13():
    excel_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    try:
        workbook = openpyxl.load_workbook(excel_file)
        
        # Check Timeline (Master)
        if "Timeline (Master)" in workbook.sheetnames:
            sheet = workbook["Timeline (Master)"]
            print(f"📋 Timeline (Master) - Row 12 (Headers) and Row 13 (Dropdowns):")
            
            for col in range(1, 16):  # Check first 15 columns
                header_cell = sheet.cell(row=12, column=col)
                dropdown_cell = sheet.cell(row=13, column=col)
                
                header = header_cell.value if header_cell.value else ""
                dropdown = dropdown_cell.value if dropdown_cell.value else ""
                
                if header:
                    print(f"  Column {col}: '{header}' -> '{dropdown}'")
        
        # Check Evidence Management
        if "Evidence Management" in workbook.sheetnames:
            sheet = workbook["Evidence Management"]
            print(f"\n📋 Evidence Management - Row 4 (Headers) and Row 5 (Dropdowns):")
            
            for col in range(1, 16):  # Check first 15 columns
                header_cell = sheet.cell(row=4, column=col)
                dropdown_cell = sheet.cell(row=5, column=col)
                
                header = header_cell.value if header_cell.value else ""
                dropdown = dropdown_cell.value if dropdown_cell.value else ""
                
                if header:
                    print(f"  Column {col}: '{header}' -> '{dropdown}'")
                    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_row13()
