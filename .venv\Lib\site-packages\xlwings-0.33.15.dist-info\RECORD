../../Scripts/xlwings.exe,sha256=l2SlcpYAMaHdNoB1p-fGH85xT2FiDbWdLnSSUJwtPAI,108385
../../xlwings32-0.33.15.dll,sha256=p59Ad3U6kj5x2M8S7bfPI30dx8ydcvgxxGPFdAN41C0,318600
../../xlwings64-0.33.15.dll,sha256=9-IktdwW6Ce9krkCyeYWQXh1SIF8Qq3vP60TgUufmJY,373384
xlwings-0.33.15.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xlwings-0.33.15.dist-info/METADATA,sha256=oZT00C6wKk184oplsAf8muV0FvRf2G2M4uiLr3TCA-c,6463
xlwings-0.33.15.dist-info/RECORD,,
xlwings-0.33.15.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xlwings-0.33.15.dist-info/WHEEL,sha256=bs-xhrmTp6GOYHnarwiqzzaLhy3P3WRx2sA2M-7RxtA,101
xlwings-0.33.15.dist-info/entry_points.txt,sha256=2_VpcQkE_Nrmv21nvIP1DOwO4WPGwq_2U9GhMcDCfG4,45
xlwings-0.33.15.dist-info/licenses/LICENSE.txt,sha256=4w-tMFwLb2jL0uBQhfWllD3UP_gRG8p_UuKtSGq2NtA,1723
xlwings-0.33.15.dist-info/licenses/LICENSE_PRO.txt,sha256=BqCIaI_EdKtwfPha7hd72gr1LZJMJq26cCkCIRH5oAU,20714
xlwings-0.33.15.dist-info/top_level.txt,sha256=85PB2DQYEDbnmhKGW9uBspWQZQDjIzIWqdw4yLp78As,8
xlwings/__init__.py,sha256=4iA-MaKtM4X6FcsVkkMEFyCEIgwizbpsOuyQemncCio,4688
xlwings/__pycache__/__init__.cpython-313.pyc,,
xlwings/__pycache__/_win32patch.cpython-313.pyc,,
xlwings/__pycache__/_xlmac.cpython-313.pyc,,
xlwings/__pycache__/_xlwindows.cpython-313.pyc,,
xlwings/__pycache__/base_classes.cpython-313.pyc,,
xlwings/__pycache__/cli.cpython-313.pyc,,
xlwings/__pycache__/com_server.cpython-313.pyc,,
xlwings/__pycache__/constants.cpython-313.pyc,,
xlwings/__pycache__/expansion.cpython-313.pyc,,
xlwings/__pycache__/mac_dict.cpython-313.pyc,,
xlwings/__pycache__/main.cpython-313.pyc,,
xlwings/__pycache__/reports.cpython-313.pyc,,
xlwings/__pycache__/server.cpython-313.pyc,,
xlwings/__pycache__/udfs.cpython-313.pyc,,
xlwings/__pycache__/utils.cpython-313.pyc,,
xlwings/_win32patch.py,sha256=_IxTfvj663osTXh8b1S-A_N4PthS9gEWFLjpY4V0iUA,3443
xlwings/_xlmac.py,sha256=pokWA8HH2RX_2kTRm69aKjB4K-kSsUo9uIqnowdzeps,68603
xlwings/_xlwindows.py,sha256=wR3LX0QBfmvaT7ajBLZdodkLtuVjxuJhQu08IVUdgHY,71193
xlwings/addin/Dictionary.cls,sha256=eM3NtvJzo9RJA-jUySRbNi8SM7s42QPX6PfyewpZ0QA,15567
xlwings/addin/IWebAuthenticator.cls,sha256=pKtsItmupWUPnSOxM3Wvph-48Q2SQI72Z8pnLxknqTs,2177
xlwings/addin/Remote.bas,sha256=-hNogeUsBeQUC9gDI9ZLMtqCAEhO8bwK_uWrq_YtT2o,31384
xlwings/addin/WebClient.cls,sha256=iKTzqNJZttScDAqhHi4FpoEEpwe2GHzMQyIJfjAacow,25616
xlwings/addin/WebHelpers.bas,sha256=JMLwnfkGGHyt27YpaguOkkJdX31U7nqmzSd7zxTxCzU,117213
xlwings/addin/WebRequest.cls,sha256=0hXMNbbZJAQZ6SaeSiH493PdAqIZaqEjxyneJ0tp6ys,24925
xlwings/addin/WebResponse.cls,sha256=AZ_rorZovyhfwr2_YYqyILkFh7umcz1N59IBg5DIxW4,15746
xlwings/addin/xlwings.xlam,sha256=SS89tvsO_33Ibo0gdlkBpgB2ADLqHAABF0nMOyzuaaU,273101
xlwings/base_classes.py,sha256=JpSHYPCm4_DcTDQhEj1Z5UzMao9qcF7Oym88zXUtQmQ,23926
xlwings/cli.py,sha256=QGdOY5y9j28P4Z2cfVUUQxV95G7yYRQOMjgtFVELR1c,48972
xlwings/com_server.py,sha256=MnbRBar6-fKKIVWpgyG5HFJk0NQNm0MUiRsLZvsRyHI,10926
xlwings/constants.py,sha256=4tpQ2bmZK2dQWkcHs1Meg6WPWdpO-J5_ocjUs3V9MME,149742
xlwings/conversion/__init__.py,sha256=zUWifRPv0NUEiUrg8YKfA14aQY-NYQR6iFLsK9_glFU,2981
xlwings/conversion/__pycache__/__init__.cpython-313.pyc,,
xlwings/conversion/__pycache__/framework.cpython-313.pyc,,
xlwings/conversion/__pycache__/numpy_conv.cpython-313.pyc,,
xlwings/conversion/__pycache__/pandas_conv.cpython-313.pyc,,
xlwings/conversion/__pycache__/polars_conv.cpython-313.pyc,,
xlwings/conversion/__pycache__/standard.cpython-313.pyc,,
xlwings/conversion/framework.py,sha256=G0sN0ybWJt_k59SEFzs5Z6zHc4mboHBZ5Gega2935c8,4379
xlwings/conversion/numpy_conv.py,sha256=U4NDKMKFpPWBX0uJNYwJufJYmJZp_HqNk40rVaJca6s,957
xlwings/conversion/pandas_conv.py,sha256=mq11UdWNwuFtb-3K-Hl9TZVp2JQjfNziDWszL1cGntk,6920
xlwings/conversion/polars_conv.py,sha256=2_Eu6Zlca4uh5CJdAei2E0Syfz77adqQgclnhgH5rxM,4570
xlwings/conversion/standard.py,sha256=HDgCf4-Tl3VB4MjuyvNOENYvHSKCnIaW9O3VtKjfS1Q,9494
xlwings/expansion.py,sha256=JQF9pt8owL34mvM5J5IKmiJ0VBi4ip-6B18HbmSS44M,2286
xlwings/ext/__init__.py,sha256=B8qfUbclPgJv5JMJwLFmWGgz5ueGmksVZ6oRMvIDZzk,69
xlwings/ext/__pycache__/__init__.cpython-313.pyc,,
xlwings/ext/__pycache__/sql.cpython-313.pyc,,
xlwings/ext/sql.py,sha256=KZ6i_qf5VcDf5oCM9WGJ-1iSs5S4bJkZj1Rxz7S5A6w,3292
xlwings/html/xlwings-alert.html,sha256=jomsCEsonZnWV4VMHeub3PVwqhiL-SbFTkutZmFthzc,2654
xlwings/js/xlwings.js,sha256=HbjFcw2HxVR4CtRQan5VfiE7AWStEB-F4ujYybxDrtE,15899
xlwings/js/xlwings.ts,sha256=mLqPEcJTAbfQEJlO7xKuTQgAVlMbpfsr5rqdED8C_7w,22609
xlwings/mac_dict.py,sha256=4VxVnLzU66tdVjemqFLp8MJALNA2_vfR7ux2N4syEA8,264939
xlwings/main.py,sha256=xEg2qGvA5ib-jIsdrCgkbDyfqfeZLdNpwZb8Z5Pcjkc,156778
xlwings/pro/__init__.py,sha256=6QFDF_OGJovUcuEyIV7M5S42YRzaNjJhZDCogaGwkCI,1087
xlwings/pro/__pycache__/__init__.cpython-313.pyc,,
xlwings/pro/__pycache__/_xlcalamine.cpython-313.pyc,,
xlwings/pro/__pycache__/_xlofficejs.cpython-313.pyc,,
xlwings/pro/__pycache__/_xlremote.cpython-313.pyc,,
xlwings/pro/__pycache__/embedded_code.cpython-313.pyc,,
xlwings/pro/__pycache__/udfs_officejs.cpython-313.pyc,,
xlwings/pro/__pycache__/utils.cpython-313.pyc,,
xlwings/pro/_xlcalamine.py,sha256=M78pm1sQngdk-EhZbg03sQ0j3poGP9Vrrchuctt4LlA,15851
xlwings/pro/_xlofficejs.py,sha256=ZgWjobuccHBZBLtrMZ3DNzp15TfQTyA5YjF8qnzeoGM,4406
xlwings/pro/_xlremote.py,sha256=vbmcDQOwvwt1XmxpERMW6XSZGjzWxDOs9AvngvOHkzg,43721
xlwings/pro/embedded_code.py,sha256=KUOsBM9STkmdqMstOk36rPnavRMNtSXxbXNfE-jg5RY,2005
xlwings/pro/reports/__init__.py,sha256=dDZ4ibKea-dYTeJ_PzJCCwUul8dNWl5dFCN7ELFLM00,998
xlwings/pro/reports/__pycache__/__init__.cpython-313.pyc,,
xlwings/pro/reports/__pycache__/filters.cpython-313.pyc,,
xlwings/pro/reports/__pycache__/image.cpython-313.pyc,,
xlwings/pro/reports/__pycache__/main.cpython-313.pyc,,
xlwings/pro/reports/__pycache__/markdown.cpython-313.pyc,,
xlwings/pro/reports/__pycache__/pdf.cpython-313.pyc,,
xlwings/pro/reports/filters.py,sha256=klnyJ21no7_e5WGpD40zN3Z37mkOOVjWK-GR7WaZ8hw,9388
xlwings/pro/reports/image.py,sha256=DbyLt__IJsHiCvkdXcfUFEllJ6DfgQILij1mOJdKO1Q,762
xlwings/pro/reports/main.py,sha256=0qjPJ4g5kvY2JzlfkHD9elIsdoQ7lbuY-iB9ELpnL_I,20385
xlwings/pro/reports/markdown.py,sha256=ashPkStOhNWgLJ7xQEt_iOROT0eS8RfG8pTT03vszAs,9180
xlwings/pro/reports/pdf.py,sha256=DSOaY0pCZsVMrvZ3Yf-OSg6pKbBj3GSvPW3JT5lQGs0,1871
xlwings/pro/udfs_officejs.py,sha256=Y1dtpQ2rdPJ1SuLITfnLy5sBRvI5ucC46alZvmcLiKM,22441
xlwings/pro/utils.py,sha256=uS2R6ljCJag0juBwBDIhAM55xrpNxgaYD-Q5hMmIqz0,7657
xlwings/quickstart.xlsm,sha256=524PW4S5aMV90jPNpb-swbD8RtjDfe5A_Q7Op7wfR3M,14215
xlwings/quickstart_addin.xlam,sha256=nxYosvMPlCmTpF05M1Ez6ndh_OupvRRuSXz2wFRlC0g,40216
xlwings/quickstart_addin_ribbon.xlam,sha256=GvKsskU7MxHUBSVJRf5zyaOi03u3Q3xXs5NjD6gYt6E,40745
xlwings/quickstart_standalone.xlsm,sha256=rKN1ewDHdrXiAeU3pUDTNKrnaX45sObxiD81fyT_hCE,41739
xlwings/reports.py,sha256=MzsyMrovfHskWsRRdheTQfWdcZ0CsKzmDHMSHORYr1Y,266
xlwings/rest/__init__.py,sha256=Dk7m0qKA9yIswBm_66Vw1MHmiiYRmqZ3egAUIMjUjts,41
xlwings/rest/__pycache__/__init__.cpython-313.pyc,,
xlwings/rest/__pycache__/api.cpython-313.pyc,,
xlwings/rest/__pycache__/serializers.cpython-313.pyc,,
xlwings/rest/api.py,sha256=LddiwWh3HAdxJgustvjE9YcDgz1G5dgNEuDflKwamiM,12867
xlwings/rest/serializers.py,sha256=ztMzL_EgbhAb3rSbfWFE2A9uyOykoZbkSoDHPeKXzGg,2885
xlwings/server.py,sha256=HHOpLYKT0UEKBZUWMHvz4SjD4cY9OBCkf63nqczdUYc,335
xlwings/udfs.py,sha256=jzG_e8Iv6PcMNyE9ks13XrZT3abEi5bMRvTLOngN7JM,29233
xlwings/utils.py,sha256=pa00cwFSZuFQzgKRjuo7p0J4vA3-GrfDnBU72MGJFyE,28664
xlwings/xlwings-0.33.15.applescript,sha256=OPWm78FvYEam9i4vQVdylL0s9O2aPAzkk5g7FSHwEn8,1248
xlwings/xlwings.bas,sha256=aXCUYz_SIDd5TK0WqkYuIC2Mtghk0VTXkD-Gw1FyxkY,46539
xlwings/xlwings_custom_addin.bas,sha256=hf1z00YE0af1zRolyhQN0whHR-Ntd8hXvMHWauKtOrE,45863
xlwings/xlwingslib.cp313-win_amd64.pyd,sha256=Ugs4JQeSy5-E66N6izIYXK1XjrQ5wlc8MlRIht60824,1113088
