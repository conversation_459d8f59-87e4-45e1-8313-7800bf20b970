#!/usr/bin/env python3
"""Test data validation reading from Excel"""

import openpyxl
import os
import sys
sys.path.append('.')

from kanvas import MainApp

def test_data_validation():
    print("🔍 Testing Data Validation Reading")
    print("=" * 50)
    
    excel_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    try:
        # Load workbook
        workbook = openpyxl.load_workbook(excel_file)
        print(f"📋 Available sheets: {workbook.sheetnames}")
        
        # Check if DataValidation sheet exists
        if "DataValidation" in workbook.sheetnames:
            print(f"✅ Found DataValidation sheet")
            validation_sheet = workbook["DataValidation"]
            
            # Show some data from DataValidation sheet
            print(f"\n📊 DataValidation sheet content (first 10 rows, first 5 columns):")
            for row in range(1, 11):
                row_data = []
                for col in range(1, 6):
                    cell = validation_sheet.cell(row=row, column=col)
                    row_data.append(str(cell.value) if cell.value else "")
                print(f"  Row {row}: {row_data}")
        else:
            print(f"❌ DataValidation sheet not found")
        
        # Test Timeline (Master) data validation
        if "Timeline (Master)" in workbook.sheetnames:
            print(f"\n🔍 Testing Timeline (Master) data validation...")
            timeline_sheet = workbook["Timeline (Master)"]
            
            # Create MainApp instance to test data validation reading
            app = MainApp()
            
            # Check raw data validation rules
            print(f"🔍 Raw data validation rules:")
            if hasattr(timeline_sheet, 'data_validations') and timeline_sheet.data_validations:
                print(f"  Found {len(timeline_sheet.data_validations.dataValidation)} validation rules")
                for i, dv in enumerate(timeline_sheet.data_validations.dataValidation):
                    print(f"    Rule {i+1}: Range={dv.sqref}, Formula={dv.formula1}, Type={dv.type}")
            else:
                print(f"  No data validation rules found")

            # Test data validation reading
            dropdown_values = app.read_data_validation_dropdowns(timeline_sheet)

            print(f"\n📋 Data validation dropdowns found: {len(dropdown_values)}")
            for column, values in dropdown_values.items():
                print(f"  • {column}: {len(values)} values - {values[:3]}{'...' if len(values) > 3 else ''}")
            
            # Test full dropdown reading (including fallbacks)
            all_dropdowns = app.read_dropdown_values(timeline_sheet, 8, 15)  # Row 8, 15 columns
            
            print(f"\n📋 All dropdowns (including fallbacks): {len(all_dropdowns)}")
            for column, values in all_dropdowns.items():
                print(f"  • {column}: {len(values)} values - {values[:3]}{'...' if len(values) > 3 else ''}")
        
        # Test sheet filtering
        print(f"\n🔍 Testing sheet filtering...")
        hidden_sheets = ["DataValidation", "Data Validation", "Validation", "Config", "Settings"]
        visible_sheets = [sheet for sheet in workbook.sheetnames 
                         if not any(hidden.lower() in sheet.lower() for hidden in hidden_sheets)]
        
        print(f"📋 All sheets: {workbook.sheetnames}")
        print(f"👁️ Visible sheets: {visible_sheets}")
        print(f"🙈 Hidden sheets: {[s for s in workbook.sheetnames if s not in visible_sheets]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_validation()
