#!/usr/bin/env python3
"""
Test script to verify Excel Data Validation dropdown reading functionality.
This script will check for data validation rules in the Excel file.
"""

import sys
import os
import openpyxl
from openpyxl.worksheet.datavalidation import DataValidation

def test_data_validation():
    """Test reading Excel Data Validation rules"""
    print("🔍 Testing Excel Data Validation Reading...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        print(f"📁 Loaded workbook: {test_file}")
        print(f"📋 Available sheets: {workbook.sheetnames}")
        
        # Test each sheet for data validation
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            print(f"\n🔍 Checking sheet: {sheet_name}")
            
            # Check if sheet has data validation
            if hasattr(sheet, 'data_validations') and sheet.data_validations:
                print(f"  ✅ Found {len(sheet.data_validations)} data validation rules")
                
                for i, dv in enumerate(sheet.data_validations, 1):
                    print(f"\n  📋 Data Validation Rule #{i}:")
                    print(f"    Type: {dv.type}")
                    print(f"    Formula1: {dv.formula1}")
                    print(f"    Formula2: {dv.formula2}")
                    print(f"    Allow Blank: {dv.allowBlank}")
                    print(f"    Show Input Message: {dv.showInputMessage}")
                    print(f"    Show Error Message: {dv.showErrorMessage}")
                    
                    # Show which cells this validation applies to
                    if dv.cells:
                        cell_ranges = []
                        for cell_range in dv.cells:
                            cell_ranges.append(str(cell_range))
                        print(f"    Applies to cells: {', '.join(cell_ranges)}")
                        
                        # If it's a list validation, try to parse the values
                        if dv.type == "list" and dv.formula1:
                            formula = dv.formula1.strip('"\'')
                            print(f"    Raw formula: '{formula}'")
                            
                            # Try to parse dropdown values
                            if ',' in formula:
                                values = [v.strip() for v in formula.split(',') if v.strip()]
                                print(f"    Dropdown values: {values}")
                            elif ';' in formula:
                                values = [v.strip() for v in formula.split(';') if v.strip()]
                                print(f"    Dropdown values: {values}")
                            elif formula.startswith('$') or '!' in formula:
                                print(f"    References range: {formula}")
                            else:
                                print(f"    Single value or unknown format: {formula}")
                    else:
                        print(f"    No cells specified")
            else:
                print(f"  ⚠️  No data validation rules found")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_columns():
    """Test specific columns mentioned by user: Column D (Artifact) and Column I (Mitre Technique)"""
    print("\n🎯 Testing Specific Columns (D: Artifact, I: Mitre Technique)...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        # Check Timeline (Master) sheet specifically
        if "Timeline (Master)" in workbook.sheetnames:
            sheet = workbook["Timeline (Master)"]
            print(f"\n📋 Timeline (Master) Sheet Analysis:")
            
            # Check headers in row 12
            print(f"  📍 Headers (Row 12):")
            for col in range(1, min(sheet.max_column + 1, 15)):
                header = sheet.cell(row=12, column=col).value
                col_letter = openpyxl.utils.get_column_letter(col)
                if header:
                    print(f"    {col_letter}{12}: {header}")
            
            # Check for data validation in columns D and I
            target_columns = ['D', 'I']
            
            if hasattr(sheet, 'data_validations') and sheet.data_validations:
                print(f"\n  🔍 Data Validation Analysis:")
                
                for dv in sheet.data_validations:
                    if dv.type == "list" and dv.cells:
                        for cell_range in dv.cells:
                            for cell in cell_range:
                                col_letter = cell.column_letter
                                if col_letter in target_columns:
                                    # Get header for this column
                                    header = sheet.cell(row=12, column=cell.column).value
                                    print(f"    ✅ Found dropdown in column {col_letter} ({header}):")
                                    print(f"      Formula: {dv.formula1}")
                                    
                                    # Try to parse values
                                    if dv.formula1:
                                        formula = dv.formula1.strip('"\'')
                                        if ',' in formula:
                                            values = [v.strip() for v in formula.split(',') if v.strip()]
                                            print(f"      Values: {values}")
                                        elif formula.startswith('$') or '!' in formula:
                                            print(f"      References: {formula}")
                                        else:
                                            print(f"      Raw: {formula}")
            else:
                print(f"  ⚠️  No data validation found in Timeline (Master) sheet")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_all_sheets_columns():
    """Test all sheets to find where data validation exists"""
    print("\n🔍 Comprehensive Data Validation Search...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        total_validations = 0
        
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            
            if hasattr(sheet, 'data_validations') and sheet.data_validations:
                sheet_validations = len(sheet.data_validations)
                total_validations += sheet_validations
                
                print(f"\n📋 {sheet_name}: {sheet_validations} validation rules")
                
                for i, dv in enumerate(sheet.data_validations, 1):
                    if dv.type == "list":
                        print(f"  Rule {i}: List validation")
                        if dv.cells:
                            cell_list = []
                            for cell_range in dv.cells:
                                for cell in cell_range:
                                    cell_list.append(f"{cell.column_letter}{cell.row}")
                            print(f"    Cells: {', '.join(cell_list[:10])}{'...' if len(cell_list) > 10 else ''}")
                        if dv.formula1:
                            formula = str(dv.formula1)[:100]  # Truncate long formulas
                            print(f"    Formula: {formula}{'...' if len(str(dv.formula1)) > 100 else ''}")
        
        print(f"\n📊 Total data validation rules found: {total_validations}")
        
        if total_validations == 0:
            print("⚠️  No data validation rules found in any sheet.")
            print("   This might mean:")
            print("   1. The Excel file doesn't use Data Validation dropdowns")
            print("   2. The dropdowns are created using a different method")
            print("   3. The file format doesn't preserve data validation")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Excel Data Validation Test Session...\n")
    
    success1 = test_data_validation()
    success2 = test_specific_columns()
    success3 = test_all_sheets_columns()
    
    if success1 and success2 and success3:
        print("\n🎉 Data validation testing completed!")
    else:
        print("\n💥 Some tests encountered errors.")
