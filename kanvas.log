2025-07-09 10:12:25,324 - __main__ - INFO - No settings found in database
2025-07-09 10:12:29,341 - __main__ - INFO - No settings found in database
2025-07-09 10:12:29,370 - helper.lookup_ransomware - INFO - Closing Ransomware Knowledge Base window
2025-07-09 10:14:19,864 - helper.bookmarks - INFO - Retrieved 0 groups: []
2025-07-09 10:14:23,571 - helper.bookmarks - INFO - Retrieved 0 bookmarks for display
2025-07-09 10:14:37,520 - helper.bookmarks - INFO - Retrieved 0 groups: []
2025-07-09 10:14:37,523 - helper.bookmarks - INFO - Retrieved 0 bookmarks for display
2025-07-09 10:15:52,557 - helper.download_updates - ERROR - File dan.txt not found.
2025-07-09 10:16:02,871 - helper.download_updates - INFO - Database updated successfully.
2025-07-09 10:16:02,876 - helper.download_updates - INFO - Deleted file: alireza-rezaee.csv
2025-07-09 10:16:02,878 - helper.download_updates - INFO - Deleted file: torproject.txt
2025-07-09 10:16:02,881 - helper.download_updates - INFO - Deleted file: d3fend-full-mappings.csv
2025-07-09 10:16:02,882 - helper.download_updates - INFO - Deleted file: user.json
2025-07-09 10:16:02,883 - helper.download_updates - INFO - Deleted file: admin.json
2025-07-09 10:16:02,885 - helper.download_updates - INFO - Deleted file: thirdparty.json
2025-07-09 10:16:02,888 - helper.download_updates - INFO - Deleted file: us-govt.json
2025-07-09 10:16:02,889 - helper.download_updates - INFO - Deleted file: china.json
2025-07-09 10:16:02,890 - helper.download_updates - INFO - Deleted file: edu.json
2025-07-09 10:16:02,892 - helper.download_updates - INFO - Deleted file: licensing.json
2025-07-09 10:16:02,893 - helper.download_updates - INFO - Deleted file: training.json
2025-07-09 10:16:02,895 - helper.download_updates - INFO - Deleted file: evtx_id.csv
2025-07-09 10:16:02,896 - helper.download_updates - INFO - Deleted file: mitre_techniques.csv
2025-07-09 10:16:02,898 - helper.download_updates - INFO - Deleted file: known_exploited_vulnerabilities.csv
2025-07-09 10:16:02,899 - helper.download_updates - INFO - Deleted file: MicrosoftApps.csv
2025-07-09 10:16:02,901 - helper.download_updates - INFO - Deleted file: GraphAppRoles.csv
2025-07-09 10:16:02,903 - helper.download_updates - INFO - Deleted file: GraphDelegateRoles.csv
2025-07-09 10:16:02,905 - helper.download_updates - INFO - Deleted file: Malicious_EntraID.csv
2025-07-09 10:16:02,906 - helper.download_updates - INFO - Deleted file: onetracker.csv
2025-07-09 10:16:02,907 - helper.download_updates - INFO - Deleted file: evidencetype.csv
2025-07-09 10:16:02,911 - helper.download_updates - INFO - Deleted file: secureupdates.txt
2025-07-09 10:24:56,490 - __main__ - INFO - No settings found in database
2025-07-09 10:25:00,671 - __main__ - INFO - No settings found in database
2025-07-09 10:25:00,879 - __main__ - INFO - No settings found in database
2025-07-09 10:25:57,274 - helper.bookmarks - INFO - Retrieved 16 groups: ['Attack Surface Discovery Tools', 'Cloud Forensics Tools', 'Filesystem Forensics Tools', 'Living Off The Land (LOTL)', 'Log Forensics Tools', 'Malware Sandbox Tools', 'Memory Forensics Tools', 'Microsoft Portals via msportals.io', 'Mobile Forenscis Tools', 'Network Forensics Tools', 'Personal', 'Privacy  and Anonymizer tools', 'Reverse Engineering Tools', 'Threat Hunting Tools', 'Threat Intelligence Tools', 'Vulnerability Intelligence Tools']
2025-07-09 10:25:59,541 - helper.bookmarks - INFO - Retrieved 19 bookmarks for display
2025-07-09 10:26:25,214 - helper.bookmarks - INFO - Retrieved 7 bookmarks for display
2025-07-09 10:26:32,233 - helper.bookmarks - INFO - Retrieved 27 bookmarks for display
2025-07-09 10:26:37,302 - helper.bookmarks - INFO - Retrieved 5 bookmarks for display
2025-07-09 10:26:42,968 - helper.bookmarks - INFO - Retrieved 7 bookmarks for display
2025-07-09 10:26:48,957 - helper.bookmarks - INFO - Retrieved 6 bookmarks for display
2025-07-09 10:26:51,415 - helper.bookmarks - INFO - Retrieved 6 bookmarks for display
2025-07-09 10:26:53,741 - helper.bookmarks - INFO - Retrieved 68 bookmarks for display
2025-07-09 10:27:06,456 - helper.bookmarks - INFO - Retrieved 1 bookmarks for display
2025-07-09 10:27:08,760 - helper.bookmarks - INFO - Retrieved 10 bookmarks for display
2025-07-09 10:27:10,399 - helper.bookmarks - INFO - Retrieved 19 bookmarks for display
2025-07-09 10:27:11,788 - helper.bookmarks - INFO - Retrieved 7 bookmarks for display
2025-07-09 10:27:13,352 - helper.bookmarks - INFO - Retrieved 27 bookmarks for display
2025-07-09 10:27:17,544 - helper.bookmarks - INFO - Retrieved 6 bookmarks for display
2025-07-09 10:27:25,442 - helper.lookup_eventid - INFO - Found 15 categories: ['Logon Events', 'Account Management', 'Policy Change', 'Process Tracking', 'Object Access', 'RDP Events', 'System Events', 'Application Events', 'PowerShell Events', 'Windows Defender', 'WMI Events', 'Sysmon Events', 'Lateral Movement', 'Active Directory', 'Persistence']
2025-07-09 11:14:37,305 - __main__ - INFO - File lock released
2025-07-09 11:14:37,480 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 15:51:53,492 - __main__ - INFO - No file selected
2025-07-11 15:52:00,675 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 15:54:22,984 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 16:37:41,277 - __main__ - INFO - File lock released
2025-07-11 16:37:41,450 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 17:44:32,897 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 17:46:16,534 - __main__ - INFO - File lock released
2025-07-11 17:46:52,837 - helper.mapping_defend - WARNING - No MITRE techniques found in the Timeline sheet.
2025-07-11 17:48:42,143 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 17:49:12,017 - __main__ - INFO - File lock released
2025-07-11 17:58:18,667 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:11:58,060 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:13:08,950 - __main__ - INFO - No file selected
2025-07-11 18:13:13,850 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:13:15,695 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:13:15,701 - __main__ - INFO - Disconnected existing sheet dropdown connections
2025-07-11 18:13:15,701 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:13:15,701 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 18:13:15,702 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:13:15,713 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:13:15,713 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:13:15,716 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:13:15,716 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:13:15,716 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:13:15,716 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:13:15,716 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:13:15,716 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:13:15,726 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:13:15,734 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:13:15,734 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:13:15,747 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:13:15,747 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:13:15,747 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:13:15,747 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:13:15,747 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:13:15,747 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:13:19,898 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:13:19,899 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:13:19,899 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:13:19,900 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:13:33,635 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:13:33,635 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:13:33,636 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:13:33,636 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:13:50,846 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:13:50,866 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:13:50,866 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:13:50,868 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:13:50,868 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:13:50,868 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:13:50,868 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:13:50,869 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:13:50,869 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:13:52,913 - __main__ - INFO - Loading sheet: Evidence Management
2025-07-11 18:13:52,932 - __main__ - INFO - Updated current sheet name to: Evidence Management
2025-07-11 18:13:52,932 - __main__ - INFO - Using config for 'Evidence Management': headers=4, dropdowns=5, data_start=6
2025-07-11 18:13:52,940 - __main__ - INFO - Read headers from row 4: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'Date Assigned', 'Date Completed', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:13:52,940 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:13:52,941 - __main__ - INFO - Found dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:15:00,998 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:15:00,999 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:15:00,999 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:15:02,396 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:15:02,396 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:15:02,396 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:15:02,396 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:15:02,396 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:15:02,397 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:15:02,397 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:15:02,397 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:15:02,397 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:15:02,397 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:15:06,155 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:15:06,155 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 18:15:06,156 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 18:15:06,156 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 18:15:06,156 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:15:06,157 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:15:06,157 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:15:06,157 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:17:13,249 - __main__ - INFO - File lock released
2025-07-11 18:17:13,447 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:31:37,887 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:31:42,955 - __main__ - INFO - No file selected
2025-07-11 18:31:48,390 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:31:48,390 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:31:48,454 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-11 18:31:48,454 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:31:48,454 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 18:31:48,455 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:31:48,467 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:31:48,467 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:31:48,474 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:31:48,474 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:31:48,474 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:31:48,475 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:31:48,475 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:31:48,475 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:31:48,481 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:31:48,493 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:31:48,493 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:31:48,504 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:31:48,505 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:31:48,505 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:31:48,505 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:31:48,505 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:31:48,505 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:31:50,950 - __main__ - INFO - Add new row for 'Timeline (Master)': using header row 12, dropdown row 13
2025-07-11 18:31:50,950 - __main__ - INFO - Headers for add new row: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:31:50,950 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:31:50,950 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:31:50,950 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:31:50,950 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:31:50,950 - __main__ - INFO - Dropdown values for add new row: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:31:50,951 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:31:50,951 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:31:50,952 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:31:50,952 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:32:04,571 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:32:04,571 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:32:04,572 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:32:04,572 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:32:24,492 - viz_network - INFO - Network module accessing workbook: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:32:24,492 - viz_network - INFO - Available sheets in network module: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:32:29,949 - viz_network - ERROR - Required column not found: Event System or Source System or Source or System
2025-07-11 18:33:47,152 - helper.mapping_attack - ERROR - No MITRE Tactic column found. Available columns: [None, 'Case Management Hub Id:', None, None, None, None, None, None, None, None, None, None, None, None, None]
2025-07-11 18:34:08,944 - viz_timeline - INFO - Timeline module accessing workbook: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:34:08,944 - viz_timeline - INFO - Available sheets in timeline module: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:34:16,709 - viz_timeline - ERROR - Required columns not found in headers: ['Column 1', 'Case Management Hub Id:', 'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 7', 'Column 8', 'Column 9', 'Column 10', 'Column 11', 'Column 12', 'Column 13', 'Column 14', 'Column 15']
2025-07-11 18:34:17,818 - viz_network - INFO - Network module accessing workbook: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:34:17,819 - viz_network - INFO - Available sheets in network module: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:34:38,189 - viz_network - ERROR - Required column not found: Event System or Source System or Source or System
2025-07-11 18:34:39,860 - viz_network - INFO - Network module accessing workbook: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:34:39,860 - viz_network - INFO - Available sheets in network module: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:34:41,596 - viz_network - ERROR - Required column not found: Event System or Source System or Source or System
2025-07-11 18:34:42,590 - viz_timeline - INFO - Timeline module accessing workbook: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:34:42,590 - viz_timeline - INFO - Available sheets in timeline module: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:35:48,023 - viz_timeline - ERROR - Required columns not found in headers: ['Column 1', 'Case Management Hub Id:', 'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 7', 'Column 8', 'Column 9', 'Column 10', 'Column 11', 'Column 12', 'Column 13', 'Column 14', 'Column 15']
2025-07-11 18:35:49,688 - __main__ - INFO - File lock released
2025-07-11 18:35:49,896 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:39:57,317 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:39:57,318 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:39:57,384 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-11 18:39:57,385 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:39:57,385 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 18:39:57,386 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:39:57,397 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:39:57,398 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:39:57,399 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:39:57,399 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:39:57,399 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:39:57,400 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:39:57,400 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:39:57,400 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:39:57,412 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:39:57,421 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:39:57,422 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:39:57,440 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:39:57,440 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:39:57,440 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:39:57,441 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:39:57,441 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:39:57,441 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:40:00,073 - __main__ - INFO - Loading sheet: Evidence Management
2025-07-11 18:40:00,089 - __main__ - INFO - Updated current sheet name to: Evidence Management
2025-07-11 18:40:00,090 - __main__ - INFO - Using config for 'Evidence Management': headers=4, dropdowns=5, data_start=6
2025-07-11 18:40:00,096 - __main__ - INFO - Read headers from row 4: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:40:00,096 - __main__ - INFO - Found dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:40:02,832 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:40:02,832 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:40:02,833 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:40:02,833 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:40:02,833 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:40:03,977 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:40:03,978 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:40:03,978 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:40:03,978 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:40:03,978 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:40:03,978 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:40:03,979 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:40:03,979 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:40:03,979 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:40:03,979 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:40:04,370 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:40:04,371 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:40:04,371 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:40:04,578 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-11 18:40:04,578 - __main__ - INFO - Found dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 18:40:04,578 - __main__ - INFO - Found dropdown for 'Collection Type': ['Triage']
2025-07-11 18:40:04,578 - __main__ - INFO - Found dropdown for 'IP Address': ['*************']
2025-07-11 18:40:04,578 - __main__ - INFO - Found dropdown for 'Operating System': ['Win XP']
2025-07-11 18:40:04,578 - __main__ - INFO - Found dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 18:40:04,579 - __main__ - INFO - Found dropdown for 'System Encrypted': ['Yes']
2025-07-11 18:40:04,579 - __main__ - INFO - Found dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 18:40:04,579 - __main__ - INFO - Found dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 18:40:04,579 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 18:47:56,142 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:47:56,142 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:47:56,208 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-11 18:47:56,208 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:47:56,209 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 18:47:56,209 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:47:56,239 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:47:56,239 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:47:56,244 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:47:56,244 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:47:56,245 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:47:56,245 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:47:56,245 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:47:56,245 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:47:56,254 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:47:56,267 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:47:56,268 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:47:56,281 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:47:56,281 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:47:56,281 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:47:56,281 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:47:56,281 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:47:56,281 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:47:59,253 - __main__ - INFO - Add new row for 'Timeline (Master)': using header row 12, dropdown row 13
2025-07-11 18:47:59,253 - __main__ - INFO - Headers for add new row: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:47:59,253 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:47:59,253 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:47:59,253 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:47:59,253 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:47:59,253 - __main__ - INFO - Dropdown values for add new row: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:47:59,254 - __main__ - INFO - Checking dropdown for header 'Analyst': found 0 values
2025-07-11 18:47:59,254 - __main__ - INFO - Checking dropdown for header 'System IP Address ': found 1 values
2025-07-11 18:47:59,254 - __main__ - INFO - \u2705 Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:47:59,254 - __main__ - INFO - Checking dropdown for header 'System Name': found 1 values
2025-07-11 18:47:59,255 - __main__ - INFO - \u2705 Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:47:59,255 - __main__ - INFO - Checking dropdown for header 'Artifact': found 1 values
2025-07-11 18:47:59,255 - __main__ - INFO - \u2705 Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:47:59,255 - __main__ - INFO - Checking dropdown for header 'Date Time (UTC)': found 0 values
2025-07-11 18:47:59,255 - __main__ - INFO - Checking dropdown for header 'Event ID': found 0 values
2025-07-11 18:47:59,255 - __main__ - INFO - Checking dropdown for header 'IP': found 0 values
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Remote System (If Applicable)': found 0 values
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Account Name': found 1 values
2025-07-11 18:47:59,256 - __main__ - INFO - \u2705 Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Mitre Attack Framework': found 0 values
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Mitre Technique ID': found 0 values
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Description': found 0 values
2025-07-11 18:47:59,256 - __main__ - INFO - Checking dropdown for header 'Type': found 0 values
2025-07-11 18:47:59,257 - __main__ - INFO - Checking dropdown for header 'Filename\Path': found 0 values
2025-07-11 18:47:59,257 - __main__ - INFO - Checking dropdown for header 'Notes': found 0 values
2025-07-11 18:54:03,334 - __main__ - INFO - List Users using Timeline (Master) sheet: header row 12, data start row 14
2025-07-11 18:54:03,335 - __main__ - INFO - Found Account Name column at position 9: 'Account Name'
2025-07-11 18:54:03,335 - __main__ - INFO - Found 3 unique users in Timeline (Master) sheet
2025-07-11 18:54:17,112 - __main__ - INFO - List Systems using Evidence Management sheet: header row 4, data start row 6
2025-07-11 18:54:17,112 - __main__ - INFO - Found System/Collection Name column at position 3: 'System/Collection Name'
2025-07-11 18:54:17,112 - __main__ - INFO - Found 6 unique systems in Evidence Management sheet
2025-07-11 18:54:52,217 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:54:52,217 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:54:52,218 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:54:52,218 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:54:52,218 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:54:52,218 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:54:53,047 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:54:53,047 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:54:53,047 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:54:53,047 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:54:53,047 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:54:53,047 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:54:53,226 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:54:53,226 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:54:53,226 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:54:53,226 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:54:53,226 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:54:53,226 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:54:54,324 - __main__ - INFO - Add new row for 'Timeline (Master)': using header row 12, dropdown row 13
2025-07-11 18:54:54,324 - __main__ - INFO - Headers for add new row: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:54:54,324 - __main__ - INFO - Found dropdown for 'System IP Address ': ['************']
2025-07-11 18:54:54,324 - __main__ - INFO - Found dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:54:54,324 - __main__ - INFO - Found dropdown for 'Artifact': ['AmCache']
2025-07-11 18:54:54,324 - __main__ - INFO - Found dropdown for 'Account Name': ['Bob']
2025-07-11 18:54:54,324 - __main__ - INFO - Dropdown values for add new row: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:54:54,324 - __main__ - INFO - Checking dropdown for header 'Analyst': found 0 values
2025-07-11 18:54:54,325 - __main__ - INFO - Checking dropdown for header 'System IP Address ': found 1 values
2025-07-11 18:54:54,325 - __main__ - INFO - \u2705 Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:54:54,325 - __main__ - INFO - Checking dropdown for header 'System Name': found 1 values
2025-07-11 18:54:54,325 - __main__ - INFO - \u2705 Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'Artifact': found 1 values
2025-07-11 18:54:54,326 - __main__ - INFO - \u2705 Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'Date Time (UTC)': found 0 values
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'Event ID': found 0 values
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'IP': found 0 values
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'Remote System (If Applicable)': found 0 values
2025-07-11 18:54:54,326 - __main__ - INFO - Checking dropdown for header 'Account Name': found 1 values
2025-07-11 18:54:54,327 - __main__ - INFO - \u2705 Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Mitre Attack Framework': found 0 values
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Mitre Technique ID': found 0 values
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Description': found 0 values
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Type': found 0 values
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Filename\Path': found 0 values
2025-07-11 18:54:54,327 - __main__ - INFO - Checking dropdown for header 'Notes': found 0 values
2025-07-11 18:54:59,794 - __main__ - INFO - File lock released
2025-07-11 18:54:59,932 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 18:57:11,610 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 18:57:11,610 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:57:11,677 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-11 18:57:11,677 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 18:57:11,677 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 18:57:11,678 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:57:11,693 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:57:11,693 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:57:11,698 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:57:11,698 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:57:12,028 - __main__ - INFO - Found xlwings dropdown for 'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']
2025-07-11 18:57:12,788 - __main__ - INFO - Found 1 xlwings dropdowns
2025-07-11 18:57:12,789 - __main__ - INFO - Found dropdown values: {'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']}
2025-07-11 18:57:12,800 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 18:57:12,808 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 18:57:12,808 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 18:57:12,829 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:57:12,829 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:57:13,514 - __main__ - INFO - Found xlwings dropdown for 'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']
2025-07-11 18:57:13,974 - __main__ - INFO - Found 1 xlwings dropdowns
2025-07-11 18:57:13,975 - __main__ - INFO - Found dropdown values: {'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']}
2025-07-11 18:57:37,957 - __main__ - INFO - Add new row for 'Timeline (Master)': using header row 12, dropdown row 13
2025-07-11 18:57:37,957 - __main__ - INFO - Headers for add new row: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 18:57:37,957 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:57:38,435 - __main__ - INFO - Found xlwings dropdown for 'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']
2025-07-11 18:57:38,797 - __main__ - INFO - Found 1 xlwings dropdowns
2025-07-11 18:57:38,797 - __main__ - INFO - Dropdown values for add new row: {'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']}
2025-07-11 18:57:38,797 - __main__ - INFO - Checking dropdown for header 'Analyst': found 0 values
2025-07-11 18:57:38,797 - __main__ - INFO - Checking dropdown for header 'System IP Address ': found 0 values
2025-07-11 18:57:38,798 - __main__ - INFO - Checking dropdown for header 'System Name': found 0 values
2025-07-11 18:57:38,798 - __main__ - INFO - Checking dropdown for header 'Artifact': found 32 values
2025-07-11 18:57:38,798 - __main__ - INFO - \u2705 Using sheet dropdown for 'Artifact': ['ActivitiesCache.db', 'AmCache', 'Application EVTX', 'Autoruns,', 'Event Log', 'Firewall Log', 'IIS Logs', 'Internet Browser', 'Jumplist', 'LNK', 'Logfile', 'LSM EVTX', 'MFT', 'NTUser Registry', 'OTHER (place in Notes)', 'Powershell EVTX', 'Prefetch', 'RCM EVTX', 'RDPCore EVTX', 'Scheduled Task', 'Security EVTX', 'ShimCache', 'Software Registry', 'SrumDB', 'SUM/UAL', 'System EVTX', 'System Registry', 'USNJrnl', 'USRClass Registry', 'VPN Log', 'Windows Timeline', 'WindowsPowershell EVTX']
2025-07-11 18:57:38,798 - __main__ - INFO - Checking dropdown for header 'Date Time (UTC)': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Event ID': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'IP': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Remote System (If Applicable)': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Account Name': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Mitre Attack Framework': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Mitre Technique ID': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Description': found 0 values
2025-07-11 18:57:38,799 - __main__ - INFO - Checking dropdown for header 'Type': found 0 values
2025-07-11 18:57:38,800 - __main__ - INFO - Checking dropdown for header 'Filename\Path': found 0 values
2025-07-11 18:57:38,800 - __main__ - INFO - Checking dropdown for header 'Notes': found 0 values
2025-07-11 18:57:57,557 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:57:57,557 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:57:57,558 - __main__ - ERROR - Error reading xlwings dropdowns: 'NoneType' object has no attribute 'books'
2025-07-11 18:57:57,558 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 18:57:57,558 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 18:57:57,558 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:57:57,558 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 18:57:57,558 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 18:57:57,558 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:58:07,013 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:58:07,013 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:58:07,013 - __main__ - ERROR - Error reading xlwings dropdowns: 'NoneType' object has no attribute 'books'
2025-07-11 18:58:07,014 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 18:58:07,014 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 18:58:07,014 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:58:07,014 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 18:58:07,014 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 18:58:07,014 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:58:08,314 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:58:08,726 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:58:08,727 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:58:08,727 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 18:58:16,426 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-11 18:58:16,426 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 18:58:16,427 - __main__ - ERROR - Error reading xlwings dropdowns: 'NoneType' object has no attribute 'books'
2025-07-11 18:58:16,428 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 18:58:16,428 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 18:58:16,428 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:58:16,428 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 18:58:16,428 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 18:58:16,428 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 18:58:21,734 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 18:58:21,734 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 18:58:21,735 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 18:58:21,735 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-11 19:04:41,409 - __main__ - INFO - File lock released
2025-07-11 19:04:42,166 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-11 19:05:10,690 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-11 19:05:10,690 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 19:05:10,751 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-11 19:05:10,752 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-11 19:05:10,752 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-11 19:05:10,753 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 19:05:10,780 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 19:05:10,780 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 19:05:10,782 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 19:05:10,782 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 19:05:19,217 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:20,697 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 19:05:20,697 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 19:05:20,697 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 19:05:20,697 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 19:05:20,697 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 19:05:20,697 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 19:05:20,700 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:05:20,710 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 19:05:20,726 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 19:05:20,727 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 19:05:20,745 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 19:05:20,745 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 19:05:21,655 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:22,985 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 19:05:22,985 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 19:05:22,985 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 19:05:22,985 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 19:05:22,986 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 19:05:22,986 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 19:05:22,990 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:05:25,188 - __main__ - INFO - Loading sheet: Evidence Management
2025-07-11 19:05:25,205 - __main__ - INFO - Updated current sheet name to: Evidence Management
2025-07-11 19:05:25,206 - __main__ - INFO - Using config for 'Evidence Management': headers=4, dropdowns=5, data_start=6
2025-07-11 19:05:25,211 - __main__ - INFO - Read headers from row 4: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-11 19:05:25,211 - __main__ - INFO - Attempting to read dropdowns using xlwings from Evidence Management
2025-07-11 19:05:26,155 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:27,404 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-11 19:05:27,404 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:05:27,404 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:05:27,404 - __main__ - INFO - Found dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 19:05:27,412 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:05:29,260 - __main__ - INFO - Loading sheet: Timeline Guidance
2025-07-11 19:05:29,275 - __main__ - INFO - Updated current sheet name to: Timeline Guidance
2025-07-11 19:05:29,275 - __main__ - INFO - Using default configuration for sheet: Timeline Guidance
2025-07-11 19:05:29,275 - __main__ - INFO - Using config for 'Timeline Guidance': headers=1, dropdowns=2, data_start=2
2025-07-11 19:05:29,278 - __main__ - INFO - Read headers from row 1: ['Key Points', 'Column 2', 'Column 3', 'Column 4', 'Column 5']
2025-07-11 19:05:29,278 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline Guidance
2025-07-11 19:05:30,195 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:31,465 - __main__ - INFO - Using default configuration for sheet: Timeline Guidance
2025-07-11 19:05:31,465 - __main__ - INFO - No data validation found in sheet 'Timeline Guidance'
2025-07-11 19:05:31,465 - __main__ - INFO - Found cell dropdown for 'Key Points': ['If a column in the timeline can be filled out', 'please fill it out.']
2025-07-11 19:05:31,465 - __main__ - INFO - Found dropdown values: {'Key Points': ['If a column in the timeline can be filled out', 'please fill it out.']}
2025-07-11 19:05:31,482 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:05:34,613 - __main__ - INFO - Loading sheet: Forensic Summary - Statistics
2025-07-11 19:05:34,629 - __main__ - INFO - Updated current sheet name to: Forensic Summary - Statistics
2025-07-11 19:05:34,629 - __main__ - INFO - Using default configuration for sheet: Forensic Summary - Statistics
2025-07-11 19:05:34,629 - __main__ - INFO - Using config for 'Forensic Summary - Statistics': headers=1, dropdowns=2, data_start=2
2025-07-11 19:05:34,631 - __main__ - INFO - Read headers from row 1: ['Forensic Summary Dashboard', 'Column 2', 'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 7', 'Column 8', 'Column 9', 'Column 10', 'Column 11', 'Column 12', 'Column 13', 'Column 14', 'Column 15', 'Column 16', 'Column 17']
2025-07-11 19:05:34,631 - __main__ - INFO - Attempting to read dropdowns using xlwings from Forensic Summary - Statistics
2025-07-11 19:05:35,565 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:36,808 - __main__ - INFO - Using default configuration for sheet: Forensic Summary - Statistics
2025-07-11 19:05:36,808 - __main__ - INFO - No data validation found in sheet 'Forensic Summary - Statistics'
2025-07-11 19:05:36,813 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:05:53,162 - __main__ - INFO - Loading sheet: Evidence Management
2025-07-11 19:05:53,178 - __main__ - INFO - Updated current sheet name to: Evidence Management
2025-07-11 19:05:53,178 - __main__ - INFO - Using config for 'Evidence Management': headers=4, dropdowns=5, data_start=6
2025-07-11 19:05:53,180 - __main__ - INFO - Read headers from row 4: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-11 19:05:53,180 - __main__ - INFO - Attempting to read dropdowns using xlwings from Evidence Management
2025-07-11 19:05:55,106 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:05:56,446 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-11 19:05:56,446 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:05:56,446 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:05:56,446 - __main__ - INFO - Found dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 19:05:56,455 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:06:50,424 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:06:50,425 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 19:06:50,425 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 19:06:50,425 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 19:06:50,426 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:06:50,426 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:06:50,426 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:06:50,427 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:06:57,964 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:06:57,964 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 19:06:57,965 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 19:06:57,965 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 19:06:57,965 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:06:57,966 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:06:57,966 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:06:57,966 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:07:04,527 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:07:04,528 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 19:07:04,528 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 19:07:04,529 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 19:07:04,529 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:07:04,529 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:07:04,530 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:07:04,530 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:07:12,197 - __main__ - INFO - Highlighted 1 cells with color: RGB(47, 255, 24)
2025-07-11 19:07:13,801 - __main__ - ERROR - Error in edit_row save_changes: [Errno 13] Permission denied: 'D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm'
2025-07-11 19:07:40,977 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:07:40,978 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 19:07:40,978 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 19:07:40,978 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 19:07:40,979 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:07:40,979 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:07:40,979 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:07:40,980 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:07:47,226 - __main__ - INFO - Highlighted 1 cells with color: cleared
2025-07-11 19:07:55,232 - __main__ - INFO - Highlighted 10 cells with color: RGB(144, 238, 144)
2025-07-11 19:08:04,234 - __main__ - INFO - Highlighted 1 cells with color: cleared
2025-07-11 19:08:14,580 - __main__ - INFO - Highlighted 10 cells with color: cleared
2025-07-11 19:08:26,315 - __main__ - INFO - Highlighted 30 cells with color: RGB(139, 0, 0)
2025-07-11 19:08:33,177 - __main__ - INFO - Highlighted 30 cells with color: cleared
2025-07-11 19:08:36,715 - __main__ - INFO - Add new row for 'Evidence Management': using header row 4, dropdown row 5
2025-07-11 19:08:36,715 - __main__ - INFO - Headers for add new row: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-11 19:08:36,715 - __main__ - INFO - Attempting to read dropdowns using xlwings from Evidence Management
2025-07-11 19:08:37,683 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:08:38,891 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-11 19:08:38,891 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:08:38,891 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:08:38,892 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:08:38,892 - __main__ - INFO - Dropdown values for add new row: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes']}
2025-07-11 19:08:38,892 - __main__ - INFO - Checking dropdown for header 'System Count': found 0 values
2025-07-11 19:08:38,892 - __main__ - INFO - Checking dropdown for header 'Analyst Name': found 0 values
2025-07-11 19:08:38,892 - __main__ - INFO - Checking dropdown for header 'System/Collection Name': found 1 values
2025-07-11 19:08:38,893 - __main__ - INFO - \u2705 Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-11 19:08:38,893 - __main__ - INFO - Checking dropdown for header 'Collection Type': found 1 values
2025-07-11 19:08:38,893 - __main__ - INFO - \u2705 Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-11 19:08:38,893 - __main__ - INFO - Checking dropdown for header 'Date Uploaded': found 0 values
2025-07-11 19:08:38,893 - __main__ - INFO - Checking dropdown for header 'IP Address': found 1 values
2025-07-11 19:08:38,894 - __main__ - INFO - \u2705 Using sheet dropdown for 'IP Address': ['*************']
2025-07-11 19:08:38,894 - __main__ - INFO - Checking dropdown for header 'Operating System': found 1 values
2025-07-11 19:08:38,894 - __main__ - INFO - \u2705 Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-11 19:08:38,895 - __main__ - INFO - Checking dropdown for header 'Persistence Mechanisms': found 1 values
2025-07-11 19:08:38,895 - __main__ - INFO - \u2705 Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-11 19:08:38,895 - __main__ - INFO - Checking dropdown for header 'System Encrypted': found 1 values
2025-07-11 19:08:38,895 - __main__ - INFO - \u2705 Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-11 19:08:38,895 - __main__ - INFO - Checking dropdown for header 'System Category': found 1 values
2025-07-11 19:08:38,896 - __main__ - INFO - \u2705 Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-11 19:08:38,896 - __main__ - INFO - Checking dropdown for header 'Evidence of Access or Exfiltration': found 1 values
2025-07-11 19:08:38,896 - __main__ - INFO - \u2705 Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-11 19:09:00,052 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-11 19:09:00,070 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-11 19:09:00,070 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-11 19:09:00,074 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-11 19:09:00,074 - __main__ - INFO - Attempting to read dropdowns using xlwings from Timeline (Master)
2025-07-11 19:09:01,059 - __main__ - ERROR - Error reading xlwings dropdowns: (-**********, 'Exception occurred.', (0, 'Microsoft Excel', "Excel cannot open the file 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' because the file format or file extension is not valid. Verify that the file has not been corrupted and that the file extension matches the format of the file.", 'xlmain11.chm', 0, -**********), None)
2025-07-11 19:09:02,281 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-11 19:09:02,281 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-11 19:09:02,281 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-11 19:09:02,281 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-11 19:09:02,281 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-11 19:09:02,282 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob']}
2025-07-11 19:09:02,292 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-11 19:09:06,244 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-11 19:09:06,245 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-11 19:09:06,245 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-11 19:09:06,245 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-14 13:16:11,943 - __main__ - INFO - No file selected
2025-07-14 13:16:27,659 - __main__ - INFO - Updated workbook references. File: C:/Users/<USER>/Downloads/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 13:16:27,659 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 13:16:27,663 - __main__ - INFO - File watcher setup for: C:/Users/<USER>/Downloads/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 13:16:27,887 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-14 13:16:27,888 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 13:16:27,889 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-14 13:16:27,889 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 13:16:27,900 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 13:16:27,900 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 13:16:27,902 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 13:16:27,902 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 13:16:27,903 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 13:16:27,903 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 13:16:27,903 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 13:16:27,903 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 13:16:27,903 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 13:16:27,903 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:16:27,909 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 13:16:27,915 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 13:16:27,924 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 13:16:27,924 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 13:16:27,941 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 13:16:27,942 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 13:16:27,942 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 13:16:27,942 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 13:16:27,942 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 13:16:27,942 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 13:16:27,942 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 13:16:27,942 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:16:27,950 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 13:16:45,463 - __main__ - INFO - Loading sheet: Evidence Management
2025-07-14 13:16:45,484 - __main__ - INFO - Updated current sheet name to: Evidence Management
2025-07-14 13:16:45,484 - __main__ - INFO - Using config for 'Evidence Management': headers=4, dropdowns=5, data_start=6
2025-07-14 13:16:45,486 - __main__ - INFO - Read headers from row 4: ['System Count', 'Analyst Name', 'System/Collection Name', 'Collection Type', 'Date Uploaded', 'Date Assigned', 'Date Completed', 'IP Address', 'Operating System', 'Persistence Mechanisms', 'System Encrypted', 'System Category', 'Evidence of Access or Exfiltration']
2025-07-14 13:16:45,486 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-14 13:16:45,486 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-14 13:16:45,486 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:16:45,487 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:16:45,487 - __main__ - INFO - Added hardcoded dropdowns for Evidence Management: 9 total columns
2025-07-14 13:16:45,487 - __main__ - INFO - Found dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:16:45,504 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 13:16:55,269 - __main__ - WARNING - File no longer exists: C:/Users/<USER>/Downloads/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 13:17:02,549 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-14 13:17:02,549 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-14 13:17:02,549 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:17:02,549 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:17:02,550 - __main__ - INFO - Added hardcoded dropdowns for Evidence Management: 9 total columns
2025-07-14 13:17:02,550 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:17:04,259 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-14 13:17:04,259 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-14 13:17:04,259 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:17:04,259 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:17:04,259 - __main__ - INFO - Added hardcoded dropdowns for Evidence Management: 9 total columns
2025-07-14 13:17:04,259 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:17:04,873 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-14 13:17:04,873 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-14 13:17:04,873 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:17:04,873 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:17:04,873 - __main__ - INFO - Added hardcoded dropdowns for Evidence Management: 9 total columns
2025-07-14 13:17:04,873 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:17:05,054 - __main__ - INFO - Loading sheet 'Evidence Management' with config: headers=4, dropdowns=5, data_start=6
2025-07-14 13:17:05,055 - __main__ - INFO - Found 5 data validation rules in sheet 'Evidence Management'
2025-07-14 13:17:05,055 - __main__ - ERROR - Error reading data validation dropdowns: 'tuple' object has no attribute 'type'
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'Collection Type': ['Triage']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'IP Address': ['*************']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'Operating System': ['Win XP']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:17:05,055 - __main__ - INFO - Found cell dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:17:05,055 - __main__ - INFO - Added hardcoded dropdowns for Evidence Management: 9 total columns
2025-07-14 13:17:05,055 - __main__ - INFO - Loaded dropdown values: {'System/Collection Name': ['Chadspc'], 'Collection Type': ['Triage'], 'IP Address': ['*************'], 'Operating System': ['Win XP'], 'Persistence Mechanisms': ['Yes'], 'System Encrypted': ['Yes'], 'System Category': ['Entrypoint/Patient 0'], 'Evidence of Access or Exfiltration': ['Yes'], 'Visualize': ['Yes', 'No']}
2025-07-14 13:17:13,419 - __main__ - INFO - Using sheet dropdown for 'System/Collection Name': ['Chadspc']
2025-07-14 13:17:13,420 - __main__ - INFO - Using sheet dropdown for 'Collection Type': ['Triage']
2025-07-14 13:17:13,420 - __main__ - INFO - Using sheet dropdown for 'IP Address': ['*************']
2025-07-14 13:17:13,421 - __main__ - INFO - Using sheet dropdown for 'Operating System': ['Win XP']
2025-07-14 13:17:13,421 - __main__ - INFO - Using sheet dropdown for 'Persistence Mechanisms': ['Yes']
2025-07-14 13:17:13,421 - __main__ - INFO - Using sheet dropdown for 'System Encrypted': ['Yes']
2025-07-14 13:17:13,422 - __main__ - INFO - Using sheet dropdown for 'System Category': ['Entrypoint/Patient 0']
2025-07-14 13:17:13,422 - __main__ - INFO - Using sheet dropdown for 'Evidence of Access or Exfiltration': ['Yes']
2025-07-14 13:17:42,730 - __main__ - ERROR - Error saving edited row to Excel: [Errno 13] Permission denied: 'C:/Users/<USER>/Downloads/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm'
2025-07-14 13:19:22,746 - __main__ - INFO - File lock released
2025-07-14 13:19:23,445 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-14 14:16:08,023 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-14 14:16:25,716 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 14:16:25,716 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 14:16:25,720 - __main__ - INFO - File watcher setup for: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 14:16:25,764 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-14 14:16:25,765 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 14:16:25,765 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-14 14:16:25,766 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 14:16:25,782 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 14:16:25,782 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 14:16:25,788 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 14:16:25,789 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:16:25,789 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:25,789 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:25,789 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:25,789 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:25,789 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:16:25,789 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:16:25,795 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 14:16:25,801 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 14:16:25,814 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 14:16:25,814 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 14:16:25,823 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 14:16:25,824 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:16:25,824 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:25,824 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:25,824 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:25,824 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:25,824 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:16:25,825 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:16:25,830 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 14:16:27,930 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:27,931 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:27,931 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:27,931 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:35,882 - __main__ - INFO - Edited row and saved to Excel file
2025-07-14 14:16:41,296 - __main__ - INFO - Using sheet dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:41,296 - __main__ - INFO - Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:41,296 - __main__ - INFO - Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:41,297 - __main__ - INFO - Using sheet dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:52,807 - __main__ - INFO - Deleted row 29
2025-07-14 14:16:56,941 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-14 14:16:56,941 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:16:56,941 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:56,942 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:56,942 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:56,942 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:56,942 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:16:56,942 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:16:57,925 - __main__ - INFO - Loading sheet 'Timeline (Master)' with config: headers=12, dropdowns=13, data_start=14
2025-07-14 14:16:57,925 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:16:57,926 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:16:57,926 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:16:57,926 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:16:57,926 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:16:57,926 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:16:57,926 - __main__ - INFO - Loaded dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:38:33,219 - kanvas - INFO - MainApp destructor called, cleaning up resources...
2025-07-14 14:40:06,932 - __main__ - INFO - MainApp destructor called, cleaning up resources...
2025-07-14 14:45:10,731 - __main__ - INFO - Updated workbook references. File: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 14:45:10,732 - __main__ - INFO - Available sheets: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 14:45:10,735 - __main__ - INFO - File watcher setup for: D:/Kanvas-main/ABZ_CompanyName_IR_Project_Workbook_Template.xlsm
2025-07-14 14:45:10,784 - __main__ - INFO - No sheet dropdown connections to disconnect: 'PySide6.QtCore.QObject.receivers' called with wrong argument types:
  PySide6.QtCore.QObject.receivers(SignalInstance)
Supported signatures:
  PySide6.QtCore.QObject.receivers(signal: str, /)
2025-07-14 14:45:10,784 - __main__ - INFO - Cleared sheet dropdown. Adding sheet names: ['Evidence Management', 'Timeline Guidance', 'Timeline (Master)', 'IOCs', 'SalesforcePicklists', 'Timeline (Analyst)', 'Forensic Summary - Statistics']
2025-07-14 14:45:10,784 - __main__ - INFO - Sheet dropdown updated. Current count: 7
2025-07-14 14:45:10,785 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 14:45:10,799 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 14:45:10,799 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 14:45:10,803 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 14:45:10,803 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:45:10,803 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:45:10,803 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:45:10,803 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:45:10,803 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:45:10,803 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:45:10,803 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:45:10,808 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 14:45:10,813 - __main__ - INFO - Loading sheet: Timeline (Master)
2025-07-14 14:45:10,827 - __main__ - INFO - Updated current sheet name to: Timeline (Master)
2025-07-14 14:45:10,827 - __main__ - INFO - Using config for 'Timeline (Master)': headers=12, dropdowns=13, data_start=14
2025-07-14 14:45:10,837 - __main__ - INFO - Read headers from row 12: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 14:45:10,837 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:45:10,837 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:45:10,837 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:45:10,837 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:45:10,837 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:45:10,837 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:45:10,837 - __main__ - INFO - Found dropdown values: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:45:10,846 - __main__ - INFO - Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)
2025-07-14 14:45:14,517 - __main__ - INFO - Deleted row 17
2025-07-14 14:45:21,393 - __main__ - INFO - Deleted row 28
2025-07-14 14:45:23,822 - __main__ - INFO - Add new row for 'Timeline (Master)': using header row 12, dropdown row 13
2025-07-14 14:45:23,822 - __main__ - INFO - Headers for add new row: ['Analyst', 'System IP Address ', 'System Name', 'Artifact', 'Date Time (UTC)', 'Event ID', 'IP', 'Remote System (If Applicable)', 'Account Name', 'Mitre Attack Framework', 'Mitre Technique ID', 'Description', 'Type', 'Filename\\Path', 'Notes']
2025-07-14 14:45:23,822 - __main__ - INFO - No data validation found in sheet 'Timeline (Master)'
2025-07-14 14:45:23,822 - __main__ - INFO - Found cell dropdown for 'System IP Address ': ['************']
2025-07-14 14:45:23,823 - __main__ - INFO - Found cell dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:45:23,823 - __main__ - INFO - Found cell dropdown for 'Artifact': ['AmCache']
2025-07-14 14:45:23,823 - __main__ - INFO - Found cell dropdown for 'Account Name': ['Bob']
2025-07-14 14:45:23,823 - __main__ - INFO - Added hardcoded dropdowns for Timeline (Master): 6 total columns
2025-07-14 14:45:23,823 - __main__ - INFO - Dropdown values for add new row: {'System IP Address ': ['************'], 'System Name': ['ChrisPc'], 'Artifact': ['AmCache'], 'Account Name': ['Bob'], 'Mitre Technique': ['T1003 - OS Credential Dumping', 'T1005 - Data from Local System', 'T1012 - Query Registry', 'T1016 - System Network Configuration Discovery', 'T1018 - Remote System Discovery', 'T1021 - Remote Services', 'T1027 - Obfuscated Files or Information', 'T1033 - System Owner/User Discovery', 'T1036 - Masquerading', 'T1040 - Network Sniffing'], 'Visualize': ['Yes', 'No']}
2025-07-14 14:45:23,823 - __main__ - INFO - Checking dropdown for header 'Analyst': found 0 values
2025-07-14 14:45:23,823 - __main__ - INFO - Checking dropdown for header 'System IP Address ': found 1 values
2025-07-14 14:45:23,824 - __main__ - INFO - \u2705 Using sheet dropdown for 'System IP Address ': ['************']
2025-07-14 14:45:23,824 - __main__ - INFO - Checking dropdown for header 'System Name': found 1 values
2025-07-14 14:45:23,824 - __main__ - INFO - \u2705 Using sheet dropdown for 'System Name': ['ChrisPc']
2025-07-14 14:45:23,824 - __main__ - INFO - Checking dropdown for header 'Artifact': found 1 values
2025-07-14 14:45:23,825 - __main__ - INFO - \u2705 Using sheet dropdown for 'Artifact': ['AmCache']
2025-07-14 14:45:23,825 - __main__ - INFO - Checking dropdown for header 'Date Time (UTC)': found 0 values
2025-07-14 14:45:23,825 - __main__ - INFO - Checking dropdown for header 'Event ID': found 0 values
2025-07-14 14:45:23,825 - __main__ - INFO - Checking dropdown for header 'IP': found 0 values
2025-07-14 14:45:23,825 - __main__ - INFO - Checking dropdown for header 'Remote System (If Applicable)': found 0 values
2025-07-14 14:45:23,825 - __main__ - INFO - Checking dropdown for header 'Account Name': found 1 values
2025-07-14 14:45:23,825 - __main__ - INFO - \u2705 Using sheet dropdown for 'Account Name': ['Bob']
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Mitre Attack Framework': found 0 values
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Mitre Technique ID': found 0 values
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Description': found 0 values
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Type': found 0 values
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Filename\Path': found 0 values
2025-07-14 14:45:23,826 - __main__ - INFO - Checking dropdown for header 'Notes': found 0 values
