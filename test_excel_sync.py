#!/usr/bin/env python3
"""
Test script to verify Excel ↔ KANVAS synchronization functionality.
This script will test the file watcher and synchronization features.
"""

import sys
import os
import time
import openpyxl
from datetime import datetime

def test_excel_sync():
    """Test Excel file synchronization"""
    print("🔄 Testing Excel ↔ KANVAS Synchronization")
    print("=" * 50)
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Test 1: Check file modification detection
        print("\n📋 Test 1: File Modification Detection")
        
        # Get initial modification time
        initial_time = os.path.getmtime(test_file)
        print(f"  Initial modification time: {datetime.fromtimestamp(initial_time)}")
        
        # Wait a moment
        time.sleep(1)
        
        # Modify the file (add a comment to a cell)
        workbook = openpyxl.load_workbook(test_file)
        sheet = workbook.active
        
        # Add a test comment to track changes
        test_cell = sheet.cell(row=1, column=1)
        original_value = test_cell.value
        test_cell.value = f"Test sync {datetime.now().strftime('%H:%M:%S')}"
        
        # Save the file
        workbook.save(test_file)
        
        # Check new modification time
        new_time = os.path.getmtime(test_file)
        print(f"  New modification time: {datetime.fromtimestamp(new_time)}")
        
        if new_time > initial_time:
            print("  ✅ File modification detected successfully")
        else:
            print("  ❌ File modification not detected")
            return False
        
        # Restore original value
        workbook = openpyxl.load_workbook(test_file)
        sheet = workbook.active
        sheet.cell(row=1, column=1).value = original_value
        workbook.save(test_file)
        
        print("\n📋 Test 2: KANVAS Integration Test")
        print("  To test full synchronization:")
        print("  1. Run KANVAS: python kanvas.py")
        print("  2. Load your Excel file")
        print("  3. Open the same file in Excel")
        print("  4. Make changes in Excel and save")
        print("  5. Check if KANVAS updates automatically")
        print("  6. Make changes in KANVAS (Add New Row)")
        print("  7. Check if Excel file is updated")
        
        print("\n📋 Expected Behavior:")
        print("  ✅ Excel changes → KANVAS reloads data automatically")
        print("  ✅ KANVAS changes → Excel file is updated")
        print("  ✅ Status bar shows 'Excel file updated - data reloaded'")
        print("  ✅ No file corruption or conflicts")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_file_watcher_setup():
    """Test the file watcher setup functionality"""
    print("\n🔍 Testing File Watcher Setup")
    print("-" * 30)
    
    try:
        from PySide6.QtCore import QFileSystemWatcher
        from PySide6.QtWidgets import QApplication
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Test file watcher creation
        watcher = QFileSystemWatcher()
        print("  ✅ QFileSystemWatcher created successfully")
        
        # Test adding a file to watch
        test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
        if os.path.exists(test_file):
            watcher.addPath(test_file)
            watched_files = watcher.files()
            
            if test_file in watched_files:
                print(f"  ✅ File added to watcher: {test_file}")
            else:
                print(f"  ❌ Failed to add file to watcher")
                return False
        else:
            print(f"  ⚠️  Test file not found: {test_file}")
        
        # Test removing file from watcher
        watcher.removePaths(watcher.files())
        if not watcher.files():
            print("  ✅ File removed from watcher successfully")
        else:
            print("  ❌ Failed to remove file from watcher")
            return False
        
        return True
        
    except ImportError as e:
        print(f"  ❌ PySide6 import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ File watcher test error: {e}")
        return False

def test_sync_workflow():
    """Test the complete synchronization workflow"""
    print("\n🔄 Testing Complete Sync Workflow")
    print("-" * 35)
    
    print("  📋 Manual Test Steps:")
    print("  1. Start KANVAS application")
    print("  2. Load Excel file")
    print("  3. Note current data in KANVAS")
    print("  4. Open same Excel file in Microsoft Excel")
    print("  5. Add a new row in Excel")
    print("  6. Save Excel file (Ctrl+S)")
    print("  7. Check KANVAS - should reload automatically")
    print("  8. In KANVAS, click 'Add New Row'")
    print("  9. Fill in data and save")
    print("  10. Check Excel file - should have new row")
    
    print("\n  🎯 Success Criteria:")
    print("  ✅ KANVAS shows status message when Excel changes")
    print("  ✅ KANVAS data refreshes without manual reload")
    print("  ✅ Excel file updates when KANVAS saves")
    print("  ✅ No file corruption or 'file in use' errors")
    print("  ✅ Both applications stay synchronized")
    
    return True

def main():
    print("🚀 Excel ↔ KANVAS Synchronization Test")
    print("=" * 50)
    
    # Test 1: Basic file modification detection
    success1 = test_excel_sync()
    
    # Test 2: File watcher functionality
    success2 = test_file_watcher_setup()
    
    # Test 3: Complete workflow guide
    success3 = test_sync_workflow()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"  File Modification Detection: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  File Watcher Setup: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"  Workflow Guide: {'✅ READY' if success3 else '❌ NOT READY'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Synchronization should work.")
        print("\n🚀 Next Steps:")
        print("  1. Run KANVAS: python kanvas.py")
        print("  2. Test the manual workflow above")
        print("  3. Report any synchronization issues")
    else:
        print("\n💥 Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
