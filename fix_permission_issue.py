#!/usr/bin/env python3
"""
Quick fix script for Excel file permission issues.
This script helps diagnose and resolve file access problems.
"""

import os
import sys
import subprocess
import psutil
import time

def check_excel_processes():
    """Check if Excel is running"""
    print("🔍 Checking for running Excel processes...")
    
    excel_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and 'excel' in proc.info['name'].lower():
                excel_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if excel_processes:
        print(f"❌ Found {len(excel_processes)} Excel processes running:")
        for proc in excel_processes:
            print(f"   PID {proc.pid}: {proc.info['name']}")
        return True
    else:
        print("✅ No Excel processes found")
        return False

def check_file_permissions(file_path):
    """Check file permissions and properties"""
    print(f"\n🔍 Checking file permissions for: {file_path}")
    
    if not os.path.exists(file_path):
        print("❌ File does not exist")
        return False
    
    # Check basic permissions
    readable = os.access(file_path, os.R_OK)
    writable = os.access(file_path, os.W_OK)
    
    print(f"   Readable: {'✅' if readable else '❌'}")
    print(f"   Writable: {'✅' if writable else '❌'}")
    
    # Check file attributes on Windows
    if os.name == 'nt':
        try:
            import stat
            file_stats = os.stat(file_path)
            is_readonly = not (file_stats.st_mode & stat.S_IWRITE)
            print(f"   Read-only: {'❌' if is_readonly else '✅'}")
            
            if is_readonly:
                print("   💡 File is marked as read-only")
                return False
        except Exception as e:
            print(f"   ⚠️  Could not check file attributes: {e}")
    
    # Try to open for writing
    try:
        with open(file_path, 'r+b') as f:
            pass
        print("   ✅ File can be opened for writing")
        return True
    except PermissionError:
        print("   ❌ Permission denied when trying to open for writing")
        return False
    except Exception as e:
        print(f"   ❌ Error opening file: {e}")
        return False

def suggest_solutions(file_path):
    """Suggest solutions based on the file location and issues found"""
    print(f"\n💡 SUGGESTED SOLUTIONS:")
    
    # Check if file is in Downloads folder
    if 'downloads' in file_path.lower():
        print("1. 📁 MOVE FILE FROM DOWNLOADS:")
        print("   • Downloads folder is often protected")
        print("   • Copy file to Desktop or Documents folder")
        print("   • Try again from the new location")
    
    # Check if Excel is running
    if check_excel_processes():
        print("2. 📊 CLOSE EXCEL:")
        print("   • Close Microsoft Excel completely")
        print("   • Check Task Manager for any remaining Excel processes")
        print("   • Wait 10 seconds, then try again")
    
    print("3. 🔐 CHECK FILE PROPERTIES:")
    print("   • Right-click the Excel file")
    print("   • Select 'Properties'")
    print("   • Uncheck 'Read-only' if checked")
    print("   • Click 'OK'")
    
    print("4. 👑 RUN AS ADMINISTRATOR:")
    print("   • Right-click on Command Prompt")
    print("   • Select 'Run as administrator'")
    print("   • Navigate to KANVAS folder")
    print("   • Run: python kanvas.py")
    
    print("5. 📋 COPY FILE TO SAFE LOCATION:")
    print("   • Copy Excel file to Desktop")
    print("   • Make sure it's not read-only")
    print("   • Load the copied file in KANVAS")

def fix_readonly_attribute(file_path):
    """Try to remove read-only attribute"""
    print(f"\n🔧 Attempting to fix read-only attribute...")
    
    try:
        if os.name == 'nt':
            # Windows: use attrib command
            result = subprocess.run(['attrib', '-R', file_path], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Read-only attribute removed")
                return True
            else:
                print(f"❌ Failed to remove read-only: {result.stderr}")
                return False
        else:
            # Unix-like: use chmod
            os.chmod(file_path, 0o666)
            print("✅ File permissions updated")
            return True
    except Exception as e:
        print(f"❌ Error fixing read-only attribute: {e}")
        return False

def create_test_copy(file_path):
    """Create a test copy of the file in a safe location"""
    print(f"\n📋 Creating test copy...")
    
    try:
        import shutil
        
        # Create copy on Desktop
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        if not os.path.exists(desktop):
            desktop = os.path.expanduser("~")  # Fallback to home directory
        
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        test_copy_path = os.path.join(desktop, f"{name}_KANVAS_TEST{ext}")
        
        shutil.copy2(file_path, test_copy_path)
        
        # Remove read-only attribute from copy
        fix_readonly_attribute(test_copy_path)
        
        print(f"✅ Test copy created: {test_copy_path}")
        print("💡 Try loading this copy in KANVAS instead")
        
        return test_copy_path
        
    except Exception as e:
        print(f"❌ Error creating test copy: {e}")
        return None

def main():
    print("🚨 Excel File Permission Issue Fixer")
    print("=" * 50)
    
    # Get file path
    file_path = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        print("Usage: python fix_permission_issue.py [path_to_excel_file]")
        return
    
    print(f"📁 Target file: {file_path}")
    
    # Step 1: Check Excel processes
    excel_running = check_excel_processes()
    
    # Step 2: Check file permissions
    file_accessible = check_file_permissions(file_path)
    
    # Step 3: Suggest solutions
    suggest_solutions(file_path)
    
    # Step 4: Offer to create test copy
    if not file_accessible:
        print(f"\n❓ Would you like to create a test copy? (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes']:
                test_copy = create_test_copy(file_path)
                if test_copy:
                    print(f"\n🚀 Next steps:")
                    print(f"1. Load this file in KANVAS: {test_copy}")
                    print(f"2. Test the sync functionality")
                    print(f"3. If it works, replace original with working copy")
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
    
    print(f"\n📋 SUMMARY:")
    print(f"   Excel running: {'❌ YES (close it!)' if excel_running else '✅ NO'}")
    print(f"   File accessible: {'✅ YES' if file_accessible else '❌ NO'}")
    
    if file_accessible and not excel_running:
        print(f"\n🎉 File should be accessible! Try KANVAS again.")
    else:
        print(f"\n⚠️  Fix the issues above, then try KANVAS again.")

if __name__ == "__main__":
    main()
