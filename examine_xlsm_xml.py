#!/usr/bin/env python3
"""Examine XLSM XML structure to find corruption"""

import zipfile
import os
import xml.etree.ElementTree as ET

def examine_xlsm_xml():
    print("🔍 XLSM XML Structure Analysis")
    print("=" * 40)
    
    xlsm_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(xlsm_file):
        print(f"❌ File not found: {xlsm_file}")
        return
    
    try:
        # Open as ZIP file
        with zipfile.ZipFile(xlsm_file, 'r') as zip_file:
            print(f"✅ Successfully opened as ZIP")
            
            # List all files in the ZIP
            print(f"\n📁 Files in XLSM archive:")
            file_list = zip_file.namelist()
            for file_name in sorted(file_list):
                file_size = zip_file.getinfo(file_name).file_size
                print(f"  📄 {file_name} ({file_size} bytes)")
            
            # Check critical XML files
            critical_files = [
                "[Content_Types].xml",
                "_rels/.rels", 
                "xl/workbook.xml",
                "xl/_rels/workbook.xml.rels"
            ]
            
            print(f"\n🔍 Checking critical XML files:")
            for file_name in critical_files:
                if file_name in file_list:
                    try:
                        content = zip_file.read(file_name)
                        # Try to parse as XML
                        ET.fromstring(content)
                        print(f"  ✅ {file_name} - Valid XML")
                    except ET.ParseError as e:
                        print(f"  ❌ {file_name} - XML CORRUPTION: {e}")
                    except Exception as e:
                        print(f"  ⚠️ {file_name} - Error: {e}")
                else:
                    print(f"  ❌ {file_name} - MISSING")
            
            # Check worksheet XML files
            print(f"\n🔍 Checking worksheet XML files:")
            worksheet_files = [f for f in file_list if f.startswith("xl/worksheets/")]
            for file_name in worksheet_files:
                try:
                    content = zip_file.read(file_name)
                    ET.fromstring(content)
                    print(f"  ✅ {file_name} - Valid XML")
                except ET.ParseError as e:
                    print(f"  ❌ {file_name} - XML CORRUPTION: {e}")
                    print(f"      This is likely where the corruption is!")
                except Exception as e:
                    print(f"  ⚠️ {file_name} - Error: {e}")
            
            # Check for VBA project (macros)
            if "xl/vbaProject.bin" in file_list:
                vba_size = zip_file.getinfo("xl/vbaProject.bin").file_size
                print(f"\n✅ VBA Macros found: xl/vbaProject.bin ({vba_size} bytes)")
            else:
                print(f"\n❌ VBA Macros missing!")
                
    except zipfile.BadZipFile:
        print(f"❌ File is not a valid ZIP archive - severely corrupted")
    except Exception as e:
        print(f"❌ Error examining file: {e}")

if __name__ == "__main__":
    examine_xlsm_xml()
