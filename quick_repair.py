#!/usr/bin/env python3
"""
Quick repair for corrupted Excel file - minimal intervention approach.
"""

import openpyxl
import shutil
import os

def quick_repair():
    """Quick repair of the Excel file"""
    file_path = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    backup_path = f"{file_path}.backup"
    
    print(f"🔧 Quick repair of: {file_path}")
    
    # Check if backup exists
    if os.path.exists(backup_path):
        print(f"📋 Found backup file: {backup_path}")
        try:
            # Try to load the backup
            wb = openpyxl.load_workbook(backup_path)
            print(f"✅ Backup file loads successfully")
            print(f"📋 Sheets in backup: {wb.sheetnames}")
            
            # Save backup as the main file
            wb.save(file_path)
            print(f"✅ Restored from backup: {file_path}")
            
            # Test the restored file
            test_wb = openpyxl.load_workbook(file_path)
            print(f"✅ Restored file loads successfully")
            print(f"📋 Sheets in restored file: {test_wb.sheetnames}")
            
            return True
            
        except Exception as e:
            print(f"❌ Backup file is also corrupted: {e}")
    
    print(f"❌ No usable backup found")
    print(f"💡 Suggestions:")
    print(f"1. Check if you have the original template file")
    print(f"2. Try Excel's built-in repair: File → Open → Open and Repair")
    print(f"3. Look for auto-recovery files in Excel")
    
    return False

if __name__ == "__main__":
    quick_repair()
