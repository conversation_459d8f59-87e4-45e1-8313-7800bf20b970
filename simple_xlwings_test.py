#!/usr/bin/env python3
"""
Simple test to check if xlwings can be installed and used.
"""

import sys
import os

def test_xlwings_installation():
    """Test if xlwings can be installed and imported"""
    print("🔍 Testing xlwings installation...")
    
    try:
        import xlwings as xw
        print("✅ xlwings imported successfully")
        print(f"   Version: {xw.__version__}")
        return True
    except ImportError as e:
        print(f"❌ xlwings import failed: {e}")
        print("\n💡 To install xlwings:")
        print("   pip install xlwings")
        return False

def test_excel_availability():
    """Test if Excel is available"""
    print("\n🔍 Testing Excel availability...")
    
    try:
        import xlwings as xw
        
        # Try to create Excel app
        app = xw.App(visible=False)
        print("✅ Excel application created")
        
        # Test basic operations
        wb = app.books.add()
        ws = wb.sheets[0]
        ws.range('A1').value = "Test"
        value = ws.range('A1').value
        
        if value == "Test":
            print("✅ Excel read/write operations working")
        
        wb.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Excel not available: {e}")
        print("\n💡 Possible solutions:")
        print("   • Make sure Microsoft Excel is installed")
        print("   • Try running as administrator")
        print("   • Check if Excel is already running and close it")
        return False

def test_file_access():
    """Test if we can access the Excel file"""
    print("\n🔍 Testing file access...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"✅ Test file found: {test_file}")
    
    try:
        import xlwings as xw
        
        app = xw.App(visible=False)
        wb = app.books.open(os.path.abspath(test_file))
        
        print(f"✅ File opened successfully")
        print(f"   Sheets: {[ws.name for ws in wb.sheets]}")
        
        wb.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Could not open file: {e}")
        return False

def main():
    print("🚀 Simple xlwings Test")
    print("=" * 40)
    
    # Test 1: Installation
    if not test_xlwings_installation():
        print("\n💥 xlwings is not installed. Please install it first.")
        return
    
    # Test 2: Excel availability
    if not test_excel_availability():
        print("\n💥 Excel is not available. xlwings requires Excel to be installed.")
        return
    
    # Test 3: File access
    if not test_file_access():
        print("\n💥 Cannot access the test file.")
        return
    
    print("\n🎉 All tests passed!")
    print("✅ xlwings is ready to use for dropdown reading")

if __name__ == "__main__":
    main()
