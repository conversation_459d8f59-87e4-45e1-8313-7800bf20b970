#!/usr/bin/env python3
"""
Test script to verify that the "Add New Row" functionality works correctly
with the new sheet-specific dropdown system.
"""

import sys
import os
import openpyxl

def test_add_new_row_headers():
    """Test that headers are read correctly for add new row functionality"""
    print("🧪 Testing Add New Row Header Reading...")
    
    # Test with the actual workbook
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        print(f"📁 Loaded workbook: {test_file}")
        print(f"📋 Available sheets: {workbook.sheetnames}")
        
        # Sheet configurations (same as in kanvas.py)
        sheet_configs = {
            "Evidence Management": {
                "header_row": 4,
                "dropdown_row": 5,
                "data_start_row": 6
            },
            "Timeline (Master)": {
                "header_row": 12,
                "dropdown_row": 13,
                "data_start_row": 14
            },
            "Timeline (Analyst)": {
                "header_row": 12,
                "dropdown_row": 13,
                "data_start_row": 14
            },
            "default": {
                "header_row": 1,
                "dropdown_row": 2,
                "data_start_row": 2
            }
        }
        
        def get_sheet_config(sheet_name):
            return sheet_configs.get(sheet_name, sheet_configs["default"])
        
        # Test each configured sheet
        test_sheets = ["Evidence Management", "Timeline (Master)", "Timeline (Analyst)"]
        
        for sheet_name in test_sheets:
            if sheet_name in workbook.sheetnames:
                print(f"\n🔍 Testing '{sheet_name}' sheet:")
                
                config = get_sheet_config(sheet_name)
                sheet = workbook[sheet_name]
                
                print(f"  📍 Configuration:")
                print(f"    Header Row: {config['header_row']}")
                print(f"    Dropdown Row: {config['dropdown_row']}")
                print(f"    Data Start Row: {config['data_start_row']}")
                
                # Test header reading (same logic as in add_new_row)
                headers = []
                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=config['header_row'], column=col)
                    header_value = cell.value if cell.value else f"Column {col}"
                    headers.append(str(header_value))
                
                print(f"  📝 Headers from row {config['header_row']}:")
                for i, header in enumerate(headers[:10], 1):  # Show first 10 headers
                    print(f"    {i:2d}. {header}")
                
                if len(headers) > 10:
                    print(f"    ... and {len(headers) - 10} more headers")
                
                # Test dropdown reading
                dropdown_values = {}
                for col in range(1, min(sheet.max_column + 1, 10)):  # Test first 10 columns
                    cell = sheet.cell(row=config['dropdown_row'], column=col)
                    if cell.value:
                        header_cell = sheet.cell(row=config['header_row'], column=col)
                        header = header_cell.value if header_cell.value else f"Column {col}"
                        dropdown_values[header] = str(cell.value)
                
                if dropdown_values:
                    print(f"  🔽 Dropdown values from row {config['dropdown_row']}:")
                    for header, value in dropdown_values.items():
                        print(f"    {header}: {value}")
                else:
                    print(f"  🔽 No dropdown values found in row {config['dropdown_row']}")
                
                # Verify headers are not "None"
                none_headers = [h for h in headers if h == "None" or h == "none"]
                if none_headers:
                    print(f"  ⚠️  WARNING: Found {len(none_headers)} 'None' headers!")
                else:
                    print(f"  ✅ All headers are valid (no 'None' values)")
        
        print("\n🎉 Add New Row header testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_old_vs_new_header_reading():
    """Compare old method (row 1) vs new method (configured rows)"""
    print("\n🔄 Comparing Old vs New Header Reading Methods...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        test_sheets = ["Evidence Management", "Timeline (Master)"]
        
        for sheet_name in test_sheets:
            if sheet_name in workbook.sheetnames:
                print(f"\n📊 Comparing methods for '{sheet_name}':")
                sheet = workbook[sheet_name]
                
                # Old method (always row 1)
                old_headers = [str(cell.value) if cell.value else "None" for cell in sheet[1]]
                
                # New method (configured row)
                if sheet_name == "Evidence Management":
                    header_row = 4
                elif sheet_name.startswith("Timeline"):
                    header_row = 12
                else:
                    header_row = 1
                
                new_headers = []
                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=header_row, column=col)
                    header_value = cell.value if cell.value else f"Column {col}"
                    new_headers.append(str(header_value))
                
                print(f"  📍 Old method (row 1): {old_headers[:5]}...")
                print(f"  📍 New method (row {header_row}): {new_headers[:5]}...")
                
                # Count "None" values
                old_none_count = sum(1 for h in old_headers if h == "None")
                new_none_count = sum(1 for h in new_headers if h == "None")
                
                print(f"  📊 'None' headers - Old: {old_none_count}, New: {new_none_count}")
                
                if new_none_count < old_none_count:
                    print(f"  ✅ New method is better (fewer 'None' headers)")
                elif new_none_count == old_none_count:
                    print(f"  ➡️  Both methods equivalent")
                else:
                    print(f"  ⚠️  Old method had fewer 'None' headers")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Add New Row Functionality Tests...\n")
    
    success1 = test_add_new_row_headers()
    success2 = test_old_vs_new_header_reading()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Add New Row functionality should work correctly.")
    else:
        print("\n💥 Some tests failed. Check the issues above.")
