#!/usr/bin/env python3
"""
Test script to verify that the List Systems and List Users functions work correctly
with the updated sheet configurations.
"""

import sys
import os
import openpyxl

def test_list_systems():
    """Test the List Systems functionality"""
    print("🔍 Testing List Systems Functionality...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        print(f"📁 Loaded workbook: {test_file}")
        
        # Test Evidence Management sheet for systems
        if "Evidence Management" not in workbook.sheetnames:
            print("❌ Evidence Management sheet not found")
            return False
        
        sheet = workbook["Evidence Management"]
        
        # Get sheet configuration
        header_row = 4
        data_start_row = 6
        
        print(f"📋 Testing Evidence Management sheet:")
        print(f"  📍 Header row: {header_row}, Data start row: {data_start_row}")
        
        # Look for System/Collection Name column
        system_col = None
        for col in range(1, sheet.max_column + 1):
            header = sheet.cell(row=header_row, column=col).value
            if header and "System" in str(header) and "Collection" in str(header):
                system_col = col
                print(f"  ✅ Found System/Collection Name column at position {col}: '{header}'")
                break
        
        if system_col is None:
            print("  ❌ System/Collection Name column not found")
            return False
        
        # Collect systems
        systems = set()
        for row in range(data_start_row, sheet.max_row + 1):
            system = sheet.cell(row=row, column=system_col).value
            if system and str(system).strip():
                systems.add(str(system).strip())
        
        sorted_systems = sorted(list(systems))
        print(f"  📊 Found {len(sorted_systems)} unique systems:")
        for i, system in enumerate(sorted_systems, 1):
            print(f"    {i:2d}. {system}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_list_users():
    """Test the List Users functionality"""
    print("\n🔍 Testing List Users Functionality...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        # Test Timeline (Master) sheet for users
        if "Timeline (Master)" not in workbook.sheetnames:
            print("❌ Timeline (Master) sheet not found")
            return False
        
        sheet = workbook["Timeline (Master)"]
        
        # Get sheet configuration
        header_row = 12
        data_start_row = 14
        
        print(f"📋 Testing Timeline (Master) sheet:")
        print(f"  📍 Header row: {header_row}, Data start row: {data_start_row}")
        
        # Look for Account Name column
        account_col = None
        for col in range(1, sheet.max_column + 1):
            header = sheet.cell(row=header_row, column=col).value
            if header and "Account" in str(header) and "Name" in str(header):
                account_col = col
                print(f"  ✅ Found Account Name column at position {col}: '{header}'")
                break
        
        if account_col is None:
            print("  ❌ Account Name column not found")
            return False
        
        # Collect users
        users = set()
        for row in range(data_start_row, sheet.max_row + 1):
            user = sheet.cell(row=row, column=account_col).value
            if user and str(user).strip():
                users.add(str(user).strip())
        
        sorted_users = sorted(list(users))
        print(f"  📊 Found {len(sorted_users)} unique users:")
        for i, user in enumerate(sorted_users, 1):
            print(f"    {i:2d}. {user}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_column_detection():
    """Test column detection in both sheets"""
    print("\n🔬 Testing Column Detection...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        # Test Evidence Management headers
        if "Evidence Management" in workbook.sheetnames:
            sheet = workbook["Evidence Management"]
            print(f"\n📋 Evidence Management - Headers (Row 4):")
            
            for col in range(1, min(sheet.max_column + 1, 15)):
                header = sheet.cell(row=4, column=col).value
                if header:
                    contains_system = "System" in str(header)
                    contains_collection = "Collection" in str(header)
                    is_target = contains_system and contains_collection
                    marker = "🎯" if is_target else "  "
                    print(f"  {marker} Col {col:2d}: {header}")
        
        # Test Timeline (Master) headers
        if "Timeline (Master)" in workbook.sheetnames:
            sheet = workbook["Timeline (Master)"]
            print(f"\n📋 Timeline (Master) - Headers (Row 12):")
            
            for col in range(1, min(sheet.max_column + 1, 15)):
                header = sheet.cell(row=12, column=col).value
                if header:
                    contains_account = "Account" in str(header)
                    contains_name = "Name" in str(header)
                    is_target = contains_account and contains_name
                    marker = "🎯" if is_target else "  "
                    print(f"  {marker} Col {col:2d}: {header}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_data_samples():
    """Test actual data samples from the sheets"""
    print("\n📊 Testing Data Samples...")
    
    test_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    try:
        workbook = openpyxl.load_workbook(test_file)
        
        # Sample Evidence Management data
        if "Evidence Management" in workbook.sheetnames:
            sheet = workbook["Evidence Management"]
            print(f"\n📋 Evidence Management - Sample Data (Starting Row 6):")
            
            for row in range(6, min(sheet.max_row + 1, 11)):  # Show first 5 data rows
                row_data = []
                for col in range(1, min(sheet.max_column + 1, 5)):  # Show first 5 columns
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value else "")
                print(f"  Row {row:2d}: {row_data}")
        
        # Sample Timeline (Master) data
        if "Timeline (Master)" in workbook.sheetnames:
            sheet = workbook["Timeline (Master)"]
            print(f"\n📋 Timeline (Master) - Sample Data (Starting Row 14):")
            
            for row in range(14, min(sheet.max_row + 1, 19)):  # Show first 5 data rows
                row_data = []
                for col in range(1, min(sheet.max_column + 1, 5)):  # Show first 5 columns
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value else "")
                print(f"  Row {row:2d}: {row_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting List Functions Test Session...\n")
    
    success1 = test_list_systems()
    success2 = test_list_users()
    success3 = test_column_detection()
    success4 = test_data_samples()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 All tests passed! List Systems and List Users should work correctly.")
        print("\n📋 Summary:")
        print("  ✅ List Systems: Reads from Evidence Management sheet, System/Collection Name column")
        print("  ✅ List Users: Reads from Timeline (Master) sheet, Account Name column")
        print("  ✅ Both functions use correct header rows and data start rows")
        print("  ✅ Data is deduplicated and sorted")
    else:
        print("\n💥 Some tests failed. Check the issues above.")
