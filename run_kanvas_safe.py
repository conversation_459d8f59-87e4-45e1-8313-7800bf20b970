#!/usr/bin/env python3
"""
Safe startup script for KANVAS application.
This script provides better error handling and debugging information.
"""

import sys
import os
import traceback
import logging
from PySide6.QtWidgets import QApplication, QMessageBox

def setup_logging():
    """Setup logging to both file and console"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('kanvas_startup.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main function with comprehensive error handling"""
    logger = setup_logging()
    logger.info("Starting KANVAS application...")
    
    try:
        # Import the main application
        logger.info("Importing kanvas module...")
        from kanvas import MainApp
        
        logger.info("Creating MainApp instance...")
        main_app = MainApp()
        
        logger.info("Starting application event loop...")
        exit_code = main_app.run()
        
        logger.info(f"Application exited with code: {exit_code}")
        return exit_code
        
    except ImportError as e:
        error_msg = f"Failed to import required modules: {e}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # Try to show error in GUI if possible
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "Import Error", error_msg)
        except:
            pass
        
        print(f"ERROR: {error_msg}")
        return 1
        
    except Exception as e:
        error_msg = f"Unexpected error during startup: {e}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # Try to show error in GUI if possible
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "Startup Error", f"{error_msg}\n\nCheck kanvas_startup.log for details.")
        except:
            pass
        
        print(f"ERROR: {error_msg}")
        print("Full traceback:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)
