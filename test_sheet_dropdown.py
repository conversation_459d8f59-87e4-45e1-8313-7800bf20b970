#!/usr/bin/env python3
"""
Test script to verify sheet dropdown functionality in KANVAS application.
This script tests that the sheet dropdown properly updates when different Excel files are loaded.
"""

import sys
import os
import openpyxl
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import QTimer
import logging

# Add the current directory to the path so we can import kanvas modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kanvas import MainApp
from helper.windowsui import Ui_KanvasMainWindow

def test_sheet_dropdown():
    """Test the sheet dropdown functionality"""
    print("Starting KANVAS Sheet Dropdown Test...")

    # Check if QApplication already exists
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # Create main app instance (but don't call its __init__ which creates another QApplication)
    # Instead, we'll create a minimal test setup
    window = QMainWindow()
    ui = Ui_KanvasMainWindow()
    ui.setupUi(window)
    window.ui = ui
    
    # Test 1: Check if sheet dropdown exists
    sheet_dropdown = window.ui.comboBoxSheet
    if not sheet_dropdown:
        print("❌ FAIL: Sheet dropdown (comboBoxSheet) not found!")
        return False
    else:
        print("✅ PASS: Sheet dropdown found")

    # Test 2: Check initial state
    initial_count = sheet_dropdown.count()
    print(f"📊 Initial sheet dropdown count: {initial_count}")
    
    # Test 3: Load a test Excel file if available
    test_files = ["New_Case.xlsm", "New_Case2.xlsx", "sod.xlsx"]
    test_file = None
    
    for file in test_files:
        if os.path.exists(file):
            test_file = file
            break
    
    if test_file:
        print(f"📁 Found test file: {test_file}")
        
        # Load the workbook to check its sheets
        try:
            workbook = openpyxl.load_workbook(test_file)
            expected_sheets = workbook.sheetnames
            print(f"📋 Expected sheets in {test_file}: {expected_sheets}")
            
            # Test loading the file by simulating the dropdown update
            # Clear and populate the dropdown as the application would
            sheet_dropdown.clear()
            sheet_dropdown.addItems(workbook.sheetnames)
            
            # Check if dropdown was updated
            new_count = sheet_dropdown.count()
            print(f"📊 Sheet dropdown count after loading: {new_count}")
            
            # Get actual items in dropdown
            actual_sheets = [sheet_dropdown.itemText(i) for i in range(sheet_dropdown.count())]
            print(f"📋 Actual sheets in dropdown: {actual_sheets}")
            
            # Verify the sheets match
            if set(expected_sheets) == set(actual_sheets):
                print("✅ PASS: Sheet dropdown correctly updated with new file's sheets")
                return True
            else:
                print("❌ FAIL: Sheet dropdown does not match expected sheets")
                print(f"   Expected: {expected_sheets}")
                print(f"   Actual: {actual_sheets}")
                return False
                
        except Exception as e:
            print(f"❌ ERROR: Failed to test with file {test_file}: {e}")
            return False
    else:
        print("⚠️  WARNING: No test Excel files found. Cannot test file loading.")
        print("   Available files should be: New_Case.xlsm, New_Case2.xlsx, or sod.xlsx")
        return False

if __name__ == "__main__":
    success = test_sheet_dropdown()
    if success:
        print("\n🎉 All tests passed! Sheet dropdown functionality is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the issues above.")
        sys.exit(1)
