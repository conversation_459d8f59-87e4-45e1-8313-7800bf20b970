"""
This file has been generated based on the Excel 2011 dictionary since Excel 2016 was
screwing things up, see:
http://lists.apple.com/archives/applescript-users/2015/Mar/msg00114.html
http://lists.apple.com/archives/applescript-users/2015/Mar/msg00290.html

Usage:
>>> from appscript.terminology import dump
>>> dump('Microsoft Excel', '/path/to/myappglue.py')

>>> import myappglue
>>> from appscript import app
>>> app = app('Microsoft Excel', terms=myappglue)
"""
version = 1.1
# path = '/Applications/Microsoft Office 2011/Microsoft Excel.app'

classes = [
    ("base_object", b"oItm"),
    ("base_application", b"cbap"),
    ("base_document", b"bDoc"),
    ("basic_window", b"bwin"),
    ("print_settings", b"pset"),
    ("command_bar_button", b"mCBB"),
    ("command_bar_combobox", b"mCBX"),
    ("command_bar_control", b"mCBC"),
    ("command_bar_popup", b"mCBP"),
    ("command_bar", b"msCB"),
    ("custom_document_property", b"mCDP"),
    ("document_property", b"mDPr"),
    ("web_page_font", b"mWPF"),
    ("Excel_comment", b"X229"),
    ("ODBC_error", b"X235"),
    ("Protection", b"Xpot"),
    ("above_average_format_condition", b"X322"),
    ("active_filter", b"Y903"),
    ("add_in", b"X133"),
    ("application", b"capp"),
    ("autofilter", b"X240"),
    ("border", b"X251"),
    ("button", b"Xbtn"),
    ("calculated_field", b"XPFc"),
    ("calculated_item", b"XPIi"),
    ("calculated_member", b"X901"),
    ("checkbox", b"Xckb"),
    ("child_item", b"XPIc"),
    ("color_scale_criteria", b"X310"),
    ("color_scale_criterion", b"X311"),
    ("color_scale_format_condition", b"X325"),
    ("colorstop", b"X305"),
    ("colorstops", b"X304"),
    ("column_field", b"XPFn"),
    ("column_item", b"XPIo"),
    ("condition_value", b"X324"),
    ("cube_field", b"X900"),
    ("custom_view", b"X225"),
    ("data_field", b"XPFd"),
    ("databar_border", b"X313"),
    ("databar_format_condition", b"X312"),
    ("default_web_options", b"X300"),
    ("dialog", b"X165"),
    ("display_format", b"X306"),
    ("document", b"docu"),
    ("dropdown", b"XdpD"),
    ("filter", b"X242"),
    ("format_color", b"X307"),
    ("format_condition_icon_object", b"X318"),
    ("format_condition_icon_set", b"X319"),
    ("format_condition_icon_sets", b"X320"),
    ("format_condition", b"X227"),
    ("graphic", b"X308"),
    ("groupbox", b"XGBc"),
    ("hidden_field", b"XPFh"),
    ("hidden_item", b"XPIh"),
    ("horizontal_page_break", b"X122"),
    ("hyperlink", b"X239"),
    ("icon_criteria", b"X316"),
    ("icon_criterion", b"X317"),
    ("icon_set_format_condition", b"X315"),
    ("international_macro_sheet", b"XiSH"),
    ("label", b"Xlbl"),
    ("linear_gradient", b"X302"),
    ("list_column", b"X248"),
    ("list_object", b"X244"),
    ("list_row", b"X246"),
    ("listbox", b"XLbx"),
    ("macro_sheet", b"XmSH"),
    ("named_item", b"X220"),
    ("negative_bar_format", b"X314"),
    ("option_button", b"XObn"),
    ("outline", b"X212"),
    ("page_field", b"XPFp"),
    ("page_setup", b"X218"),
    ("pane", b"X189"),
    ("parent_item", b"XPIp"),
    ("phonetic", b"X288"),
    ("pivot_axis", b"X902"),
    ("pivot_cache", b"X151"),
    ("pivot_cell", b"X908"),
    ("pivot_field", b"X157"),
    ("pivot_filter", b"X903"),
    ("pivot_formula", b"X153"),
    ("pivot_item", b"X160"),
    ("pivot_line", b"X907"),
    ("pivot_table", b"X155"),
    ("query_table", b"X231"),
    ("recent_file", b"X125"),
    ("rectangular_gradient", b"X303"),
    ("row_field", b"XPFr"),
    ("row_item", b"XPIr"),
    ("scenario", b"X191"),
    ("scrollbar", b"XSrl"),
    ("sheet", b"X128"),
    ("slicer", b"X906"),
    ("sort", b"Xsrt"),
    ("sortfield", b"Xsfd"),
    ("sortfields", b"Xsfs"),
    ("spinner", b"XSpn"),
    ("tab", b"Xtab"),
    ("table_style_element", b"TSET"),
    ("table_style", b"1936"),
    ("textbox", b"XTbx"),
    ("top_10_format_condition", b"X321"),
    ("treeview_control", b"X911"),
    ("unique_values_format_condition", b"X323"),
    ("validation", b"X237"),
    ("value_change", b"X905"),
    ("vertical_page_break", b"X121"),
    ("web_options", b"X301"),
    ("window", b"cwin"),
    ("workbook_connection", b"X904"),
    ("workbook", b"X141"),
    ("worksheet", b"XwSH"),
    ("xlspelling_options", b"Xspo"),
    ("adjustment", b"mAdj"),
    ("arc", b"Xarc"),
    ("bullet_format", b"xbf2"),
    ("callout_format", b"X101"),
    ("callout", b"cD00"),
    ("connector_format", b"X294"),
    ("fill_format", b"X110"),
    ("glow_format", b"DGoF"),
    ("gradient_stop", b"GrdS"),
    ("line_format", b"X103"),
    ("line", b"Xlne"),
    ("major_theme_font", b"1ThF"),
    ("minor_theme_font", b"2ThF"),
    ("office_theme", b"DOfT"),
    ("oval", b"XOvl"),
    ("paragraph_format", b"xpf2"),
    ("picture_format", b"X106"),
    ("picture", b"cD04"),
    ("rectangle", b"XRct"),
    ("reflection_format", b"DReF"),
    ("ruler_level", b"xRlL"),
    ("ruler", b"xRul"),
    ("shadow_format", b"X107"),
    ("shape_connector", b"cD01"),
    ("shape_font", b"Fon2"),
    ("shape_line", b"cD12"),
    ("shape_text_frame", b"X295"),
    ("shape_textbox", b"cD07"),
    ("shape", b"pShp"),
    ("soft_edge_format", b"DSeF"),
    ("tab_stop", b"Tab2"),
    ("text_column", b"Tcl2"),
    ("text_frame", b"X293"),
    ("theme_color_scheme", b"DTcS"),
    ("theme_color", b"DThC"),
    ("theme_effect_scheme", b"DTeS"),
    ("theme_font_scheme", b"DTfS"),
    ("theme_font", b"DThF"),
    ("threeD_format", b"X109"),
    ("word_art_format", b"X108"),
    ("character", b"cha "),
    ("font", b"X111"),
    ("paragraph", b"cpar"),
    ("sentence", b"csen"),
    ("style", b"X129"),
    ("text_flow", b"cflo"),
    ("text_range_character", b"TrCh"),
    ("text_range_line", b"TrLn"),
    ("text_range", b"TObj"),
    ("word", b"cwor"),
    ("cell", b"ccel"),
    ("column", b"ccol"),
    ("range", b"X117"),
    ("row", b"crow"),
    ("autocorrect", b"X250"),
    ("area_group", b"cg01"),
    ("axis_title", b"X257"),
    ("axis", b"X255"),
    ("bar_group", b"cg05"),
    ("chart_area", b"X284"),
    ("chart_fill_format", b"X253"),
    ("chart_format", b"X115"),
    ("chart_group", b"X258"),
    ("chart_object", b"X221"),
    ("chart_sheet", b"XcSH"),
    ("chart_title", b"X256"),
    ("chart", b"X119"),
    ("column_group", b"cg04"),
    ("corners", b"X272"),
    ("data_label", b"X265"),
    ("data_table", b"X287"),
    ("display_unit_label", b"X299"),
    ("doughnut_group", b"cg03"),
    ("down_bars", b"X279"),
    ("drop_lines", b"X276"),
    ("error_bars", b"X286"),
    ("floor", b"X280"),
    ("gridlines", b"X275"),
    ("hilo_lines", b"X274"),
    ("interior", b"X252"),
    ("leader_lines", b"X277"),
    ("legend_entry", b"X267"),
    ("legend_key", b"X269"),
    ("legend", b"X285"),
    ("line_group", b"cg02"),
    ("pie_group", b"cg06"),
    ("plot_area", b"X283"),
    ("radar_group", b"cg07"),
    ("series_lines", b"X273"),
    ("series_point", b"X262"),
    ("series", b"X263"),
    ("tick_labels", b"X282"),
    ("trendline", b"X271"),
    ("up_bars", b"X278"),
    ("walls", b"X281"),
    ("xy_group", b"cg09"),
]

enums = [
    ("yes", b"yes "),
    ("no", b"no  "),
    ("ask", b"ask "),
    ("index", b"indx"),
    ("named", b"name"),
    ("id", b"ID  "),
    ("Macintosh_path", b"mPth"),
    ("Posix_path", b"file"),
    ("standard", b"lwst"),
    ("detailed", b"lwdt"),
    ("line_dash_style_unset", b"\x00\x92\xff\xfe"),
    ("line_dash_style_solid", b"\x00\x93\x00\x01"),
    ("line_dash_style_square_dot", b"\x00\x93\x00\x02"),
    ("line_dash_style_round_dot", b"\x00\x93\x00\x03"),
    ("line_dash_style_dash", b"\x00\x93\x00\x04"),
    ("line_dash_style_dash_dot", b"\x00\x93\x00\x05"),
    ("line_dash_style_dash_dot_dot", b"\x00\x93\x00\x06"),
    ("line_dash_style_long_dash", b"\x00\x93\x00\x07"),
    ("line_dash_style_long_dash_dot", b"\x00\x93\x00\x08"),
    ("line_dash_style_long_dash_dot_dot", b"\x00\x93\x00\t"),
    ("line_dash_style_system_dash", b"\x00\x93\x00\n"),
    ("line_dash_style_system_dot", b"\x00\x93\x00\x0b"),
    ("line_dash_style_system_dash_dot", b"\x00\x93\x00\x0c"),
    ("line_style_unset", b"\x00\x94\xff\xfe"),
    ("single_line", b"\x00\x95\x00\x01"),
    ("thin_thin_line", b"\x00\x95\x00\x02"),
    ("thin_thick_line", b"\x00\x95\x00\x03"),
    ("thick_thin_line", b"\x00\x95\x00\x04"),
    ("thick_between_thin_line", b"\x00\x95\x00\x05"),
    ("arrowhead_style_unset", b"\x00\x91\xff\xfe"),
    ("no_arrowhead", b"\x00\x92\x00\x01"),
    ("triangle_arrowhead", b"\x00\x92\x00\x02"),
    ("open_arrowhead", b"\x00\x92\x00\x03"),
    ("stealth_arrowhead", b"\x00\x92\x00\x04"),
    ("diamond_arrowhead", b"\x00\x92\x00\x05"),
    ("oval_arrowhead", b"\x00\x92\x00\x06"),
    ("arrowhead_width_unset", b"\x00\x90\xff\xfe"),
    ("narrow_width_arrowhead", b"\x00\x91\x00\x01"),
    ("medium_width_arrowhead", b"\x00\x91\x00\x02"),
    ("wide_arrowhead", b"\x00\x91\x00\x03"),
    ("arrowhead_length_unset", b"\x00\x93\xff\xfe"),
    ("short_arrowhead", b"\x00\x94\x00\x01"),
    ("medium_arrowhead", b"\x00\x94\x00\x02"),
    ("long_arrowhead", b"\x00\x94\x00\x03"),
    ("fill_unset", b"\x00c\xff\xfe"),
    ("fill_solid", b"\x00d\x00\x01"),
    ("fill_patterned", b"\x00d\x00\x02"),
    ("fill_gradient", b"\x00d\x00\x03"),
    ("fill_textured", b"\x00d\x00\x04"),
    ("fill_background", b"\x00d\x00\x05"),
    ("fill_picture", b"\x00d\x00\x06"),
    ("gradient_unset", b"\x00d\xff\xfe"),
    ("horizontal_gradient", b"\x00e\x00\x01"),
    ("vertical_gradient", b"\x00e\x00\x02"),
    ("diagonal_up_gradient", b"\x00e\x00\x03"),
    ("diagonal_down_gradient", b"\x00e\x00\x04"),
    ("from_corner_gradient", b"\x00e\x00\x05"),
    ("from_title_gradient", b"\x00e\x00\x06"),
    ("from_center_gradient", b"\x00e\x00\x07"),
    ("gradient_type_unset", b"\x03\xef\xff\xfe"),
    ("single_shade_gradient_type", b"\x03\xf0\x00\x01"),
    ("two_colors_gradient_type", b"\x03\xf0\x00\x02"),
    ("preset_colors_gradient_type", b"\x03\xf0\x00\x03"),
    ("multi_colors_gradient_type", b"\x03\xf0\x00\x04"),
    ("texture_type_texture_type_unset", b"\x03\xf0\xff\xfe"),
    ("texture_type_preset_texture", b"\x03\xf1\x00\x01"),
    ("texture_type_user_defined_texture", b"\x03\xf1\x00\x02"),
    ("preset_texture_unset", b"\x00e\xff\xfe"),
    ("texture_papyrus", b"\x00f\x00\x01"),
    ("texture_canvas", b"\x00f\x00\x02"),
    ("texture_denim", b"\x00f\x00\x03"),
    ("texture_woven_mat", b"\x00f\x00\x04"),
    ("texture_water_droplets", b"\x00f\x00\x05"),
    ("texture_paper_bag", b"\x00f\x00\x06"),
    ("texture_fish_fossil", b"\x00f\x00\x07"),
    ("texture_sand", b"\x00f\x00\x08"),
    ("texture_green_marble", b"\x00f\x00\t"),
    ("texture_white_marble", b"\x00f\x00\n"),
    ("texture_brown_marble", b"\x00f\x00\x0b"),
    ("texture_granite", b"\x00f\x00\x0c"),
    ("texture_newsprint", b"\x00f\x00\r"),
    ("texture_recycled_paper", b"\x00f\x00\x0e"),
    ("texture_parchment", b"\x00f\x00\x0f"),
    ("texture_stationery", b"\x00f\x00\x10"),
    ("texture_blue_tissue_paper", b"\x00f\x00\x11"),
    ("texture_pink_tissue_paper", b"\x00f\x00\x12"),
    ("texture_purple_mesh", b"\x00f\x00\x13"),
    ("texture_bouquet", b"\x00f\x00\x14"),
    ("texture_cork", b"\x00f\x00\x15"),
    ("texture_walnut", b"\x00f\x00\x16"),
    ("texture_oak", b"\x00f\x00\x17"),
    ("texture_medium_wood", b"\x00f\x00\x18"),
    ("pattern_unset", b"\x00f\xff\xfe"),
    ("five_percent_pattern", b"\x00g\x00\x01"),
    ("ten_percent_pattern", b"\x00g\x00\x02"),
    ("twenty_percent_pattern", b"\x00g\x00\x03"),
    ("twenty_five_percent_pattern", b"\x00g\x00\x04"),
    ("thirty_percent_pattern", b"\x00g\x00\x05"),
    ("forty_percent_pattern", b"\x00g\x00\x06"),
    ("fifty_percent_pattern", b"\x00g\x00\x07"),
    ("sixty_percent_pattern", b"\x00g\x00\x08"),
    ("seventy_percent_pattern", b"\x00g\x00\t"),
    ("seventy_five_percent_pattern", b"\x00g\x00\n"),
    ("eighty_percent_pattern", b"\x00g\x00\x0b"),
    ("ninety_percent_pattern", b"\x00g\x00\x0c"),
    ("dark_horizontal_pattern", b"\x00g\x00\r"),
    ("dark_vertical_pattern", b"\x00g\x00\x0e"),
    ("dark_downward_diagonal_pattern", b"\x00g\x00\x0f"),
    ("dark_upward_diagonal_pattern", b"\x00g\x00\x10"),
    ("small_checker_board_pattern", b"\x00g\x00\x11"),
    ("trellis_pattern", b"\x00g\x00\x12"),
    ("light_horizontal_pattern", b"\x00g\x00\x13"),
    ("light_vertical_pattern", b"\x00g\x00\x14"),
    ("light_downward_diagonal_pattern", b"\x00g\x00\x15"),
    ("light_upward_diagonal_pattern", b"\x00g\x00\x16"),
    ("small_grid_pattern", b"\x00g\x00\x17"),
    ("dotted_diamond_pattern", b"\x00g\x00\x18"),
    ("wide_downward_diagonal", b"\x00g\x00\x19"),
    ("wide_upward_diagonal_pattern", b"\x00g\x00\x1a"),
    ("dashed_upward_diagonal_pattern", b"\x00g\x00\x1b"),
    ("dashed_downward_diagonal_pattern", b"\x00g\x00\x1c"),
    ("narrow_vertical_pattern", b"\x00g\x00\x1d"),
    ("narrow_horizontal_pattern", b"\x00g\x00\x1e"),
    ("dashed_vertical_pattern", b"\x00g\x00\x1f"),
    ("dashed_horizontal_pattern", b"\x00g\x00 "),
    ("large_confetti_pattern", b"\x00g\x00!"),
    ("large_grid_pattern", b'\x00g\x00"'),
    ("horizontal_brick_pattern", b"\x00g\x00#"),
    ("large_checker_board_pattern", b"\x00g\x00$"),
    ("small_confetti_pattern", b"\x00g\x00%"),
    ("zig_zag_pattern", b"\x00g\x00&"),
    ("solid_diamond_pattern", b"\x00g\x00'"),
    ("diagonal_brick_pattern", b"\x00g\x00("),
    ("outlined_diamond_pattern", b"\x00g\x00)"),
    ("plaid_pattern", b"\x00g\x00*"),
    ("sphere_pattern", b"\x00g\x00+"),
    ("weave_pattern", b"\x00g\x00,"),
    ("dotted_grid_pattern", b"\x00g\x00-"),
    ("divot_pattern", b"\x00g\x00."),
    ("shingle_pattern", b"\x00g\x00/"),
    ("wave_pattern", b"\x00g\x000"),
    ("horizontal_pattern", b"\x00g\x001"),
    ("vertical_pattern", b"\x00g\x002"),
    ("cross_pattern", b"\x00g\x003"),
    ("downward_diagonal_pattern", b"\x00g\x004"),
    ("upward_diagonal_pattern", b"\x00g\x005"),
    ("diagonal_cross_pattern", b"\x00g\x005"),
    ("preset_gradient_unset", b"\x00g\xff\xfe"),
    ("gradient_early_sunset", b"\x00h\x00\x01"),
    ("gradient_late_sunset", b"\x00h\x00\x02"),
    ("gradient_nightfall", b"\x00h\x00\x03"),
    ("gradient_daybreak", b"\x00h\x00\x04"),
    ("gradient_horizon", b"\x00h\x00\x05"),
    ("gradient_desert", b"\x00h\x00\x06"),
    ("gradient_ocean", b"\x00h\x00\x07"),
    ("gradient_calm_water", b"\x00h\x00\x08"),
    ("gradient_fire", b"\x00h\x00\t"),
    ("gradient_fog", b"\x00h\x00\n"),
    ("gradient_moss", b"\x00h\x00\x0b"),
    ("gradient_peacock", b"\x00h\x00\x0c"),
    ("gradient_wheat", b"\x00h\x00\r"),
    ("gradient_parchment", b"\x00h\x00\x0e"),
    ("gradient_mahogany", b"\x00h\x00\x0f"),
    ("gradient_rainbow", b"\x00h\x00\x10"),
    ("gradient_rainbow2", b"\x00h\x00\x11"),
    ("gradient_gold", b"\x00h\x00\x12"),
    ("gradient_gold2", b"\x00h\x00\x13"),
    ("gradient_brass", b"\x00h\x00\x14"),
    ("gradient_chrome", b"\x00h\x00\x15"),
    ("gradient_chrome2", b"\x00h\x00\x16"),
    ("gradient_silver", b"\x00h\x00\x17"),
    ("gradient_sapphire", b"\x00h\x00\x18"),
    ("shadow_unset", b"\x03_\xff\xfe"),
    ("shadow1", b"\x03`\x00\x01"),
    ("shadow2", b"\x03`\x00\x02"),
    ("shadow3", b"\x03`\x00\x03"),
    ("shadow4", b"\x03`\x00\x04"),
    ("shadow5", b"\x03`\x00\x05"),
    ("shadow6", b"\x03`\x00\x06"),
    ("shadow7", b"\x03`\x00\x07"),
    ("shadow8", b"\x03`\x00\x08"),
    ("shadow9", b"\x03`\x00\t"),
    ("shadow10", b"\x03`\x00\n"),
    ("shadow11", b"\x03`\x00\x0b"),
    ("shadow12", b"\x03`\x00\x0c"),
    ("shadow13", b"\x03`\x00\r"),
    ("shadow14", b"\x03`\x00\x0e"),
    ("shadow15", b"\x03`\x00\x0f"),
    ("shadow16", b"\x03`\x00\x10"),
    ("shadow17", b"\x03`\x00\x11"),
    ("shadow18", b"\x03`\x00\x12"),
    ("shadow19", b"\x03`\x00\x13"),
    ("shadow20", b"\x03`\x00\x14"),
    ("shadow21", b"\x03`\x00\x15"),
    ("shadow22", b"\x03`\x00\x16"),
    ("shadow23", b"\x03`\x00\x17"),
    ("shadow24", b"\x03`\x00\x18"),
    ("shadow25", b"\x03`\x00\x19"),
    ("shadow26", b"\x03`\x00\x1a"),
    ("shadow27", b"\x03`\x00\x1b"),
    ("shadow28", b"\x03`\x00\x1c"),
    ("shadow29", b"\x03`\x00\x1d"),
    ("shadow30", b"\x03`\x00\x1e"),
    ("shadow31", b"\x03`\x00\x1f"),
    ("shadow32", b"\x03`\x00 "),
    ("shadow33", b"\x03`\x00!"),
    ("shadow34", b'\x03`\x00"'),
    ("shadow35", b"\x03`\x00#"),
    ("shadow36", b"\x03`\x00$"),
    ("shadow37", b"\x03`\x00%"),
    ("shadow38", b"\x03`\x00&"),
    ("shadow39", b"\x03`\x00'"),
    ("shadow40", b"\x03`\x00("),
    ("shadow41", b"\x03`\x00)"),
    ("shadow42", b"\x03`\x00*"),
    ("shadow43", b"\x03`\x00+"),
    ("wordart_format_unset", b"\x03\xf1\xff\xfe"),
    ("wordart_format1", b"\x03\xf2\x00\x00"),
    ("wordart_format2", b"\x03\xf2\x00\x01"),
    ("wordart_format3", b"\x03\xf2\x00\x02"),
    ("wordart_format4", b"\x03\xf2\x00\x03"),
    ("wordart_format5", b"\x03\xf2\x00\x04"),
    ("wordart_format6", b"\x03\xf2\x00\x05"),
    ("wordart_format7", b"\x03\xf2\x00\x06"),
    ("wordart_format8", b"\x03\xf2\x00\x07"),
    ("wordart_format9", b"\x03\xf2\x00\x08"),
    ("wordart_format10", b"\x03\xf2\x00\t"),
    ("wordart_format11", b"\x03\xf2\x00\n"),
    ("wordart_format12", b"\x03\xf2\x00\x0b"),
    ("wordart_format13", b"\x03\xf2\x00\x0c"),
    ("wordart_format14", b"\x03\xf2\x00\r"),
    ("wordart_format15", b"\x03\xf2\x00\x0e"),
    ("wordart_format16", b"\x03\xf2\x00\x0f"),
    ("wordart_format17", b"\x03\xf2\x00\x10"),
    ("wordart_format18", b"\x03\xf2\x00\x11"),
    ("wordart_format19", b"\x03\xf2\x00\x12"),
    ("wordart_format20", b"\x03\xf2\x00\x13"),
    ("wordart_format21", b"\x03\xf2\x00\x14"),
    ("wordart_format22", b"\x03\xf2\x00\x15"),
    ("wordart_format23", b"\x03\xf2\x00\x16"),
    ("wordart_format24", b"\x03\xf2\x00\x17"),
    ("wordart_format25", b"\x03\xf2\x00\x18"),
    ("wordart_format26", b"\x03\xf2\x00\x19"),
    ("wordart_format27", b"\x03\xf2\x00\x1a"),
    ("wordart_format28", b"\x03\xf2\x00\x1b"),
    ("wordart_format29", b"\x03\xf2\x00\x1c"),
    ("wordart_format30", b"\x03\xf2\x00\x1d"),
    ("text_effect_shape_unset", b"\x00\x97\xff\xfe"),
    ("plain_text", b"\x00\x98\x00\x01"),
    ("stop", b"\x00\x98\x00\x02"),
    ("triangle_up", b"\x00\x98\x00\x03"),
    ("triangle_down", b"\x00\x98\x00\x04"),
    ("chevron_up", b"\x00\x98\x00\x05"),
    ("chevron_down", b"\x00\x98\x00\x06"),
    ("ring_inside", b"\x00\x98\x00\x07"),
    ("ring_outside", b"\x00\x98\x00\x08"),
    ("arch_up_curve", b"\x00\x98\x00\t"),
    ("arch_down_curve", b"\x00\x98\x00\n"),
    ("circle_curve", b"\x00\x98\x00\x0b"),
    ("button_curve", b"\x00\x98\x00\x0c"),
    ("arch_up_pour", b"\x00\x98\x00\r"),
    ("arch_down_pour", b"\x00\x98\x00\x0e"),
    ("circle_pour", b"\x00\x98\x00\x0f"),
    ("button_pour", b"\x00\x98\x00\x10"),
    ("curve_up", b"\x00\x98\x00\x11"),
    ("curve_down", b"\x00\x98\x00\x12"),
    ("can_up", b"\x00\x98\x00\x13"),
    ("can_down", b"\x00\x98\x00\x14"),
    ("wave1", b"\x00\x98\x00\x15"),
    ("wave2", b"\x00\x98\x00\x16"),
    ("double_wave1", b"\x00\x98\x00\x17"),
    ("double_wave2", b"\x00\x98\x00\x18"),
    ("inflate", b"\x00\x98\x00\x19"),
    ("deflate", b"\x00\x98\x00\x1a"),
    ("inflate_bottom", b"\x00\x98\x00\x1b"),
    ("deflate_bottom", b"\x00\x98\x00\x1c"),
    ("inflate_top", b"\x00\x98\x00\x1d"),
    ("deflate_top", b"\x00\x98\x00\x1e"),
    ("deflate_inflate", b"\x00\x98\x00\x1f"),
    ("deflate_inflate_deflate", b"\x00\x98\x00 "),
    ("fade_right", b"\x00\x98\x00!"),
    ("fade_left", b'\x00\x98\x00"'),
    ("fade_up", b"\x00\x98\x00#"),
    ("fade_down", b"\x00\x98\x00$"),
    ("slant_up", b"\x00\x98\x00%"),
    ("slant_down", b"\x00\x98\x00&"),
    ("cascade_up", b"\x00\x98\x00'"),
    ("cascade_down", b"\x00\x98\x00("),
    ("text_effect_alignment_unset", b"\x00\x96\xff\xfe"),
    ("left_text_effect_alignment", b"\x00\x97\x00\x01"),
    ("centered_text_effect_alignment", b"\x00\x97\x00\x02"),
    ("right_text_effect_alignment", b"\x00\x97\x00\x03"),
    ("justify_text_effect_alignment", b"\x00\x97\x00\x04"),
    ("word_justify_text_effect_alignment", b"\x00\x97\x00\x05"),
    ("stretch_justify_text_effect_alignment", b"\x00\x97\x00\x06"),
    ("preset_lighting_direction_unset", b"\x00\x9b\xff\xfe"),
    ("light_from_top_left", b"\x00\x9c\x00\x01"),
    ("light_from_top", b"\x00\x9c\x00\x02"),
    ("light_from_top_right", b"\x00\x9c\x00\x03"),
    ("light_from_left", b"\x00\x9c\x00\x04"),
    ("light_from_none", b"\x00\x9c\x00\x05"),
    ("light_from_right", b"\x00\x9c\x00\x06"),
    ("light_from_bottom_left", b"\x00\x9c\x00\x07"),
    ("light_from_bottom", b"\x00\x9c\x00\x08"),
    ("light_from_bottom_right", b"\x00\x9c\x00\t"),
    ("lighting_softness_unset", b"\x00\x9c\xff\xfe"),
    ("lighting_dim", b"\x00\x9d\x00\x01"),
    ("lighting_normal", b"\x00\x9d\x00\x02"),
    ("lighting_bright", b"\x00\x9d\x00\x03"),
    ("preset_material_unset", b"\x00\x9d\xff\xfe"),
    ("matte", b"\x00\x9e\x00\x01"),
    ("plastic", b"\x00\x9e\x00\x02"),
    ("metal", b"\x00\x9e\x00\x03"),
    ("wireframe", b"\x00\x9e\x00\x04"),
    ("matte2", b"\x00\x9e\x00\x05"),
    ("plastic2", b"\x00\x9e\x00\x06"),
    ("metal2", b"\x00\x9e\x00\x07"),
    ("warm_matte", b"\x00\x9e\x00\x08"),
    ("translucent_powder", b"\x00\x9e\x00\t"),
    ("powder", b"\x00\x9e\x00\n"),
    ("dark_edge", b"\x00\x9e\x00\x0b"),
    ("soft_edge", b"\x00\x9e\x00\x0c"),
    ("material_clear", b"\x00\x9e\x00\r"),
    ("flat", b"\x00\x9e\x00\x0e"),
    ("soft_metal", b"\x00\x9e\x00\x0f"),
    ("preset_extrusion_direction_unset", b"\x00\x99\xff\xfe"),
    ("extrude_bottom_right", b"\x00\x9a\x00\x01"),
    ("extrude_bottom", b"\x00\x9a\x00\x02"),
    ("extrude_bottom_left", b"\x00\x9a\x00\x03"),
    ("extrude_right", b"\x00\x9a\x00\x04"),
    ("extrude_none", b"\x00\x9a\x00\x05"),
    ("extrude_left", b"\x00\x9a\x00\x06"),
    ("extrude_top_right", b"\x00\x9a\x00\x07"),
    ("extrude_top", b"\x00\x9a\x00\x08"),
    ("extrude_top_left", b"\x00\x9a\x00\t"),
    ("preset_threeD_format_unset", b"\x00\x98\xff\xfe"),
    ("format1", b"\x00\x99\x00\x01"),
    ("format2", b"\x00\x99\x00\x02"),
    ("format3", b"\x00\x99\x00\x03"),
    ("format4", b"\x00\x99\x00\x04"),
    ("format5", b"\x00\x99\x00\x05"),
    ("format6", b"\x00\x99\x00\x06"),
    ("format7", b"\x00\x99\x00\x07"),
    ("format8", b"\x00\x99\x00\x08"),
    ("format9", b"\x00\x99\x00\t"),
    ("format10", b"\x00\x99\x00\n"),
    ("format11", b"\x00\x99\x00\x0b"),
    ("format12", b"\x00\x99\x00\x0c"),
    ("format13", b"\x00\x99\x00\r"),
    ("format14", b"\x00\x99\x00\x0e"),
    ("format15", b"\x00\x99\x00\x0f"),
    ("format16", b"\x00\x99\x00\x10"),
    ("format17", b"\x00\x99\x00\x11"),
    ("format18", b"\x00\x99\x00\x12"),
    ("format19", b"\x00\x99\x00\x13"),
    ("format20", b"\x00\x99\x00\x14"),
    ("extrusion_color_unset", b"\x00\x9a\xff\xfe"),
    ("extrusion_color_automatic", b"\x00\x9b\x00\x01"),
    ("extrusion_color_custom", b"\x00\x9b\x00\x02"),
    ("connector_type_unset", b"\x00h\xff\xfe"),
    ("straight", b"\x00i\x00\x01"),
    ("elbow", b"\x00i\x00\x02"),
    ("curve", b"\x00i\x00\x03"),
    ("horizontal_anchor_unset", b"\x00\x9e\xff\xfe"),
    ("horizontal_anchor_none", b"\x00\x9f\x00\x01"),
    ("horizontal_anchor_center", b"\x00\x9f\x00\x02"),
    ("vertical_anchor_unset", b"\x00\x9f\xff\xfe"),
    ("anchor_top", b"\x00\xa0\x00\x01"),
    ("anchor_top_baseline", b"\x00\xa0\x00\x02"),
    ("anchor_middle", b"\x00\xa0\x00\x03"),
    ("anchor_bottom", b"\x00\xa0\x00\x04"),
    ("anchor_bottom_baseline", b"\x00\xa0\x00\x05"),
    ("autoshape_shape_type_unset", b"\x00i\xff\xfe"),
    ("autoshape_rectangle", b"\x00j\x00\x01"),
    ("autoshape_parallelogram", b"\x00j\x00\x02"),
    ("autoshape_trapezoid", b"\x00j\x00\x03"),
    ("autoshape_diamond", b"\x00j\x00\x04"),
    ("autoshape_rounded_rectangle", b"\x00j\x00\x05"),
    ("autoshape_octagon", b"\x00j\x00\x06"),
    ("autoshape_isosceles_triangle", b"\x00j\x00\x07"),
    ("autoshape_right_triangle", b"\x00j\x00\x08"),
    ("autoshape_oval", b"\x00j\x00\t"),
    ("autoshape_hexagon", b"\x00j\x00\n"),
    ("autoshape_cross", b"\x00j\x00\x0b"),
    ("autoshape_regular_pentagon", b"\x00j\x00\x0c"),
    ("autoshape_can", b"\x00j\x00\r"),
    ("autoshape_cube", b"\x00j\x00\x0e"),
    ("autoshape_bevel", b"\x00j\x00\x0f"),
    ("autoshape_folded_corner", b"\x00j\x00\x10"),
    ("autoshape_smiley_face", b"\x00j\x00\x11"),
    ("autoshape_donut", b"\x00j\x00\x12"),
    ("autoshape_no_symbol", b"\x00j\x00\x13"),
    ("autoshape_block_arc", b"\x00j\x00\x14"),
    ("autoshape_heart", b"\x00j\x00\x15"),
    ("autoshape_lightning_bolt", b"\x00j\x00\x16"),
    ("autoshape_sun", b"\x00j\x00\x17"),
    ("autoshape_moon", b"\x00j\x00\x18"),
    ("autoshape_arc", b"\x00j\x00\x19"),
    ("autoshape_double_bracket", b"\x00j\x00\x1a"),
    ("autoshape_double_brace", b"\x00j\x00\x1b"),
    ("autoshape_plaque", b"\x00j\x00\x1c"),
    ("autoshape_left_bracket", b"\x00j\x00\x1d"),
    ("autoshape_right_bracket", b"\x00j\x00\x1e"),
    ("autoshape_left_brace", b"\x00j\x00\x1f"),
    ("autoshape_right_brace", b"\x00j\x00 "),
    ("autoshape_right_arrow", b"\x00j\x00!"),
    ("autoshape_left_arrow", b'\x00j\x00"'),
    ("autoshape_up_arrow", b"\x00j\x00#"),
    ("autoshape_down_arrow", b"\x00j\x00$"),
    ("autoshape_left_right_arrow", b"\x00j\x00%"),
    ("autoshape_up_down_arrow", b"\x00j\x00&"),
    ("autoshape_quad_arrow", b"\x00j\x00'"),
    ("autoshape_left_right_up_arrow", b"\x00j\x00("),
    ("autoshape_bent_arrow", b"\x00j\x00)"),
    ("autoshape_U_turn_arrow", b"\x00j\x00*"),
    ("autoshape_left_up_arrow", b"\x00j\x00+"),
    ("autoshape_bent_up_arrow", b"\x00j\x00,"),
    ("autoshape_curved_right_arrow", b"\x00j\x00-"),
    ("autoshape_curved_left_arrow", b"\x00j\x00."),
    ("autoshape_curved_up_arrow", b"\x00j\x00/"),
    ("autoshape_curved_down_arrow", b"\x00j\x000"),
    ("autoshape_striped_right_arrow", b"\x00j\x001"),
    ("autoshape_notched_right_arrow", b"\x00j\x002"),
    ("autoshape_pentagon", b"\x00j\x003"),
    ("autoshape_chevron", b"\x00j\x004"),
    ("autoshape_right_arrow_callout", b"\x00j\x005"),
    ("autoshape_left_arrow_callout", b"\x00j\x006"),
    ("autoshape_up_arrow_callout", b"\x00j\x007"),
    ("autoshape_down_arrow_callout", b"\x00j\x008"),
    ("autoshape_left_right_arrow_callout", b"\x00j\x009"),
    ("autoshape_up_down_arrow_callout", b"\x00j\x00:"),
    ("autoshape_quad_arrow_callout", b"\x00j\x00;"),
    ("autoshape_circular_arrow", b"\x00j\x00<"),
    ("autoshape_flowchart_process", b"\x00j\x00="),
    ("autoshape_flowchart_alternate_process", b"\x00j\x00>"),
    ("autoshape_flowchart_decision", b"\x00j\x00?"),
    ("autoshape_flowchart_data", b"\x00j\x00@"),
    ("autoshape_flowchart_predefined_process", b"\x00j\x00A"),
    ("autoshape_flowchart_internal_storage", b"\x00j\x00B"),
    ("autoshape_flowchart_document", b"\x00j\x00C"),
    ("autoshape_flowchart_multi_document", b"\x00j\x00D"),
    ("autoshape_flowchart_terminator", b"\x00j\x00E"),
    ("autoshape_flowchart_preparation", b"\x00j\x00F"),
    ("autoshape_flowchart_manual_input", b"\x00j\x00G"),
    ("autoshape_flowchart_manual_operation", b"\x00j\x00H"),
    ("autoshape_flowchart_connector", b"\x00j\x00I"),
    ("autoshape_flowchart_offpage_connector", b"\x00j\x00J"),
    ("autoshape_flowchart_card", b"\x00j\x00K"),
    ("autoshape_flowchart_punched_tape", b"\x00j\x00L"),
    ("autoshape_flowchart_summing_junction", b"\x00j\x00M"),
    ("autoshape_flowchart_or", b"\x00j\x00N"),
    ("autoshape_flowchart_collate", b"\x00j\x00O"),
    ("autoshape_flowchart_sort", b"\x00j\x00P"),
    ("autoshape_flowchart_extract", b"\x00j\x00Q"),
    ("autoshape_flowchart_merge", b"\x00j\x00R"),
    ("autoshape_flowchart_stored_data", b"\x00j\x00S"),
    ("autoshape_flowchart_delay", b"\x00j\x00T"),
    ("autoshape_flowchart_sequential_access_storage", b"\x00j\x00U"),
    ("autoshape_flowchart_magnetic_disk", b"\x00j\x00V"),
    ("autoshape_flowchart_direct_access_storage", b"\x00j\x00W"),
    ("autoshape_flowchart_display", b"\x00j\x00X"),
    ("autoshape_explosion_one", b"\x00j\x00Y"),
    ("autoshape_explosion_two", b"\x00j\x00Z"),
    ("autoshape_four_point_star", b"\x00j\x00["),
    ("autoshape_five_point_star", b"\x00j\x00\\"),
    ("autoshape_eight_point_star", b"\x00j\x00]"),
    ("autoshape_sixteen_point_star", b"\x00j\x00^"),
    ("autoshape_twenty_four_point_star", b"\x00j\x00_"),
    ("autoshape_thirty_two_point_star", b"\x00j\x00`"),
    ("autoshape_up_ribbon", b"\x00j\x00a"),
    ("autoshape_down_ribbon", b"\x00j\x00b"),
    ("autoshape_curved_up_ribbon", b"\x00j\x00c"),
    ("autoshape_curved_down_ribbon", b"\x00j\x00d"),
    ("autoshape_vertical_scroll", b"\x00j\x00e"),
    ("autoshape_horizontal_scroll", b"\x00j\x00f"),
    ("autoshape_wave", b"\x00j\x00g"),
    ("autoshape_double_wave", b"\x00j\x00h"),
    ("autoshape_rectangular_callout", b"\x00j\x00i"),
    ("autoshape_rounded_rectangular_callout", b"\x00j\x00j"),
    ("autoshape_oval_callout", b"\x00j\x00k"),
    ("autoshape_cloud_callout", b"\x00j\x00l"),
    ("autoshape_line_callout_one", b"\x00j\x00m"),
    ("autoshape_line_callout_two", b"\x00j\x00n"),
    ("autoshape_line_callout_three", b"\x00j\x00o"),
    ("autoshape_line_callout_four", b"\x00j\x00p"),
    ("autoshape_line_callout_one_accent_bar", b"\x00j\x00q"),
    ("autoshape_line_callout_two_accent_bar", b"\x00j\x00r"),
    ("autoshape_line_callout_three_accent_bar", b"\x00j\x00s"),
    ("autoshape_line_callout_four_accent_bar", b"\x00j\x00t"),
    ("autoshape_line_callout_one_no_border", b"\x00j\x00u"),
    ("autoshape_line_callout_two_no_border", b"\x00j\x00v"),
    ("autoshape_line_callout_three_no_border", b"\x00j\x00w"),
    ("autoshape_line_callout_four_no_border", b"\x00j\x00x"),
    ("autoshape_callout_one_border_and_accent_bar", b"\x00j\x00y"),
    ("autoshape_callout_two_border_and_accent_bar", b"\x00j\x00z"),
    ("autoshape_callout_three_border_and_accent_bar", b"\x00j\x00{"),
    ("autoshape_callout_four_border_and_accent_bar", b"\x00j\x00|"),
    ("autoshape_action_button_custom", b"\x00j\x00}"),
    ("autoshape_action_button_home", b"\x00j\x00~"),
    ("autoshape_action_button_help", b"\x00j\x00\x7f"),
    ("autoshape_action_button_information", b"\x00j\x00\x80"),
    ("autoshape_action_button_back_or_previous", b"\x00j\x00\x81"),
    ("autoshape_action_button_forward_or_next", b"\x00j\x00\x82"),
    ("autoshape_action_button_beginning", b"\x00j\x00\x83"),
    ("autoshape_action_button_end", b"\x00j\x00\x84"),
    ("autoshape_action_button_return", b"\x00j\x00\x85"),
    ("autoshape_action_button_document", b"\x00j\x00\x86"),
    ("autoshape_action_button_sound", b"\x00j\x00\x87"),
    ("autoshape_action_button_movie", b"\x00j\x00\x88"),
    ("autoshape_balloon", b"\x00j\x00\x89"),
    ("autoshape_not_primitive", b"\x00j\x00\x8a"),
    ("autoshape_flowchart_offline_storage", b"\x00j\x00\x8b"),
    ("autoshape_left_right_ribbon", b"\x00j\x00\x8c"),
    ("autoshape_diagonal_stripe", b"\x00j\x00\x8d"),
    ("autoshape_pie", b"\x00j\x00\x8e"),
    ("autoshape_non_isosceles_trapezoid", b"\x00j\x00\x8f"),
    ("autoshape_Decagon", b"\x00j\x00\x90"),
    ("autoshape_Heptagon", b"\x00j\x00\x91"),
    ("autoshape_Dodecagon", b"\x00j\x00\x92"),
    ("autoshape_six_points_star", b"\x00j\x00\x93"),
    ("autoshape_seven_points_star", b"\x00j\x00\x94"),
    ("autoshape_ten_points_star", b"\x00j\x00\x95"),
    ("autoshape_twelve_points_star", b"\x00j\x00\x96"),
    ("autoshape_round_one_rectangle", b"\x00j\x00\x97"),
    ("autoshape_round_two_same_rectangle", b"\x00j\x00\x98"),
    ("autoshape_round_two_diagonal_rectangle", b"\x00j\x00\x99"),
    ("autoshape_snip_round_rectangle", b"\x00j\x00\x9a"),
    ("autoshape_snip_one_rectangle", b"\x00j\x00\x9b"),
    ("autoshape_snip_two_same_rectangle", b"\x00j\x00\x9c"),
    ("autoshape_snip_two_diagonal_rectangle", b"\x00j\x00\x9d"),
    ("autoshape_frame", b"\x00j\x00\x9e"),
    ("autoshape_half_frame", b"\x00j\x00\x9f"),
    ("autoshape_tear", b"\x00j\x00\xa0"),
    ("autoshape_chord", b"\x00j\x00\xa1"),
    ("autoshape_corner", b"\x00j\x00\xa2"),
    ("autoshape_math_plus", b"\x00j\x00\xa3"),
    ("autoshape_math_minus", b"\x00j\x00\xa4"),
    ("autoshape_math_multiply", b"\x00j\x00\xa5"),
    ("autoshape_math_divide", b"\x00j\x00\xa6"),
    ("autoshape_math_equal", b"\x00j\x00\xa7"),
    ("autoshape_math_not_equal", b"\x00j\x00\xa8"),
    ("autoshape_corner_tabs", b"\x00j\x00\xa9"),
    ("autoshape_square_tabs", b"\x00j\x00\xaa"),
    ("autoshape_plaque_tabs", b"\x00j\x00\xab"),
    ("autoshape_gear_six", b"\x00j\x00\xac"),
    ("autoshape_gear_nine", b"\x00j\x00\xad"),
    ("autoshape_funnel", b"\x00j\x00\xae"),
    ("autoshape_pie_wedge", b"\x00j\x00\xaf"),
    ("autoshape_left_circular_arrow", b"\x00j\x00\xb0"),
    ("autoshape_left_right_circular_arrow", b"\x00j\x00\xb1"),
    ("autoshape_swoosh_arrow", b"\x00j\x00\xb2"),
    ("autoshape_cloud", b"\x00j\x00\xb3"),
    ("autoshape_chart_x", b"\x00j\x00\xb4"),
    ("autoshape_chart_star", b"\x00j\x00\xb5"),
    ("autoshape_chart_plus", b"\x00j\x00\xb6"),
    ("autoshape_line_inverse", b"\x00j\x00\xb7"),
    ("shape_type_unset", b"\x00\x8b\xff\xfe"),
    ("shape_type_auto", b"\x00\x8c\x00\x01"),
    ("shape_type_callout", b"\x00\x8c\x00\x02"),
    ("shape_type_chart", b"\x00\x8c\x00\x03"),
    ("shape_type_comment", b"\x00\x8c\x00\x04"),
    ("shape_type_free_form", b"\x00\x8c\x00\x05"),
    ("shape_type_group", b"\x00\x8c\x00\x06"),
    ("shape_type_embedded_OLE_control", b"\x00\x8c\x00\x07"),
    ("shape_type_form_control", b"\x00\x8c\x00\x08"),
    ("shape_type_line", b"\x00\x8c\x00\t"),
    ("shape_type_linked_OLE_object", b"\x00\x8c\x00\n"),
    ("shape_type_linked_picture", b"\x00\x8c\x00\x0b"),
    ("shape_type_OLE_control", b"\x00\x8c\x00\x0c"),
    ("shape_type_picture", b"\x00\x8c\x00\r"),
    ("shape_type_place_holder", b"\x00\x8c\x00\x0e"),
    ("shape_type_word_art", b"\x00\x8c\x00\x0f"),
    ("shape_type_media", b"\x00\x8c\x00\x10"),
    ("shape_type_text_box", b"\x00\x8c\x00\x11"),
    ("shape_type_script_anchor", b"\x00\x8c\x00\x12"),
    ("shape_type_table", b"\x00\x8c\x00\x13"),
    ("shape_type_canvas", b"\x00\x8c\x00\x14"),
    ("shape_type_diagram", b"\x00\x8c\x00\x15"),
    ("shape_type_ink", b"\x00\x8c\x00\x16"),
    ("shape_type_ink_comment", b"\x00\x8c\x00\x17"),
    ("shape_type_smartart_graphic", b"\x00\x8c\x00\x18"),
    ("shape_type_slicer", b"\x00\x8c\x00\x19"),
    ("shape_type_web_video", b"\x00\x8c\x00\x1a"),
    ("shape_type_content_application", b"\x00\x8c\x00\x1b"),
    ("shape_type_graphic", b"\x00\x8c\x00\x1c"),
    ("shape_type_linked_graphic", b"\x00\x8c\x00\x1d"),
    ("shape_type_3d_model", b"\x00\x8c\x00\x1e"),
    ("shape_type_linked_3d_model", b"\x00\x8c\x00\x1f"),
    ("color_type_unset", b"\x00j\xff\xfe"),
    ("RGB", b"\x00k\x00\x01"),
    ("Scheme", b"\x00k\x00\x02"),
    ("picture_color_type_unset", b"\x00\xb5\xff\xfe"),
    ("picture_color_automatic", b"\x00\xb6\x00\x01"),
    ("picture_color_gray_scale", b"\x00\xb6\x00\x02"),
    ("picture_color_black_and_white", b"\x00\xb6\x00\x03"),
    ("picture_color_watermark", b"\x00\xb6\x00\x04"),
    ("angle_unset", b"\x00k\xff\xfe"),
    ("angle_automatic", b"\x00l\x00\x01"),
    ("angle30", b"\x00l\x00\x02"),
    ("angle45", b"\x00l\x00\x03"),
    ("angle60", b"\x00l\x00\x04"),
    ("angle90", b"\x00l\x00\x05"),
    ("drop_unset", b"\x00l\xff\xfe"),
    ("drop_custom", b"\x00m\x00\x01"),
    ("drop_top", b"\x00m\x00\x02"),
    ("drop_center", b"\x00m\x00\x03"),
    ("drop_bottom", b"\x00m\x00\x04"),
    ("callout_unset", b"\x00m\xff\xfe"),
    ("callout_one", b"\x00n\x00\x01"),
    ("callout_two", b"\x00n\x00\x02"),
    ("callout_three", b"\x00n\x00\x03"),
    ("callout_four", b"\x00n\x00\x04"),
    ("text_orientation_unset", b"\x00\x8d\xff\xfe"),
    ("horizontal", b"\x00\x8e\x00\x01"),
    ("upward", b"\x00\x8e\x00\x02"),
    ("downward", b"\x00\x8e\x00\x03"),
    ("vertical_east_asian", b"\x00\x8e\x00\x04"),
    ("vertical", b"\x00\x8e\x00\x05"),
    ("horizontal_rotated_east_asian", b"\x00\x8e\x00\x06"),
    ("scale_from_top_left", b"\x00o\x00\x00"),
    ("scale_from_middle", b"\x00o\x00\x01"),
    ("scale_from_bottom_right", b"\x00o\x00\x02"),
    ("preset_camera_unset", b"\x00\xae\xff\xfe"),
    ("camera_legacy_oblique_from_top_left", b"\x00\xaf\x00\x01"),
    ("camera_legacy_oblique_from_top", b"\x00\xaf\x00\x02"),
    ("camera_legacy_oblique_from_topright", b"\x00\xaf\x00\x03"),
    ("camera_legacy_oblique_from_left", b"\x00\xaf\x00\x04"),
    ("camera_legacy_oblique_from_front", b"\x00\xaf\x00\x05"),
    ("camera_legacy_oblique_from_right", b"\x00\xaf\x00\x06"),
    ("camera_legacy_oblique_from_bottom_left", b"\x00\xaf\x00\x07"),
    ("camera_legacy_oblique_from_bottom", b"\x00\xaf\x00\x08"),
    ("camera_legacy_oblique_from_bottom_right", b"\x00\xaf\x00\t"),
    ("camera_legacy_perspective_from_top_left", b"\x00\xaf\x00\n"),
    ("camera_legacy_perspective_from_top", b"\x00\xaf\x00\x0b"),
    ("camera_legacy_perspective_from_top_right", b"\x00\xaf\x00\x0c"),
    ("camera_legacy_perspective_from_left", b"\x00\xaf\x00\r"),
    ("camera_legacy_perspective_from_front", b"\x00\xaf\x00\x0e"),
    ("camera_legacy_perspective_from_right", b"\x00\xaf\x00\x0f"),
    ("camera_legacy_perspective_from_bottom_left", b"\x00\xaf\x00\x10"),
    ("camera_legacy_perspective_from_bottom", b"\x00\xaf\x00\x11"),
    ("camera_legacy_perspective_from_bottom_right", b"\x00\xaf\x00\x12"),
    ("camera_orthographic", b"\x00\xaf\x00\x13"),
    ("camera_isometric_from_top_up", b"\x00\xaf\x00\x14"),
    ("camera_isometric_from_top_down", b"\x00\xaf\x00\x15"),
    ("camera_isometric_from_bottom_up", b"\x00\xaf\x00\x16"),
    ("camera_isometric_from_bottom_down", b"\x00\xaf\x00\x17"),
    ("camera_isometric_from_left_up", b"\x00\xaf\x00\x18"),
    ("camera_isometric_from_left_down", b"\x00\xaf\x00\x19"),
    ("camera_isometric_from_right_up", b"\x00\xaf\x00\x1a"),
    ("camera_isometric_from_right_down", b"\x00\xaf\x00\x1b"),
    ("camera_isometric_off_axis1_from_left", b"\x00\xaf\x00\x1c"),
    ("camera_isometric_off_axis1_from_right", b"\x00\xaf\x00\x1d"),
    ("camera_isometric_off_axis1_from_top", b"\x00\xaf\x00\x1e"),
    ("camera_isometric_off_axis2_from_left", b"\x00\xaf\x00\x1f"),
    ("camera_isometric_off_axis2_from_right", b"\x00\xaf\x00 "),
    ("camera_isometric_off_axis2_from_top", b"\x00\xaf\x00!"),
    ("camera_isometric_off_axis3_from_left", b'\x00\xaf\x00"'),
    ("camera_isometric_off_axis3_from_right", b"\x00\xaf\x00#"),
    ("camera_isometric_off_axis3_from_bottom", b"\x00\xaf\x00$"),
    ("camera_isometric_off_axis4_from_left", b"\x00\xaf\x00%"),
    ("camera_isometric_off_axis4_from_right", b"\x00\xaf\x00&"),
    ("camera_isometric_off_axis4_from_bottom", b"\x00\xaf\x00'"),
    ("camera_oblique_from_top_left", b"\x00\xaf\x00("),
    ("camera_oblique_from_top", b"\x00\xaf\x00)"),
    ("camera_oblique_from_top_right", b"\x00\xaf\x00*"),
    ("camera_oblique_from_left", b"\x00\xaf\x00+"),
    ("camera_oblique_from_right", b"\x00\xaf\x00,"),
    ("camera_oblique_from_bottom_left", b"\x00\xaf\x00-"),
    ("camera_oblique_from_bottom", b"\x00\xaf\x00."),
    ("camera_oblique_from_bottom_right", b"\x00\xaf\x00/"),
    ("camera_perspective_from_front", b"\x00\xaf\x000"),
    ("camera_perspective_from_left", b"\x00\xaf\x001"),
    ("camera_perspective_from_right", b"\x00\xaf\x002"),
    ("camera_perspective_from_above", b"\x00\xaf\x003"),
    ("camera_perspective_from_below", b"\x00\xaf\x004"),
    ("camera_perspective_from_above_facing_left", b"\x00\xaf\x005"),
    ("camera_perspective_from_above_facing_right", b"\x00\xaf\x006"),
    ("camera_perspective_contrasting_facing_left", b"\x00\xaf\x007"),
    ("camera_perspective_contrasting_facing_right", b"\x00\xaf\x008"),
    ("camera_perspective_heroic_facing_left", b"\x00\xaf\x009"),
    ("camera_perspective_heroic_facing_right", b"\x00\xaf\x00:"),
    ("camera_perspective_heroic_extreme_facing_left", b"\x00\xaf\x00;"),
    ("camera_perspective_heroic_extreme_facing_right", b"\x00\xaf\x00<"),
    ("camera_perspective_relaxed", b"\x00\xaf\x00="),
    ("camera_perspective_relaxed_moderately", b"\x00\xaf\x00>"),
    ("light_rig_unset", b"\x00\xaf\xff\xfe"),
    ("light_rig_flat1", b"\x00\xb0\x00\x01"),
    ("light_rig_flat2", b"\x00\xb0\x00\x02"),
    ("light_rig_flat3", b"\x00\xb0\x00\x03"),
    ("light_rig_flat4", b"\x00\xb0\x00\x04"),
    ("light_rig_Normal1", b"\x00\xb0\x00\x05"),
    ("light_rig_Normal2", b"\x00\xb0\x00\x06"),
    ("light_rig_Normal3", b"\x00\xb0\x00\x07"),
    ("light_rig_Normal4", b"\x00\xb0\x00\x08"),
    ("light_rig_Harsh1", b"\x00\xb0\x00\t"),
    ("light_rig_Harsh2", b"\x00\xb0\x00\n"),
    ("light_rig_Harsh3", b"\x00\xb0\x00\x0b"),
    ("light_rig_Harsh4", b"\x00\xb0\x00\x0c"),
    ("light_rig_three_point", b"\x00\xb0\x00\r"),
    ("light_rig_balanced", b"\x00\xb0\x00\x0e"),
    ("light_rig_soft", b"\x00\xb0\x00\x0f"),
    ("light_rig_harsh", b"\x00\xb0\x00\x10"),
    ("light_rig_flood", b"\x00\xb0\x00\x11"),
    ("light_rig_contrasting", b"\x00\xb0\x00\x12"),
    ("light_rig_morning", b"\x00\xb0\x00\x13"),
    ("light_rig_sunrise", b"\x00\xb0\x00\x14"),
    ("light_rig_sunset", b"\x00\xb0\x00\x15"),
    ("light_rig_chilly", b"\x00\xb0\x00\x16"),
    ("light_rig_freezing", b"\x00\xb0\x00\x17"),
    ("light_rig_flat", b"\x00\xb0\x00\x18"),
    ("light_rig_two_point", b"\x00\xb0\x00\x19"),
    ("light_rig_glow", b"\x00\xb0\x00\x1a"),
    ("light_rig_bright_room", b"\x00\xb0\x00\x1b"),
    ("bevel_type_unset", b"\x00\xb0\xff\xfe"),
    ("bevel_none", b"\x00\xb1\x00\x01"),
    ("bevel_relaxed_inset", b"\x00\xb1\x00\x02"),
    ("bevel_circle", b"\x00\xb1\x00\x03"),
    ("bevel_slope", b"\x00\xb1\x00\x04"),
    ("bevel_cross", b"\x00\xb1\x00\x05"),
    ("bevel_angle", b"\x00\xb1\x00\x06"),
    ("bevel_soft_round", b"\x00\xb1\x00\x07"),
    ("bevel_convex", b"\x00\xb1\x00\x08"),
    ("bevel_cool_slant", b"\x00\xb1\x00\t"),
    ("bevel_divot", b"\x00\xb1\x00\n"),
    ("bevel_riblet", b"\x00\xb1\x00\x0b"),
    ("bevel_hard_edge", b"\x00\xb1\x00\x0c"),
    ("bevel_art_deco", b"\x00\xb1\x00\r"),
    ("shadow_style_unset", b"\x00\xb1\xff\xfe"),
    ("shadow_style_inner", b"\x00\xb2\x00\x01"),
    ("shadow_style_outer", b"\x00\xb2\x00\x02"),
    ("paragraph_alignment_unset", b"\x00\xe6\xff\xfe"),
    ("paragraph_align_left", b"\x00\xe7\x00\x00"),
    ("paragraph_align_center", b"\x00\xe7\x00\x01"),
    ("paragraph_align_right", b"\x00\xe7\x00\x02"),
    ("paragraph_align_justify", b"\x00\xe7\x00\x03"),
    ("paragraph_align_distribute", b"\x00\xe7\x00\x04"),
    ("paragraph_align_Thai", b"\x00\xe7\x00\x05"),
    ("paragraph_align_justify_low", b"\x00\xe7\x00\x06"),
    ("strike_unset", b"\x00\xb3\xff\xfe"),
    ("no_strike", b"\x00\xb4\x00\x00"),
    ("single_strike", b"\x00\xb4\x00\x01"),
    ("double_strike", b"\x00\xb4\x00\x02"),
    ("caps_unset", b"\x00\xb4\xff\xfe"),
    ("no_caps", b"\x00\xb5\x00\x00"),
    ("small_caps", b"\x00\xb5\x00\x01"),
    ("all_caps", b"\x00\xb5\x00\x02"),
    ("underline_unset", b"\x03\xee\xff\xfe"),
    ("no_underline", b"\x03\xef\x00\x00"),
    ("underline_words_only", b"\x03\xef\x00\x01"),
    ("underline_single_line", b"\x03\xef\x00\x02"),
    ("underline_double_line", b"\x03\xef\x00\x03"),
    ("underline_heavy_line", b"\x03\xef\x00\x04"),
    ("underline_dotted_line", b"\x03\xef\x00\x05"),
    ("underline_heavy_dotted_line", b"\x03\xef\x00\x06"),
    ("underline_dash_line", b"\x03\xef\x00\x07"),
    ("underline_heavy_dash_line", b"\x03\xef\x00\x08"),
    ("underline_long_dash_line", b"\x03\xef\x00\t"),
    ("underline_heavy_long_dash_line", b"\x03\xef\x00\n"),
    ("underline_dot_dash_line", b"\x03\xef\x00\x0b"),
    ("underline_heavy_dot_dash_line", b"\x03\xef\x00\x0c"),
    ("underline_dot_dot_dash_line", b"\x03\xef\x00\r"),
    ("underline_heavy_dot_dot_dash_line", b"\x03\xef\x00\x0e"),
    ("underline_wavy_line", b"\x03\xef\x00\x0f"),
    ("underline_heavy_wavy_line", b"\x03\xef\x00\x10"),
    ("underline_wavy_double_line", b"\x03\xef\x00\x11"),
    ("tab_unset", b"\x00\xb6\xff\xfe"),
    ("left_tab", b"\x00\xb7\x00\x00"),
    ("center_tab", b"\x00\xb7\x00\x01"),
    ("right_tab", b"\x00\xb7\x00\x02"),
    ("decimal_tab", b"\x00\xb7\x00\x03"),
    ("character_wrap_unset", b"\x00\xb7\xff\xfe"),
    ("no_character_wrap", b"\x00\xb8\x00\x00"),
    ("standard_character_wrap", b"\x00\xb8\x00\x01"),
    ("strict_character_wrap", b"\x00\xb8\x00\x02"),
    ("custom_character_wrap", b"\x00\xb8\x00\x03"),
    ("font_alignment_unset", b"\x00\xb8\xff\xfe"),
    ("automatic_alignment", b"\x00\xb9\x00\x00"),
    ("top_alignment", b"\x00\xb9\x00\x01"),
    ("center_alignment", b"\x00\xb9\x00\x02"),
    ("baseline_alignment", b"\x00\xb9\x00\x03"),
    ("bottom_alignment", b"\x00\xb9\x00\x04"),
    ("auto_size_unset", b"\x00\xe4\xff\xfe"),
    ("auto_size_none", b"\x00\xe5\x00\x00"),
    ("shape_to_fit_text", b"\x00\xe5\x00\x01"),
    ("text_to_fit_shape", b"\x00\xe5\x00\x02"),
    ("path_type_unset", b"\x00\xba\xff\xfe"),
    ("no_path_type", b"\x00\xbb\x00\x00"),
    ("path_type1", b"\x00\xbb\x00\x01"),
    ("path_type2", b"\x00\xbb\x00\x02"),
    ("path_type3", b"\x00\xbb\x00\x03"),
    ("path_type4", b"\x00\xbb\x00\x04"),
    ("warp_format_unset", b"\x00\xbb\xff\xfe"),
    ("warp_format1", b"\x00\xbc\x00\x00"),
    ("warp_format2", b"\x00\xbc\x00\x01"),
    ("warp_format3", b"\x00\xbc\x00\x02"),
    ("warp_format4", b"\x00\xbc\x00\x03"),
    ("warp_format5", b"\x00\xbc\x00\x04"),
    ("warp_format6", b"\x00\xbc\x00\x05"),
    ("warp_format7", b"\x00\xbc\x00\x06"),
    ("warp_format8", b"\x00\xbc\x00\x07"),
    ("warp_format9", b"\x00\xbc\x00\x08"),
    ("warp_format10", b"\x00\xbc\x00\t"),
    ("warp_format11", b"\x00\xbc\x00\n"),
    ("warp_format12", b"\x00\xbc\x00\x0b"),
    ("warp_format13", b"\x00\xbc\x00\x0c"),
    ("warp_format14", b"\x00\xbc\x00\r"),
    ("warp_format15", b"\x00\xbc\x00\x0e"),
    ("warp_format16", b"\x00\xbc\x00\x0f"),
    ("warp_format17", b"\x00\xbc\x00\x10"),
    ("warp_format18", b"\x00\xbc\x00\x11"),
    ("warp_format19", b"\x00\xbc\x00\x12"),
    ("warp_format20", b"\x00\xbc\x00\x13"),
    ("warp_format21", b"\x00\xbc\x00\x14"),
    ("warp_format22", b"\x00\xbc\x00\x15"),
    ("warp_format23", b"\x00\xbc\x00\x16"),
    ("warp_format24", b"\x00\xbc\x00\x17"),
    ("warp_format25", b"\x00\xbc\x00\x18"),
    ("warp_format26", b"\x00\xbc\x00\x19"),
    ("warp_format27", b"\x00\xbc\x00\x1a"),
    ("warp_format28", b"\x00\xbc\x00\x1b"),
    ("warp_format29", b"\x00\xbc\x00\x1c"),
    ("warp_format30", b"\x00\xbc\x00\x1d"),
    ("warp_format31", b"\x00\xbc\x00\x1e"),
    ("warp_format32", b"\x00\xbc\x00\x1f"),
    ("warp_format33", b"\x00\xbc\x00 "),
    ("warp_format34", b"\x00\xbc\x00!"),
    ("warp_format35", b'\x00\xbc\x00"'),
    ("warp_format36", b"\x00\xbc\x00#"),
    ("case_sentence", b"\x00\xe4\x00\x01"),
    ("case_lower", b"\x00\xe4\x00\x02"),
    ("case_upper", b"\x00\xe4\x00\x03"),
    ("case_title", b"\x00\xe4\x00\x04"),
    ("case_toggle", b"\x00\xe4\x00\x05"),
    ("date_time_format_unset", b"\x00\xbd\xff\xfe"),
    ("date_time_format_Mdyy", b"\x00\xbe\x00\x01"),
    ("date_time_format_ddddMMMMddyyyy", b"\x00\xbe\x00\x02"),
    ("date_time_format_dMMMMyyyy", b"\x00\xbe\x00\x03"),
    ("date_time_format_MMMMdyyyy", b"\x00\xbe\x00\x04"),
    ("date_time_format_dMMMyy", b"\x00\xbe\x00\x05"),
    ("date_time_format_MMMMyy", b"\x00\xbe\x00\x06"),
    ("date_time_format_MMyy", b"\x00\xbe\x00\x07"),
    ("date_time_format_MMddyyHmm", b"\x00\xbe\x00\x08"),
    ("date_time_format_MMddyyhmmAMPM", b"\x00\xbe\x00\t"),
    ("date_time_format_Hmm", b"\x00\xbe\x00\n"),
    ("date_time_format_Hmmss", b"\x00\xbe\x00\x0b"),
    ("date_time_format_hmmAMPM", b"\x00\xbe\x00\x0c"),
    ("date_time_format_hmmssAMPM", b"\x00\xbe\x00\r"),
    ("date_time_format_figure_out", b"\x00\xbe\x00\x0e"),
    ("soft_edge_unset", b"\x00\xbf\xff\xfe"),
    ("no_soft_edge", b"\x00\xc0\x00\x00"),
    ("soft_edge_type1", b"\x00\xc0\x00\x01"),
    ("soft_edge_type2", b"\x00\xc0\x00\x02"),
    ("soft_edge_type3", b"\x00\xc0\x00\x03"),
    ("soft_edge_type4", b"\x00\xc0\x00\x04"),
    ("soft_edge_type5", b"\x00\xc0\x00\x05"),
    ("soft_edge_type6", b"\x00\xc0\x00\x06"),
    ("first_dark_scheme_color", b"\x00\xc1\x00\x01"),
    ("first_light_scheme_color", b"\x00\xc1\x00\x02"),
    ("second_dark_scheme_color", b"\x00\xc1\x00\x03"),
    ("second_light_scheme_color", b"\x00\xc1\x00\x04"),
    ("first_accent_scheme_color", b"\x00\xc1\x00\x05"),
    ("second_accent_scheme_color", b"\x00\xc1\x00\x06"),
    ("third_accent_scheme_color", b"\x00\xc1\x00\x07"),
    ("fourth_accent_scheme_color", b"\x00\xc1\x00\x08"),
    ("fifth_accent_scheme_color", b"\x00\xc1\x00\t"),
    ("sixth_accent_scheme_color", b"\x00\xc1\x00\n"),
    ("hyperlink_scheme_color", b"\x00\xc1\x00\x0b"),
    ("followed_hyperlink_scheme_color", b"\x00\xc1\x00\x0c"),
    ("theme_color_unset", b"\x00\xc1\xff\xfe"),
    ("no_theme_color", b"\x00\xc2\x00\x00"),
    ("first_dark_theme_color", b"\x00\xc2\x00\x01"),
    ("first_light_theme_color", b"\x00\xc2\x00\x02"),
    ("second_dark_theme_color", b"\x00\xc2\x00\x03"),
    ("second_light_theme_color", b"\x00\xc2\x00\x04"),
    ("first_accent_theme_color", b"\x00\xc2\x00\x05"),
    ("second_accent_theme_color", b"\x00\xc2\x00\x06"),
    ("third_accent_theme_color", b"\x00\xc2\x00\x07"),
    ("fourth_accent_theme_color", b"\x00\xc2\x00\x08"),
    ("fifth_accent_theme_color", b"\x00\xc2\x00\t"),
    ("sixth_accent_theme_color", b"\x00\xc2\x00\n"),
    ("hyperlink_theme_color", b"\x00\xc2\x00\x0b"),
    ("followed_hyperlink_theme_color", b"\x00\xc2\x00\x0c"),
    ("first_text_theme_color", b"\x00\xc2\x00\r"),
    ("first_background_theme_color", b"\x00\xc2\x00\x0e"),
    ("second_text_theme_color", b"\x00\xc2\x00\x0f"),
    ("second_background_theme_color", b"\x00\xc2\x00\x10"),
    ("theme_font_latin", b"\x00\xc3\x00\x01"),
    ("theme_font_complex_script", b"\x00\xc3\x00\x02"),
    ("theme_font_high_ansi", b"\x00\xc3\x00\x03"),
    ("theme_font_east_asian", b"\x00\xc3\x00\x04"),
    ("shape_style_unset", b"\x00\xc3\xff\xfe"),
    ("shape_not_a_preset", b"\x00\xc4\x00\x00"),
    ("shape_preset1", b"\x00\xc4\x00\x01"),
    ("shape_preset2", b"\x00\xc4\x00\x02"),
    ("shape_preset3", b"\x00\xc4\x00\x03"),
    ("shape_preset4", b"\x00\xc4\x00\x04"),
    ("shape_preset5", b"\x00\xc4\x00\x05"),
    ("shape_preset6", b"\x00\xc4\x00\x06"),
    ("shape_preset7", b"\x00\xc4\x00\x07"),
    ("shape_preset8", b"\x00\xc4\x00\x08"),
    ("shape_preset9", b"\x00\xc4\x00\t"),
    ("shape_preset10", b"\x00\xc4\x00\n"),
    ("shape_preset11", b"\x00\xc4\x00\x0b"),
    ("shape_preset12", b"\x00\xc4\x00\x0c"),
    ("shape_preset13", b"\x00\xc4\x00\r"),
    ("shape_preset14", b"\x00\xc4\x00\x0e"),
    ("shape_preset15", b"\x00\xc4\x00\x0f"),
    ("shape_preset16", b"\x00\xc4\x00\x10"),
    ("shape_preset17", b"\x00\xc4\x00\x11"),
    ("shape_preset18", b"\x00\xc4\x00\x12"),
    ("shape_preset19", b"\x00\xc4\x00\x13"),
    ("shape_preset20", b"\x00\xc4\x00\x14"),
    ("shape_preset21", b"\x00\xc4\x00\x15"),
    ("shape_preset22", b"\x00\xc4\x00\x16"),
    ("shape_preset23", b"\x00\xc4\x00\x17"),
    ("shape_preset24", b"\x00\xc4\x00\x18"),
    ("shape_preset25", b"\x00\xc4\x00\x19"),
    ("shape_preset26", b"\x00\xc4\x00\x1a"),
    ("shape_preset27", b"\x00\xc4\x00\x1b"),
    ("shape_preset28", b"\x00\xc4\x00\x1c"),
    ("shape_preset29", b"\x00\xc4\x00\x1d"),
    ("shape_preset30", b"\x00\xc4\x00\x1e"),
    ("shape_preset31", b"\x00\xc4\x00\x1f"),
    ("shape_preset32", b"\x00\xc4\x00 "),
    ("shape_preset33", b"\x00\xc4\x00!"),
    ("shape_preset34", b'\x00\xc4\x00"'),
    ("shape_preset35", b"\x00\xc4\x00#"),
    ("shape_preset36", b"\x00\xc4\x00$"),
    ("shape_preset37", b"\x00\xc4\x00%"),
    ("shape_preset38", b"\x00\xc4\x00&"),
    ("shape_preset39", b"\x00\xc4\x00'"),
    ("shape_preset40", b"\x00\xc4\x00("),
    ("shape_preset41", b"\x00\xc4\x00)"),
    ("shape_preset42", b"\x00\xc4\x00*"),
    ("line_preset1", b"\x00\xc4'\x11"),
    ("line_preset2", b"\x00\xc4'\x12"),
    ("line_preset3", b"\x00\xc4'\x13"),
    ("line_preset4", b"\x00\xc4'\x14"),
    ("line_preset5", b"\x00\xc4'\x15"),
    ("line_preset6", b"\x00\xc4'\x16"),
    ("line_preset7", b"\x00\xc4'\x17"),
    ("line_preset8", b"\x00\xc4'\x18"),
    ("line_preset9", b"\x00\xc4'\x19"),
    ("line_preset10", b"\x00\xc4'\x1a"),
    ("line_preset11", b"\x00\xc4'\x1b"),
    ("line_preset12", b"\x00\xc4'\x1c"),
    ("line_preset13", b"\x00\xc4'\x1d"),
    ("line_preset14", b"\x00\xc4'\x1e"),
    ("line_preset15", b"\x00\xc4'\x1f"),
    ("line_preset16", b"\x00\xc4' "),
    ("line_preset17", b"\x00\xc4'!"),
    ("line_preset18", b"\x00\xc4'\""),
    ("line_preset19", b"\x00\xc4'#"),
    ("line_preset20", b"\x00\xc4'$"),
    ("line_preset21", b"\x00\xc4'%"),
    ("background_unset", b"\x00\xc4\xff\xfe"),
    ("background_not_a_preset", b"\x00\xc5\x00\x00"),
    ("background_preset1", b"\x00\xc5\x00\x01"),
    ("background_preset2", b"\x00\xc5\x00\x02"),
    ("background_preset3", b"\x00\xc5\x00\x03"),
    ("background_preset4", b"\x00\xc5\x00\x04"),
    ("background_preset5", b"\x00\xc5\x00\x05"),
    ("background_preset6", b"\x00\xc5\x00\x06"),
    ("background_preset7", b"\x00\xc5\x00\x07"),
    ("background_preset8", b"\x00\xc5\x00\x08"),
    ("background_preset9", b"\x00\xc5\x00\t"),
    ("background_preset10", b"\x00\xc5\x00\n"),
    ("background_preset11", b"\x00\xc5\x00\x0b"),
    ("background_preset12", b"\x00\xc5\x00\x0c"),
    ("text_direction_unset", b"\x00\xea\xff\xfe"),
    ("left_to_right", b"\x00\xeb\x00\x01"),
    ("right_to_left", b"\x00\xeb\x00\x02"),
    ("bullet_type_unset", b"\x00\xe7\xff\xfe"),
    ("bullet_type_none", b"\x00\xe8\x00\x00"),
    ("bullet_type_unnumbered", b"\x00\xe8\x00\x01"),
    ("bullet_type_numbered", b"\x00\xe8\x00\x02"),
    ("picture_bullet_type", b"\x00\xe8\x00\x03"),
    ("numbered_bullet_style_unset", b"\x00\xe8\xff\xfe"),
    ("numbered_bullet_style_alpha_lowercase_period", b"\x00\xe9\x00\x00"),
    ("numbered_bullet_style_alpha_uppercase_period", b"\x00\xe9\x00\x01"),
    ("numbered_bullet_style_arabic_right_paren", b"\x00\xe9\x00\x02"),
    ("numbered_bullet_style_arabic_period", b"\x00\xe9\x00\x03"),
    ("numbered_bullet_style_roman_lowercase_paren_both", b"\x00\xe9\x00\x04"),
    ("numbered_bullet_style_roman_lowercase_paren_right", b"\x00\xe9\x00\x05"),
    ("numbered_bullet_style_roman_lowercase_period", b"\x00\xe9\x00\x06"),
    ("numbered_bullet_style_roman_uppercase_period", b"\x00\xe9\x00\x07"),
    ("numbered_bullet_style_alpha_lowercase_paren_both", b"\x00\xe9\x00\x08"),
    ("numbered_bullet_style_alpha_lowercase_paren_right", b"\x00\xe9\x00\t"),
    ("numbered_bullet_style_alpha_uppercase_paren_both", b"\x00\xe9\x00\n"),
    ("numbered_bullet_style_alpha_uppercase_paren_right", b"\x00\xe9\x00\x0b"),
    ("numbered_bullet_style_arabic_paren_both", b"\x00\xe9\x00\x0c"),
    ("numbered_bullet_style_arabic_plain", b"\x00\xe9\x00\r"),
    ("numbered_bullet_style_roman_uppercase_paren_both", b"\x00\xe9\x00\x0e"),
    ("numbered_bullet_style_roman_uppercase_paren_right", b"\x00\xe9\x00\x0f"),
    ("numbered_bullet_style_simplified_chinese_plain", b"\x00\xe9\x00\x10"),
    ("numbered_bullet_style_simplified_chinese_period", b"\x00\xe9\x00\x11"),
    ("numbered_bullet_style_circle_number_plain", b"\x00\xe9\x00\x12"),
    ("numbered_bullet_style_circle_number_white_plain", b"\x00\xe9\x00\x13"),
    ("numbered_bullet_style_circle_number_black_plain", b"\x00\xe9\x00\x14"),
    ("numbered_bullet_style_traditional_chinese_plain", b"\x00\xe9\x00\x15"),
    ("numbered_bullet_style_traditional_chinese_period", b"\x00\xe9\x00\x16"),
    ("numbered_bullet_style_arabic_alpha_dash", b"\x00\xe9\x00\x17"),
    ("numbered_bullet_style_arabic_abjad_dash", b"\x00\xe9\x00\x18"),
    ("numbered_bullet_style_hebrew_alpha_dash", b"\x00\xe9\x00\x19"),
    ("numbered_bullet_style_kanji_korean_plain", b"\x00\xe9\x00\x1a"),
    ("numbered_bullet_style_kanji_korean_period", b"\x00\xe9\x00\x1b"),
    ("numbered_bullet_style_arabic_DB_plain", b"\x00\xe9\x00\x1c"),
    ("numbered_bullet_style_arabic_DB_period", b"\x00\xe9\x00\x1d"),
    ("numbered_bullet_style_thai_alpha_period", b"\x00\xe9\x00\x1e"),
    ("numbered_bullet_style_thai_alpha_paren_right", b"\x00\xe9\x00\x1f"),
    ("numbered_bullet_style_thai_alpha_paren_both", b"\x00\xe9\x00 "),
    ("numbered_bullet_style_thai_number_period", b"\x00\xe9\x00!"),
    ("numbered_bullet_style_thai_number_paren_right", b'\x00\xe9\x00"'),
    ("numbered_bullet_style_thai_paren_both", b"\x00\xe9\x00#"),
    ("numbered_bullet_style_hindi_alpha_period", b"\x00\xe9\x00$"),
    ("numbered_bullet_style_hindi_number_period", b"\x00\xe9\x00%"),
    ("numbered_bullet_style_kanji_simpified_chinese_DB_period", b"\x00\xe9\x00&"),
    ("numbered_bullet_style_hindi_number_paren_right", b"\x00\xe9\x00'"),
    ("numbered_bullet_style_hindi_alpha1_period", b"\x00\xe9\x00("),
    ("tabstop_unset", b"\x00\xf4\xff\xfe"),
    ("tabstop_left", b"\x00\xf5\x00\x01"),
    ("tabstop_center", b"\x00\xf5\x00\x02"),
    ("tabstop_right", b"\x00\xf5\x00\x03"),
    ("tabstop_decimal", b"\x00\xf5\x00\x04"),
    ("reflection_unset", b"\x03\xe9\xff\xfe"),
    ("reflection_type_none", b"\x03\xea\x00\x00"),
    ("reflection_type1", b"\x03\xea\x00\x01"),
    ("reflection_type2", b"\x03\xea\x00\x02"),
    ("reflection_type3", b"\x03\xea\x00\x03"),
    ("reflection_type4", b"\x03\xea\x00\x04"),
    ("reflection_type5", b"\x03\xea\x00\x05"),
    ("reflection_type6", b"\x03\xea\x00\x06"),
    ("reflection_type7", b"\x03\xea\x00\x07"),
    ("reflection_type8", b"\x03\xea\x00\x08"),
    ("reflection_type9", b"\x03\xea\x00\t"),
    ("texture_unset", b"\x03\xea\xff\xfe"),
    ("texture_top_left", b"\x03\xeb\x00\x00"),
    ("texture_top", b"\x03\xeb\x00\x01"),
    ("texture_top_right", b"\x03\xeb\x00\x02"),
    ("texture_left", b"\x03\xeb\x00\x03"),
    ("texture_center", b"\x03\xeb\x00\x04"),
    ("texture_right", b"\x03\xeb\x00\x05"),
    ("texture_bottom_left", b"\x03\xeb\x00\x06"),
    ("texture_botton", b"\x03\xeb\x00\x07"),
    ("texture_bottom_right", b"\x03\xeb\x00\x08"),
    ("text_baseline_alignment_unset", b"\x03\xeb\xff\xfe"),
    ("text_baseline_align_baseline", b"\x03\xec\x00\x01"),
    ("text_baseline_align_top", b"\x03\xec\x00\x02"),
    ("text_baseline_align_center", b"\x03\xec\x00\x03"),
    ("text_baseline_align_east_asian50", b"\x03\xec\x00\x04"),
    ("text_baseline_align_automatic", b"\x03\xec\x00\x05"),
    ("clipboard_format_unset", b"\x03\xec\xff\xfe"),
    ("native_clipboard_format", b"\x03\xed\x00\x01"),
    ("HTMl_clipboard_format", b"\x03\xed\x00\x02"),
    ("RTF_clipboard_format", b"\x03\xed\x00\x03"),
    ("plain_text_clipboard_format", b"\x03\xed\x00\x04"),
    ("insert_before", b"\x03\xee\x00\x00"),
    ("insert_after", b"\x03\xee\x00\x01"),
    ("save_as_default", b"\x03\xf2\xff\xfe"),
    ("save_as_PNG_file", b"\x03\xf3\x00\x00"),
    ("save_as_BMP_file", b"\x03\xf3\x00\x01"),
    ("save_as_GIF_file", b"\x03\xf3\x00\x02"),
    ("save_as_JPG_file", b"\x03\xf3\x00\x03"),
    ("save_as_PDF_file", b"\x03\xf3\x00\x04"),
    ("no_effect", b"\x03\xf4\x00\x00"),
    ("effect_background_removal", b"\x03\xf4\x00\x01"),
    ("effect_blur", b"\x03\xf4\x00\x02"),
    ("effect_brightness_contrast", b"\x03\xf4\x00\x03"),
    ("effect_cement", b"\x03\xf4\x00\x04"),
    ("effect_crisscross_etching", b"\x03\xf4\x00\x05"),
    ("effect_chalk_sketch", b"\x03\xf4\x00\x06"),
    ("effect_color_temperature", b"\x03\xf4\x00\x07"),
    ("effect_cutout", b"\x03\xf4\x00\x08"),
    ("effect_film_grain", b"\x03\xf4\x00\t"),
    ("effect_glass", b"\x03\xf4\x00\n"),
    ("effect_glow_diffused", b"\x03\xf4\x00\x0b"),
    ("effect_glow_edges", b"\x03\xf4\x00\x0c"),
    ("effect_light_screen", b"\x03\xf4\x00\r"),
    ("effect_line_drawing", b"\x03\xf4\x00\x0e"),
    ("effect_marker", b"\x03\xf4\x00\x0f"),
    ("effect_mosiaic_bubbles", b"\x03\xf4\x00\x10"),
    ("effect_paint_brush", b"\x03\xf4\x00\x11"),
    ("effect_paint_strokes", b"\x03\xf4\x00\x12"),
    ("effect_pastels_smooth", b"\x03\xf4\x00\x13"),
    ("effect_pencil_grayscale", b"\x03\xf4\x00\x14"),
    ("effect_pencil_sketch", b"\x03\xf4\x00\x15"),
    ("effect_photocopy", b"\x03\xf4\x00\x16"),
    ("effect_plastic_wrap", b"\x03\xf4\x00\x17"),
    ("effect_saturation", b"\x03\xf4\x00\x18"),
    ("effect_sharpen_soften", b"\x03\xf4\x00\x19"),
    ("effect_texturizer", b"\x03\xf4\x00\x1a"),
    ("effect_watercolor_sponge", b"\x03\xf4\x00\x1b"),
    ("line", b"\x00\x8f\x00\x00"),
    ("curve", b"\x00\x8f\x00\x01"),
    ("auto", b"\x00\x90\x00\x00"),
    ("corner", b"\x00\x90\x00\x01"),
    ("smooth", b"\x00\x90\x00\x02"),
    ("symmetric", b"\x00\x90\x00\x03"),
    ("default_node_position", b"\x03\xf5\x00\x01"),
    ("after_node", b"\x03\xf5\x00\x02"),
    ("before_node", b"\x03\xf5\x00\x03"),
    ("above_node", b"\x03\xf5\x00\x04"),
    ("below_node", b"\x03\xf5\x00\x05"),
    ("default_node", b"\x03\xf6\x00\x01"),
    ("assistant_node", b"\x03\xf6\x00\x02"),
    ("org_chart_layout_unset", b"\x03\xf6\xff\xfe"),
    ("org_chart_layout_standard", b"\x03\xf7\x00\x01"),
    ("org_chart_layout_both_hanging", b"\x03\xf7\x00\x02"),
    ("org_chart_layout_left_hanging", b"\x03\xf7\x00\x03"),
    ("org_chart_layout_right_hanging", b"\x03\xf7\x00\x04"),
    ("org_chart_layout_default", b"\x03\xf7\x00\x05"),
    ("align_lefts", b"\x00\x00\x00\x00"),
    ("align_centers", b"\x00\x00\x00\x01"),
    ("align_rights", b"\x00\x00\x00\x02"),
    ("align_tops", b"\x00\x00\x00\x03"),
    ("align_middles", b"\x00\x00\x00\x04"),
    ("align_bottoms", b"\x00\x00\x00\x05"),
    ("distribute_horizontally", b"\x00\x00\x00\x00"),
    ("distribute_vertically", b"\x00\x00\x00\x01"),
    ("orientation_unset", b"\x00\x8c\xff\xfe"),
    ("horizontal_orientation", b"\x00\x8d\x00\x01"),
    ("vertical_orientation", b"\x00\x8d\x00\x02"),
    ("bring_shape_to_front", b"\x00p\x00\x00"),
    ("send_shape_to_back", b"\x00p\x00\x01"),
    ("bring_shape_forward", b"\x00p\x00\x02"),
    ("send_shape_backward", b"\x00p\x00\x03"),
    ("bring_shape_in_front_of_text", b"\x00p\x00\x04"),
    ("send_shape_behind_text", b"\x00p\x00\x05"),
    ("flip_horizontal", b"\x00q\x00\x00"),
    ("flip_vertical", b"\x00q\x00\x01"),
    ("true", b"\x00\xa0\xff\xff"),
    ("false", b"\x00\xa1\x00\x00"),
    ("C_true", b"\x00\xa1\x00\x01"),
    ("toggle", b"\x00\xa0\xff\xfd"),
    ("tri_state_unset", b"\x00\xa0\xff\xfe"),
    ("black_and_white_unset", b"\x00\xac\xff\xfe"),
    ("black_and_white_mode_automatic", b"\x00\xad\x00\x01"),
    ("black_and_white_mode_gray_scale", b"\x00\xad\x00\x02"),
    ("black_and_white_mode_light_gray_scale", b"\x00\xad\x00\x03"),
    ("black_and_white_mode_inverse_gray_scale", b"\x00\xad\x00\x04"),
    ("black_and_white_mode_gray_outline", b"\x00\xad\x00\x05"),
    ("black_and_white_mode_black_text_and_line", b"\x00\xad\x00\x06"),
    ("black_and_white_mode_high_contrast", b"\x00\xad\x00\x07"),
    ("black_and_white_mode_black", b"\x00\xad\x00\x08"),
    ("black_and_white_mode_white", b"\x00\xad\x00\t"),
    ("black_and_white_mode_dont_show", b"\x00\xad\x00\n"),
    ("bar_left", b"\x00r\x00\x00"),
    ("bar_top", b"\x00r\x00\x01"),
    ("bar_right", b"\x00r\x00\x02"),
    ("bar_bottom", b"\x00r\x00\x03"),
    ("bar_floating", b"\x00r\x00\x04"),
    ("bar_pop_up", b"\x00r\x00\x05"),
    ("bar_menu", b"\x00r\x00\x06"),
    ("no_protection", b"\x00s\x00\x00"),
    ("no_customize", b"\x00s\x00\x01"),
    ("no_resize", b"\x00s\x00\x02"),
    ("no_move", b"\x00s\x00\x04"),
    ("no_change_visible", b"\x00s\x00\x08"),
    ("no_change_dock", b"\x00s\x00\x10"),
    ("no_vertical_dock", b"\x00s\x00 "),
    ("no_horizontal_dock", b"\x00s\x00@"),
    ("normal_command_bar", b"\x00t\x00\x00"),
    ("menubar_command_bar", b"\x00t\x00\x01"),
    ("popup_command_bar", b"\x00t\x00\x02"),
    ("control_custom", b"\x00u\x00\x00"),
    ("control_button", b"\x00u\x00\x01"),
    ("control_edit", b"\x00u\x00\x02"),
    ("control_drop_down", b"\x00u\x00\x03"),
    ("control_combobox", b"\x00u\x00\x04"),
    ("button_drop_down", b"\x00u\x00\x05"),
    ("split_drop_down", b"\x00u\x00\x06"),
    ("OCX_drop_down", b"\x00u\x00\x07"),
    ("generic_drop_down", b"\x00u\x00\x08"),
    ("graphic_drop_down", b"\x00u\x00\t"),
    ("control_popup", b"\x00u\x00\n"),
    ("graphic_Popup", b"\x00u\x00\x0b"),
    ("button_popup", b"\x00u\x00\x0c"),
    ("split_button_popup", b"\x00u\x00\r"),
    ("split_button_MRU_popup", b"\x00u\x00\x0e"),
    ("control_label", b"\x00u\x00\x0f"),
    ("expanding_grid", b"\x00u\x00\x10"),
    ("split_expanding_grid", b"\x00u\x00\x11"),
    ("control_grid", b"\x00u\x00\x12"),
    ("control_gauge", b"\x00u\x00\x13"),
    ("graphic_combobox", b"\x00u\x00\x14"),
    ("control_pane", b"\x00u\x00\x15"),
    ("active_X", b"\x00u\x00\x16"),
    ("control_group", b"\x00u\x00\x17"),
    ("control_tab", b"\x00u\x00\x18"),
    ("control_spinner", b"\x00u\x00\x19"),
    ("button_state_up", b"\x00v\x00\x00"),
    ("button_state_down", b"\x00u\xff\xff"),
    ("button_state_unset", b"\x00v\x00\x02"),
    ("neither", b"\x00w\x00\x00"),
    ("server", b"\x00w\x00\x01"),
    ("client", b"\x00w\x00\x02"),
    ("both", b"\x00w\x00\x03"),
    ("button_automatic", b"\x00x\x00\x00"),
    ("button_icon", b"\x00x\x00\x01"),
    ("button_caption", b"\x00x\x00\x02"),
    ("button_icon_and_caption", b"\x00x\x00\x03"),
    ("combobox_style_normal", b"\x00y\x00\x00"),
    ("combobox_style_label", b"\x00y\x00\x01"),
    ("None_", b"\x00{\x00\x00"),
    ("Random", b"\x00{\x00\x01"),
    ("Unfold", b"\x00{\x00\x02"),
    ("Slide", b"\x00{\x00\x03"),
    ("hyperlink_type_text_range", b"\x00\x96\x00\x00"),
    ("hyperlink_type_shape", b"\x00\x96\x00\x01"),
    ("hyperlink_type_inline_shape", b"\x00\x96\x00\x02"),
    ("append_string", b"\x00\xae\x00\x00"),
    ("post_string", b"\x00\xae\x00\x01"),
    ("idle", b"\x00|\x00\x01"),
    ("greeting", b"\x00|\x00\x02"),
    ("goodbye", b"\x00|\x00\x03"),
    ("begin_speaking", b"\x00|\x00\x04"),
    ("character_success_major", b"\x00|\x00\x06"),
    ("get_attention_major", b"\x00|\x00\x0b"),
    ("get_attention_minor", b"\x00|\x00\x0c"),
    ("searching", b"\x00|\x00\r"),
    ("printing", b"\x00|\x00\x12"),
    ("gesture_right", b"\x00|\x00\x13"),
    ("writing_noting_something", b"\x00|\x00\x16"),
    ("working_at_something", b"\x00|\x00\x17"),
    ("thinking", b"\x00|\x00\x18"),
    ("sending_mail", b"\x00|\x00\x19"),
    ("listens_to_computer", b"\x00|\x00\x1a"),
    ("disappear", b"\x00|\x00\x1f"),
    ("appear", b"\x00|\x00 "),
    ("get_artsy", b"\x00|\x00d"),
    ("get_techy", b"\x00|\x00e"),
    ("get_wizardy", b"\x00|\x00f"),
    ("checking_something", b"\x00|\x00g"),
    ("look_down", b"\x00|\x00h"),
    ("look_down_left", b"\x00|\x00i"),
    ("look_down_right", b"\x00|\x00j"),
    ("look_left", b"\x00|\x00k"),
    ("look_right", b"\x00|\x00l"),
    ("look_up", b"\x00|\x00m"),
    ("look_up_left", b"\x00|\x00n"),
    ("look_up_right", b"\x00|\x00o"),
    ("saving", b"\x00|\x00p"),
    ("gesture_down", b"\x00|\x00q"),
    ("gesture_left", b"\x00|\x00r"),
    ("gesture_up", b"\x00|\x00s"),
    ("empty_trash", b"\x00|\x00t"),
    ("button_none", b"\x00}\x00\x00"),
    ("button_ok", b"\x00}\x00\x01"),
    ("button_cancel", b"\x00}\x00\x02"),
    ("buttons_ok_cancel", b"\x00}\x00\x03"),
    ("buttons_yes_no", b"\x00}\x00\x04"),
    ("buttons_yes_no_cancel", b"\x00}\x00\x05"),
    ("buttons_back_close", b"\x00}\x00\x06"),
    ("buttons_next_close", b"\x00}\x00\x07"),
    ("buttons_back_next_close", b"\x00}\x00\x08"),
    ("buttons_retry_cancel", b"\x00}\x00\t"),
    ("buttons_abort_retry_ignore", b"\x00}\x00\n"),
    ("buttons_search_close", b"\x00}\x00\x0b"),
    ("buttons_back_next_snooze", b"\x00}\x00\x0c"),
    ("buttons_tips_options_close", b"\x00}\x00\r"),
    ("buttons_yes_all_no_cancel", b"\x00}\x00\x0e"),
    ("icon_none", b"\x00~\x00\x00"),
    ("icon_application", b"\x00~\x00\x01"),
    ("icon_alert", b"\x00~\x00\x02"),
    ("icon_tip", b"\x00~\x00\x03"),
    ("icon_alert_critical", b"\x00~\x00e"),
    ("icon_alert_warning", b"\x00~\x00g"),
    ("icon_alert_info", b"\x00~\x00h"),
    ("Inactive", b"\x00\x82\x00\x00"),
    ("Active", b"\x00\x82\x00\x01"),
    ("Suspend", b"\x00\x82\x00\x02"),
    ("Resume", b"\x00\x82\x00\x03"),
    ("property_type_number", b"\x00\xa2\x00\x01"),
    ("property_type_boolean", b"\x00\xa2\x00\x02"),
    ("property_type_date", b"\x00\xa2\x00\x03"),
    ("property_type_string", b"\x00\xa2\x00\x04"),
    ("property_type_float", b"\x00\xa2\x00\x05"),
    ("msoAutomationSecurityLow", b"\x00\xa3\x00\x01"),
    ("msoAutomationSecurityByUI", b"\x00\xa3\x00\x02"),
    ("msoAutomationSecurityForceDisable", b"\x00\xa3\x00\x03"),
    ("resolution544x376", b"\x00\x84\x00\x00"),
    ("resolution640x480", b"\x00\x84\x00\x01"),
    ("resolution720x512", b"\x00\x84\x00\x02"),
    ("resolution800x600", b"\x00\x84\x00\x03"),
    ("resolution1024x768", b"\x00\x84\x00\x04"),
    ("resolution1152x882", b"\x00\x84\x00\x05"),
    ("resolution1152x900", b"\x00\x84\x00\x06"),
    ("resolution1280x1024", b"\x00\x84\x00\x07"),
    ("resolution1600x1200", b"\x00\x84\x00\x08"),
    ("resolution1800x1440", b"\x00\x84\x00\t"),
    ("resolution1920x1200", b"\x00\x84\x00\n"),
    ("Arabic_character_set", b"\x00\x85\x00\x01"),
    ("Cyrillic_character_set", b"\x00\x85\x00\x02"),
    ("English_character_set", b"\x00\x85\x00\x03"),
    ("Greek_character_set", b"\x00\x85\x00\x04"),
    ("Hebrew_character_set", b"\x00\x85\x00\x05"),
    ("Japanese_character_set", b"\x00\x85\x00\x06"),
    ("Korean_character_set", b"\x00\x85\x00\x07"),
    ("Multilingual_Unicode_character_set", b"\x00\x85\x00\x08"),
    ("Simplified_Chinese_character_set", b"\x00\x85\x00\t"),
    ("Thai_character_set", b"\x00\x85\x00\n"),
    ("Traditional_Chinese_character_set", b"\x00\x85\x00\x0b"),
    ("Vietnamese_character_set", b"\x00\x85\x00\x0c"),
    ("encoding_Thai", b"\x00\x8b\x03j"),
    ("encoding_Japanese_ShiftJIS", b"\x00\x8b\x03\xa4"),
    ("encoding_simplified_Chinese", b"\x00\x8b\x03\xa8"),
    ("encoding_Korean", b"\x00\x8b\x03\xb5"),
    ("encoding_Big5_traditional_Chinese", b"\x00\x8b\x03\xb6"),
    ("encoding_little_endian", b"\x00\x8b\x04\xb0"),
    ("encoding_big_endian", b"\x00\x8b\x04\xb1"),
    ("encoding_central_European", b"\x00\x8b\x04\xe2"),
    ("encoding_Cyrillic", b"\x00\x8b\x04\xe3"),
    ("encoding_Western", b"\x00\x8b\x04\xe4"),
    ("encoding_Greek", b"\x00\x8b\x04\xe5"),
    ("encoding_Turkish", b"\x00\x8b\x04\xe6"),
    ("encoding_Hebrew", b"\x00\x8b\x04\xe7"),
    ("encoding_Arabic", b"\x00\x8b\x04\xe8"),
    ("encoding_Baltic", b"\x00\x8b\x04\xe9"),
    ("encoding_Vietnamese", b"\x00\x8b\x04\xea"),
    ("encoding_ISO88591_Latin1", b"\x00\x8bo\xaf"),
    ("encoding_ISO88592_central_Europe", b"\x00\x8bo\xb0"),
    ("encoding_ISO88593_Latin3", b"\x00\x8bo\xb1"),
    ("encoding_ISO88594_Baltic", b"\x00\x8bo\xb2"),
    ("encoding_ISO88595_Cyrillic", b"\x00\x8bo\xb3"),
    ("encoding_ISO88596_Arabic", b"\x00\x8bo\xb4"),
    ("encoding_ISO88597_Greek", b"\x00\x8bo\xb5"),
    ("encoding_ISO88598_Hebrew", b"\x00\x8bo\xb6"),
    ("encoding_ISO88599_Turkish", b"\x00\x8bo\xb7"),
    ("encoding_ISO885915_Latin9", b"\x00\x8bo\xbd"),
    ("encoding_ISO2022_Japanese_no_half_width_Katakana", b"\x00\x8b\xc4,"),
    ("encoding_ISO2022_Japanese_JISX02021984", b"\x00\x8b\xc4-"),
    ("encoding_ISO2022_Japanese_JISX02011989", b"\x00\x8b\xc4."),
    ("encoding_ISO2022KR", b"\x00\x8b\xc41"),
    ("encoding_ISO2022CN_traditional_Chinese", b"\x00\x8b\xc43"),
    ("encoding_ISO2022CN_simplified_Chinese", b"\x00\x8b\xc45"),
    ("encoding_Mac_Roman", b"\x00\x8b'\x10"),
    ("encoding_Mac_Japanese", b"\x00\x8b'\x11"),
    ("encoding_Mac_traditional_Chinese", b"\x00\x8b'\x12"),
    ("encoding_Mac_Korean", b"\x00\x8b'\x13"),
    ("encoding_Mac_Arabic", b"\x00\x8b'\x14"),
    ("encoding_Mac_Hebrew", b"\x00\x8b'\x15"),
    ("encoding_Mac_Greek1", b"\x00\x8b'\x16"),
    ("encoding_Mac_Cyrillic", b"\x00\x8b'\x17"),
    ("encoding_Mac_simplified_Chinese_GB2312", b"\x00\x8b'\x18"),
    ("encoding_Mac_Romania", b"\x00\x8b'\x1a"),
    ("encoding_Mac_Ukraine", b"\x00\x8b'!"),
    ("encoding_Mac_Latin2", b"\x00\x8b'-"),
    ("encoding_Mac_Icelandic", b"\x00\x8b'_"),
    ("encoding_Mac_Turkish", b"\x00\x8b'a"),
    ("encoding_Mac_Croatia", b"\x00\x8b'b"),
    ("encoding_EBCDIC_US_Canada", b"\x00\x8b\x00%"),
    ("encoding_EBCDIC_International", b"\x00\x8b\x01\xf4"),
    ("encoding_EBCDIC_multilingual_ROECE_Latin2", b"\x00\x8b\x03f"),
    ("encoding_EBCDIC_Greek_modern", b"\x00\x8b\x03k"),
    ("encoding_EBCDIC_Turkish_Latin5", b"\x00\x8b\x04\x02"),
    ("encoding_EBCDIC_Germany", b"\x00\x8bO1"),
    ("encoding_EBCDIC_Denmark_Norway", b"\x00\x8bO5"),
    ("encoding_EBCDIC_Finland_Sweden", b"\x00\x8bO6"),
    ("encoding_EBCDIC_Italy", b"\x00\x8bO8"),
    ("encoding_EBCDIC_Latin_America_Spain", b"\x00\x8bO<"),
    ("encoding_EBCDIC_United_Kingdom", b"\x00\x8bO="),
    ("encoding_EBCDIC_Japanese_Katakana_extended", b"\x00\x8bOB"),
    ("encoding_EBCDIC_France", b"\x00\x8bOI"),
    ("encoding_EBCDIC_Arabic", b"\x00\x8bO\xc4"),
    ("encoding_EBCDIC_Greek", b"\x00\x8bO\xc7"),
    ("encoding_EBCDIC_Hebrew", b"\x00\x8bO\xc8"),
    ("encoding_EBCDIC_Korean_extended", b"\x00\x8bQa"),
    ("encoding_EBCDIC_Thai", b"\x00\x8bQf"),
    ("encoding_EBCDIC_Icelandic", b"\x00\x8bQ\x87"),
    ("encoding_EBCDIC_Turkish", b"\x00\x8bQ\xa9"),
    ("encoding_EBCDIC_Russian", b"\x00\x8bQ\x90"),
    ("encoding_EBCDIC_Serbian_Bulgarian", b"\x00\x8bR!"),
    ("encoding_EBCDIC_Japanese_Katakana_extended_and_Japanese", b"\x00\x8b\xc6\xf2"),
    ("encoding_EBCDIC_US_Canada_and_Japanese", b"\x00\x8b\xc6\xf3"),
    ("encoding_EBCDIC_extended_and_Korean", b"\x00\x8b\xc6\xf5"),
    (
        "encoding_EBCDIC_simplified_Chinese_extended_and_simplified_Chinese",
        b"\x00\x8b\xc6\xf7",
    ),
    ("encoding_EBCDIC_US_Canada_and_traditional_Chinese", b"\x00\x8b\xc6\xf9"),
    ("encoding_EBCDIC_Japanese_Latin_extended_and_Japanese", b"\x00\x8b\xc6\xfb"),
    ("encoding_OEM_United_States", b"\x00\x8b\x01\xb5"),
    ("encoding_OEM_Greek", b"\x00\x8b\x02\xe1"),
    ("encoding_OEM_Baltic", b"\x00\x8b\x03\x07"),
    ("encoding_OEM_multilingual_LatinI", b"\x00\x8b\x03R"),
    ("encoding_OEM_multilingual_LatinII", b"\x00\x8b\x03T"),
    ("encoding_OEM_Cyrillic", b"\x00\x8b\x03W"),
    ("encoding_OEM_Turkish", b"\x00\x8b\x03Y"),
    ("encoding_OEM_Portuguese", b"\x00\x8b\x03\\"),
    ("encoding_OEM_Icelandic", b"\x00\x8b\x03]"),
    ("encoding_OEM_Hebrew", b"\x00\x8b\x03^"),
    ("encoding_OEM_Canadian_French", b"\x00\x8b\x03_"),
    ("encoding_OEM_Arabic", b"\x00\x8b\x03`"),
    ("encoding_OEM_Nordic", b"\x00\x8b\x03a"),
    ("encoding_OEM_CyrillicII", b"\x00\x8b\x03b"),
    ("encoding_OEM_modern_Greek", b"\x00\x8b\x03e"),
    ("encoding_EUC_Japanese", b"\x00\x8b\xca\xdc"),
    ("encoding_EUC_Chinese_simplified_Chinese", b"\x00\x8b\xca\xe0"),
    ("encoding_EUC_Korean", b"\x00\x8b\xca\xed"),
    ("encoding_EUC_Taiwanese_traditional_Chinese", b"\x00\x8b\xca\xee"),
    ("encoding_Devanagari", b"\x00\x8b\xde\xaa"),
    ("encoding_Bengali", b"\x00\x8b\xde\xab"),
    ("encoding_Tamil", b"\x00\x8b\xde\xac"),
    ("encoding_Telugu", b"\x00\x8b\xde\xad"),
    ("encoding_Assamese", b"\x00\x8b\xde\xae"),
    ("encoding_Oriya", b"\x00\x8b\xde\xaf"),
    ("encoding_Kannada", b"\x00\x8b\xde\xb0"),
    ("encoding_Malayalam", b"\x00\x8b\xde\xb1"),
    ("encoding_Gujarati", b"\x00\x8b\xde\xb2"),
    ("encoding_Punjabi", b"\x00\x8b\xde\xb3"),
    ("encoding_Arabic_ASMO", b"\x00\x8b\x02\xc4"),
    ("encoding_Arabic_transparent_ASMO", b"\x00\x8b\x02\xd0"),
    ("encoding_Korean_Johab", b"\x00\x8b\x05Q"),
    ("encoding_Taiwan_CNS", b"\x00\x8bN "),
    ("encoding_Taiwan_TCA", b"\x00\x8bN!"),
    ("encoding_Taiwan_Eten", b'\x00\x8bN"'),
    ("encoding_Taiwan_IBM5550", b"\x00\x8bN#"),
    ("encoding_Taiwan_teletext", b"\x00\x8bN$"),
    ("encoding_Taiwan_Wang", b"\x00\x8bN%"),
    ("encoding_IA5IRV", b"\x00\x8bN\x89"),
    ("encoding_IA5_German", b"\x00\x8bN\x8a"),
    ("encoding_IA5_Swedish", b"\x00\x8bN\x8b"),
    ("encoding_IA5_Norwegian", b"\x00\x8bN\x8c"),
    ("encoding_US_ASCII", b"\x00\x8bN\x9f"),
    ("encoding_T61", b"\x00\x8bO%"),
    ("encoding_ISO6937_nonspacing_accent", b"\x00\x8bO-"),
    ("encoding_KOI8R", b"\x00\x8bQ\x82"),
    ("encoding_Ext_alpha_lowercase", b"\x00\x8bR#"),
    ("encoding_KOI8U", b"\x00\x8bUj"),
    ("encoding_Europa3", b"\x00\x8bqI"),
    ("encoding_HZGB_simplified_Chinese", b"\x00\x8b\xce\xc8"),
    ("encoding_UTF7", b"\x00\x8b\xfd\xe8"),
    ("encoding_UTF8", b"\x00\x8b\xfd\xe9"),
    ("command_bar", b"msCB"),
    ("command_bar_control", b"mCBC"),
    ("built_in_chart", b"\x01\xf6\x00\x15"),
    ("user_defined", b"\x01\xf6\x00\x16"),
    ("any_gallery", b"\x01\xf6\x00\x17"),
    ("color_index_automatic", b"\x01\xf6\xef\xf7"),
    ("color_index_none", b"\x01\xf6\xef\xd2"),
    ("a_color_index_integer", b"\x01\xf7\x00\x00"),
    ("cap", b"\x01\xf8\x00\x01"),
    ("no_cap", b"\x01\xf8\x00\x02"),
    ("by_columns", b"\x01\xf9\x00\x02"),
    ("by_rows", b"\x01\xf9\x00\x01"),
    ("scale_linear", b"\x01\xf9\xef\xdc"),
    ("scale_logarithmic", b"\x01\xf9\xef\xdb"),
    ("autofill_series", b"\x01\xfb\x00\x04"),
    ("chronological_series", b"\x01\xfb\x00\x03"),
    ("growth_series", b"\x01\xfb\x00\x02"),
    ("data_series_linear", b"\x01\xfa\xef\xdc"),
    ("axis_crosses_automatic", b"\x01\xfb\xef\xf7"),
    ("axis_crosses_custom", b"\x01\xfb\xef\xee"),
    ("axis_crosses_maximum", b"\x01\xfc\x00\x02"),
    ("axis_crosses_minimum", b"\x01\xfc\x00\x04"),
    ("primary_axis", b"\x01\xfd\x00\x01"),
    ("secondary_axis", b"\x01\xfd\x00\x02"),
    ("background_automatic", b"\x01\xfd\xef\xf7"),
    ("background_opaque", b"\x01\xfe\x00\x03"),
    ("background_transparent", b"\x01\xfe\x00\x02"),
    ("window_state_maximized", b"\x01\xfe\xef\xd7"),
    ("window_state_minimized", b"\x01\xfe\xef\xd4"),
    ("window_state_normal", b"\x01\xfe\xef\xd1"),
    ("category_axis", b"\x02\x00\x00\x01"),
    ("series_axis", b"\x02\x00\x00\x03"),
    ("value_axis", b"\x02\x00\x00\x02"),
    ("arrowhead_length_long", b"\x02\x01\x00\x03"),
    ("arrowhead_length_medium", b"\x02\x00\xef\xd6"),
    ("arrowhead_length_short", b"\x02\x01\x00\x01"),
    ("valign_bottom", b"\x02\x01\xef\xf5"),
    ("valign_center", b"\x02\x01\xef\xf4"),
    ("valign_distributed", b"\x02\x01\xef\xeb"),
    ("valign_justify", b"\x02\x01\xef\xde"),
    ("valign_top", b"\x02\x01\xef\xc0"),
    ("tick_mark_cross", b"\x02\x03\x00\x04"),
    ("tick_mark_inside", b"\x02\x03\x00\x02"),
    ("tick_mark_none", b"\x02\x02\xef\xd2"),
    ("tick_mark_outside", b"\x02\x03\x00\x03"),
    ("error_bar_direction_x", b"\x02\x03\xef\xb8"),
    ("error_bar_direction_y", b"\x02\x04\x00\x01"),
    ("error_bar_include_both", b"\x02\x05\x00\x01"),
    ("error_bar_include_minus_values", b"\x02\x05\x00\x03"),
    ("error_bar_include_none", b"\x02\x04\xef\xd2"),
    ("error_bar_include_plus_values", b"\x02\x05\x00\x02"),
    ("interpolated", b"\x02\x06\x00\x03"),
    ("not_plotted", b"\x02\x06\x00\x01"),
    ("zero", b"\x02\x06\x00\x02"),
    ("arrowhead_style_closed", b"\x02\x07\x00\x03"),
    ("arrowhead_style_double_closed", b"\x02\x07\x00\x05"),
    ("arrowhead_style_double_open", b"\x02\x07\x00\x04"),
    ("arrowhead_style_none", b"\x02\x06\xef\xd2"),
    ("arrowhead_style_open", b"\x02\x07\x00\x02"),
    ("arrowhead_width_medium", b"\x02\x07\xef\xd6"),
    ("arrowhead_width_narrow", b"\x02\x08\x00\x01"),
    ("arrowhead_width_wide", b"\x02\x08\x00\x03"),
    ("horizontal_align_center", b"\x02\x08\xef\xf4"),
    ("horizontal_align_center_across_selection", b"\x02\t\x00\x07"),
    ("horizontal_align_distributed", b"\x02\x08\xef\xeb"),
    ("horizontal_align_fill", b"\x02\t\x00\x05"),
    ("horizontal_align_general", b"\x02\t\x00\x01"),
    ("horizontal_align_justify", b"\x02\x08\xef\xde"),
    ("horizontal_align_left", b"\x02\x08\xef\xdd"),
    ("horizontal_align_right", b"\x02\x08\xef\xc8"),
    ("tick_label_position_high", b"\x02\t\xef\xe1"),
    ("tick_label_position_low", b"\x02\t\xef\xda"),
    ("tick_label_position_next_to_axis", b"\x02\n\x00\x04"),
    ("tick_label_position_none", b"\x02\t\xef\xd2"),
    ("legend_position_bottom", b"\x02\n\xef\xf5"),
    ("legend_position_corner", b"\x02\x0b\x00\x02"),
    ("legend_position_left", b"\x02\n\xef\xdd"),
    ("legend_position_right", b"\x02\n\xef\xc8"),
    ("legend_position_top", b"\x02\n\xef\xc0"),
    ("chart_picture_type_stack_scale", b"\x02\x0c\x00\x03"),
    ("chart_picture_type_stack", b"\x02\x0c\x00\x02"),
    ("chart_picture_type_stretch", b"\x02\x0c\x00\x01"),
    ("sides", b"\x02\r\x00\x01"),
    ("end_", b"\x02\r\x00\x02"),
    ("end_sides", b"\x02\r\x00\x03"),
    ("front", b"\x02\r\x00\x04"),
    ("front_sides", b"\x02\r\x00\x05"),
    ("front_end", b"\x02\r\x00\x06"),
    ("all_faces", b"\x02\r\x00\x07"),
    ("orientation_downward", b"\x02\r\xef\xb6"),
    ("orientation_horizontal", b"\x02\r\xef\xe0"),
    ("orientation_upward", b"\x02\r\xef\xb5"),
    ("orientation_vertical", b"\x02\r\xef\xba"),
    ("tick_label_orientation_automatic", b"\x02\x0e\xef\xf7"),
    ("tick_label_orientation_downward", b"\x02\x0e\xef\xb6"),
    ("tick_label_orientation_horizontal", b"\x02\x0e\xef\xe0"),
    ("tick_label_orientation_upward", b"\x02\x0e\xef\xb5"),
    ("tick_label_orientation_vertical", b"\x02\x0e\xef\xba"),
    ("border_weight_hairline", b"\x02\x10\x00\x01"),
    ("border_weight_medium", b"\x02\x0f\xef\xd6"),
    ("border_weight_thick", b"\x02\x10\x00\x04"),
    ("border_weight_thin", b"\x02\x10\x00\x02"),
    ("series_date_day", b"\x02\x11\x00\x01"),
    ("series_date_month", b"\x02\x11\x00\x03"),
    ("series_date_weekday", b"\x02\x11\x00\x02"),
    ("series_date_year", b"\x02\x11\x00\x04"),
    ("underline_style_double", b"\x02\x11\xef\xe9"),
    ("underline_style_double_accounting", b"\x02\x12\x00\x05"),
    ("underline_style_none", b"\x02\x11\xef\xd2"),
    ("underline_style_single", b"\x02\x12\x00\x02"),
    ("underline_style_single_accounting", b"\x02\x12\x00\x04"),
    ("error_bar_type_custom", b"\x02\x12\xef\xee"),
    ("error_bar_type_fixed_value", b"\x02\x13\x00\x01"),
    ("error_bar_type_percent", b"\x02\x13\x00\x02"),
    ("error_bar_type_standard_deviation", b"\x02\x12\xef\xc5"),
    ("error_bar_type_standard_error", b"\x02\x13\x00\x04"),
    ("exponential", b"\x02\x14\x00\x05"),
    ("linear", b"\x02\x13\xef\xdc"),
    ("logarithmic", b"\x02\x13\xef\xdb"),
    ("moving_average", b"\x02\x14\x00\x06"),
    ("polynomial", b"\x02\x14\x00\x03"),
    ("power", b"\x02\x14\x00\x04"),
    ("continuous", b"\x02\x15\x00\x01"),
    ("dash", b"\x02\x14\xef\xed"),
    ("dash_dot", b"\x02\x15\x00\x04"),
    ("dash_dot_dot", b"\x02\x15\x00\x05"),
    ("dot", b"\x02\x14\xef\xea"),
    ("double", b"\x02\x14\xef\xe9"),
    ("slant_dash_dot", b"\x02\x15\x00\r"),
    ("line_style_none", b"\x02\x14\xef\xd2"),
    ("data_labels_show_none", b"\x02\x15\xef\xd2"),
    ("data_labels_show_value", b"\x02\x16\x00\x02"),
    ("data_labels_show_percent", b"\x02\x16\x00\x03"),
    ("data_labels_show_label", b"\x02\x16\x00\x04"),
    ("data_labels_show_label_and_percent", b"\x02\x16\x00\x05"),
    ("data_labels_show_bubble_sizes", b"\x02\x16\x00\x06"),
    ("marker_style_automatic", b"\x02\x16\xef\xf7"),
    ("marker_style_circle", b"\x02\x17\x00\x08"),
    ("marker_style_dash", b"\x02\x16\xef\xed"),
    ("marker_style_diamond", b"\x02\x17\x00\x02"),
    ("marker_style_dot", b"\x02\x16\xef\xea"),
    ("marker_style_none", b"\x02\x16\xef\xd2"),
    ("marker_style_picture", b"\x02\x16\xef\xcd"),
    ("marker_style_plus", b"\x02\x17\x00\t"),
    ("marker_style_square", b"\x02\x17\x00\x01"),
    ("marker_style_star", b"\x02\x17\x00\x05"),
    ("marker_style_triangle", b"\x02\x17\x00\x03"),
    ("marker_style_x", b"\x02\x16\xef\xb8"),
    ("pattern_automatic", b"\x02\x18\xef\xf7"),
    ("pattern_checker", b"\x02\x19\x00\t"),
    ("pattern_criss_cross", b"\x02\x19\x00\x10"),
    ("pattern_down", b"\x02\x18\xef\xe7"),
    ("pattern_gray_16", b"\x02\x19\x00\x11"),
    ("pattern_gray_25", b"\x02\x18\xef\xe4"),
    ("pattern_gray_50", b"\x02\x18\xef\xe3"),
    ("pattern_gray_75", b"\x02\x18\xef\xe2"),
    ("pattern_gray_8", b"\x02\x19\x00\x12"),
    ("pattern_grid", b"\x02\x19\x00\x0f"),
    ("pattern_horizontal", b"\x02\x18\xef\xe0"),
    ("pattern_light_down", b"\x02\x19\x00\r"),
    ("pattern_light_horizontal", b"\x02\x19\x00\x0b"),
    ("pattern_light_up", b"\x02\x19\x00\x0e"),
    ("pattern_light_vertical", b"\x02\x19\x00\x0c"),
    ("pattern_none", b"\x02\x18\xef\xd2"),
    ("pattern_semi_gray_75", b"\x02\x19\x00\n"),
    ("pattern_solid", b"\x02\x19\x00\x01"),
    ("pattern_up", b"\x02\x18\xef\xbe"),
    ("pattern_vertical", b"\x02\x18\xef\xba"),
    ("pattern_linear_gradient", b"\x02\x19\x0f\xa0"),
    ("pattern_rectangular_gradient", b"\x02\x19\x0f\xa1"),
    ("split_by_position", b"\x02\x1a\x00\x01"),
    ("split_by_percent_value", b"\x02\x1a\x00\x03"),
    ("split_by_custom_split", b"\x02\x1a\x00\x04"),
    ("split_by_value", b"\x02\x1a\x00\x02"),
    ("hundreds", b"\x02\x1a\xff\xfe"),
    ("thousands", b"\x02\x1a\xff\xfd"),
    ("ten_thousands", b"\x02\x1a\xff\xfc"),
    ("hundred_thousands", b"\x02\x1a\xff\xfb"),
    ("millions", b"\x02\x1a\xff\xfa"),
    ("ten_millions", b"\x02\x1a\xff\xf9"),
    ("hundred_millions", b"\x02\x1a\xff\xf8"),
    ("thousand_millions", b"\x02\x1a\xff\xf7"),
    ("million_millions", b"\x02\x1a\xff\xf6"),
    ("custom_display_unit", b"\x02\x1a\xef\xee"),
    ("label_position_center", b"\x02\x1b\xef\xf4"),
    ("label_position_above", b"\x02\x1c\x00\x00"),
    ("label_position_below", b"\x02\x1c\x00\x01"),
    ("label_position_left", b"\x02\x1b\xef\xdd"),
    ("label_position_right", b"\x02\x1b\xef\xc8"),
    ("label_position_outside_end", b"\x02\x1c\x00\x02"),
    ("label_position_inside_end", b"\x02\x1c\x00\x03"),
    ("label_position_inside_base", b"\x02\x1c\x00\x04"),
    ("label_position_best_fit", b"\x02\x1c\x00\x05"),
    ("label_position_mixed", b"\x02\x1c\x00\x06"),
    ("label_position_custom", b"\x02\x1c\x00\x07"),
    ("days", b"\x02\x1d\x00\x00"),
    ("months", b"\x02\x1d\x00\x01"),
    ("years", b"\x02\x1d\x00\x02"),
    ("category_scale", b"\x02\x1e\x00\x02"),
    ("time_scale", b"\x02\x1e\x00\x03"),
    ("automatic_scale", b"\x02\x1d\xef\xf7"),
    ("box", b"\x02\x1f\x00\x00"),
    ("pyramid_to_point", b"\x02\x1f\x00\x01"),
    ("pyramid_to_max", b"\x02\x1f\x00\x02"),
    ("cylinder", b"\x02\x1f\x00\x03"),
    ("cone_to_point", b"\x02\x1f\x00\x04"),
    ("cone_to_max", b"\x02\x1f\x00\x05"),
    ("column_clustered", b"\x02 \x003"),
    ("column_stacked", b"\x02 \x004"),
    ("column_stacked_100", b"\x02 \x005"),
    ("ThreeD_column_clustered", b"\x02 \x006"),
    ("ThreeD_column_stacked", b"\x02 \x007"),
    ("ThreeD_column_stacked_100", b"\x02 \x008"),
    ("bar_clustered", b"\x02 \x009"),
    ("bar_stacked", b"\x02 \x00:"),
    ("bar_stacked_100", b"\x02 \x00;"),
    ("ThreeD_bar_clustered", b"\x02 \x00<"),
    ("ThreeD_bar_stacked", b"\x02 \x00="),
    ("ThreeD_bar_stacked_100", b"\x02 \x00>"),
    ("line_stacked", b"\x02 \x00?"),
    ("line_stacked_100", b"\x02 \x00@"),
    ("line_markers", b"\x02 \x00A"),
    ("line_markers_stacked", b"\x02 \x00B"),
    ("line_markers_stacked_100", b"\x02 \x00C"),
    ("pie_of_pie", b"\x02 \x00D"),
    ("pie_exploded", b"\x02 \x00E"),
    ("ThreeD_pie_exploded", b"\x02 \x00F"),
    ("bar_of_pie", b"\x02 \x00G"),
    ("xy_scatter_smooth", b"\x02 \x00H"),
    ("xy_scatter_smooth_no_markers", b"\x02 \x00I"),
    ("xy_scatter_lines", b"\x02 \x00J"),
    ("xy_scatter_lines_no_markers", b"\x02 \x00K"),
    ("area_stacked", b"\x02 \x00L"),
    ("area_stacked_100", b"\x02 \x00M"),
    ("ThreeD_area_stacked", b"\x02 \x00N"),
    ("ThreeD_area_stacked_100", b"\x02 \x00O"),
    ("doughnut_exploded", b"\x02 \x00P"),
    ("radar_markers", b"\x02 \x00Q"),
    ("radar_filled", b"\x02 \x00R"),
    ("surface", b"\x02 \x00S"),
    ("surface_wireframe", b"\x02 \x00T"),
    ("surface_top_view", b"\x02 \x00U"),
    ("surface_top_view_wireframe", b"\x02 \x00V"),
    ("bubble", b"\x02 \x00\x0f"),
    ("bubble_ThreeD_effect", b"\x02 \x00W"),
    ("stock_HLC", b"\x02 \x00X"),
    ("stock_OHLC", b"\x02 \x00Y"),
    ("stock_VHLC", b"\x02 \x00Z"),
    ("stock_VOHLC", b"\x02 \x00["),
    ("cylinder_column_clustered", b"\x02 \x00\\"),
    ("cylinder_column_stacked", b"\x02 \x00]"),
    ("cylinder_column_stacked_100", b"\x02 \x00^"),
    ("cylinder_bar_clustered", b"\x02 \x00_"),
    ("cylinder_bar_stacked", b"\x02 \x00`"),
    ("cylinder_bar_stacked_100", b"\x02 \x00a"),
    ("cylinder_column", b"\x02 \x00b"),
    ("cone_column_clustered", b"\x02 \x00c"),
    ("cone_column_stacked", b"\x02 \x00d"),
    ("cone_column_stacked_100", b"\x02 \x00e"),
    ("cone_bar_clustered", b"\x02 \x00f"),
    ("cone_bar_stacked", b"\x02 \x00g"),
    ("cone_bar_stacked_100", b"\x02 \x00h"),
    ("cone_col", b"\x02 \x00i"),
    ("pyramid_column_clustered", b"\x02 \x00j"),
    ("pyramid_column_stacked", b"\x02 \x00k"),
    ("pyramid_column_stacked_100", b"\x02 \x00l"),
    ("pyramid_bar_clustered", b"\x02 \x00m"),
    ("pyramid_bar_stacked", b"\x02 \x00n"),
    ("pyramid_bar_stacked_100", b"\x02 \x00o"),
    ("pyramid_column", b"\x02 \x00p"),
    ("ThreeD_column", b"\x02\x1f\xef\xfc"),
    ("line_chart", b"\x02 \x00\x04"),
    ("ThreeD_line", b"\x02\x1f\xef\xfb"),
    ("ThreeD_pie", b"\x02\x1f\xef\xfa"),
    ("pie_chart", b"\x02 \x00\x05"),
    ("xyscatter", b"\x02\x1f\xef\xb7"),
    ("ThreeD_area", b"\x02\x1f\xef\xfe"),
    ("area_chart", b"\x02 \x00\x01"),
    ("doughnut", b"\x02\x1f\xef\xe8"),
    ("radar", b"\x02\x1f\xef\xc9"),
    ("combination_chart", b"\x02\x1f\xef\xf1"),
    ("data_label", b"\x02!\x00\x00"),
    ("a_chart_area", b"\x02!\x00\x02"),
    ("a_series", b"\x02!\x00\x03"),
    ("a_chart_title", b"\x02!\x00\x04"),
    ("walls", b"\x02!\x00\x05"),
    ("a_corners_object", b"\x02!\x00\x06"),
    ("data_table", b"\x02!\x00\x07"),
    ("trendline", b"\x02!\x00\x08"),
    ("error_bars_object", b"\x02!\x00\t"),
    ("xerror_bars", b"\x02!\x00\n"),
    ("yerror_bars", b"\x02!\x00\x0b"),
    ("legend_entry", b"\x02!\x00\x0c"),
    ("legend_key", b"\x02!\x00\r"),
    ("shape", b"\x02!\x00\x0e"),
    ("major_gridlines", b"\x02!\x00\x0f"),
    ("minor_gridlines", b"\x02!\x00\x10"),
    ("axis_title", b"\x02!\x00\x11"),
    ("up_bars", b"\x02!\x00\x12"),
    ("plot_area", b"\x02!\x00\x13"),
    ("down_bars", b"\x02!\x00\x14"),
    ("axis", b"\x02!\x00\x15"),
    ("series_lines", b"\x02!\x00\x16"),
    ("floor", b"\x02!\x00\x17"),
    ("legend", b"\x02!\x00\x18"),
    ("hi_lo_lines", b"\x02!\x00\x19"),
    ("drop_lines", b"\x02!\x00\x1a"),
    ("radar_axis_labels", b"\x02!\x00\x1b"),
    ("nothing", b"\x02!\x00\x1c"),
    ("leader_lines", b"\x02!\x00\x1d"),
    ("display_unit_label", b"\x02!\x00\x1e"),
    ("size_is_width", b'\x02"\x00\x02'),
    ("size_is_area", b'\x02"\x00\x01'),
    ("shift_down", b'\x02"\xef\xe7'),
    ("shift_to_right", b'\x02"\xef\xbf'),
    ("shift_to_left", b"\x02#\xef\xc1"),
    ("shift_up", b"\x02#\xef\xbe"),
    ("toward_the_bottom", b"\x02$\xef\xe7"),
    ("toward_the_left", b"\x02$\xef\xc1"),
    ("toward_the_right", b"\x02$\xef\xbf"),
    ("toward_the_top", b"\x02$\xef\xbe"),
    ("do_average", b"\x02%\xef\xf6"),
    ("do_count", b"\x02%\xef\xf0"),
    ("do_count_numbers", b"\x02%\xef\xef"),
    ("do_maximum", b"\x02%\xef\xd8"),
    ("do_minimum", b"\x02%\xef\xd5"),
    ("do_product", b"\x02%\xef\xcb"),
    ("do_standard_deviation", b"\x02%\xef\xc5"),
    ("do_standard_deviation_p", b"\x02%\xef\xc4"),
    ("do_sum", b"\x02%\xef\xc3"),
    ("do_var", b"\x02%\xef\xbc"),
    ("do_var_p", b"\x02%\xef\xbb"),
    ("sheet_type_chart", b"\x02&\xef\xf3"),
    ("sheet_type_dialog_sheet", b"\x02&\xef\xec"),
    ("sheet_type_excel_4_intl_macro_sheet", b"\x02'\x00\x04"),
    ("sheet_type_excel_4_macro_sheet", b"\x02'\x00\x03"),
    ("sheet_type_worksheet", b"\x02&\xef\xb9"),
    ("column_header", b"\x02'\xef\xf2"),
    ("column_item", b"\x02(\x00\x05"),
    ("data_header", b"\x02(\x00\x03"),
    ("data_item", b"\x02(\x00\x07"),
    ("page_header", b"\x02(\x00\x02"),
    ("page_item", b"\x02(\x00\x06"),
    ("row_header", b"\x02'\xef\xc7"),
    ("row_item", b"\x02(\x00\x04"),
    ("table_body", b"\x02(\x00\x08"),
    ("formulas", b"\x02(\xef\xe5"),
    ("comments", b"\x02(\xef\xd0"),
    ("values", b"\x02(\xef\xbd"),
    ("window_type_chart_as_window", b"\x02*\x00\x05"),
    ("window_type_chart_in_place", b"\x02*\x00\x04"),
    ("window_type_clipboard", b"\x02*\x00\x03"),
    ("window_type_info", b"\x02)\xef\xdf"),
    ("window_type_workbook", b"\x02*\x00\x01"),
    ("pivot_field_type_date", b"\x02+\x00\x02"),
    ("pivot_field_type_number", b"\x02*\xef\xcf"),
    ("pivot_field_type_text", b"\x02*\xef\xc2"),
    ("bitmap", b"\x02,\x00\x02"),
    ("picture", b"\x02+\xef\xcd"),
    ("consolidation", b"\x02-\x00\x03"),
    ("database", b"\x02-\x00\x01"),
    ("external", b"\x02-\x00\x02"),
    ("pivot_table", b"\x02,\xef\xcc"),
    ("A1", b"\x02.\x00\x01"),
    ("R1C1", b"\x02-\xef\xca"),
    ("Microsoft_Access", b"\x02/\x00\x04"),
    ("Microsoft_Fox_Pro", b"\x02/\x00\x05"),
    ("Microsoft_Mail", b"\x02/\x00\x03"),
    ("Microsoft_PowerPoint", b"\x02/\x00\x02"),
    ("Microsoft_Project", b"\x02/\x00\x06"),
    ("Microsoft_Schedule_Plus", b"\x02/\x00\x07"),
    ("Microsoft_Word", b"\x02/\x00\x01"),
    ("no_button", b"\x020\x00\x00"),
    ("primary_button", b"\x020\x00\x01"),
    ("secondary_button", b"\x020\x00\x02"),
    ("copy_mode", b"\x021\x00\x01"),
    ("cut_mode", b"\x021\x00\x02"),
    ("filter_copy", b"\x023\x00\x02"),
    ("filter_in_place", b"\x023\x00\x01"),
    ("down_then_over", b"\x024\x00\x01"),
    ("over_then_down", b"\x024\x00\x02"),
    ("link_type_Excel_links", b"\x025\x00\x01"),
    ("link_type_OLE_links", b"\x025\x00\x02"),
    ("column_then_row", b"\x026\x00\x02"),
    ("row_then_column", b"\x026\x00\x01"),
    ("cancel_key_disabled", b"\x027\x00\x00"),
    ("error_handler", b"\x027\x00\x02"),
    ("interrupt", b"\x027\x00\x01"),
    ("page_break_automatic", b"\x027\xef\xf7"),
    ("page_break_manual", b"\x027\xef\xd9"),
    ("page_break_none", b"\x028\x00\x00"),
    ("landscape", b"\x02:\x00\x02"),
    ("portrait", b"\x02:\x00\x01"),
    ("edition_date", b"\x02;\x00\x02"),
    ("update_state", b"\x02;\x00\x01"),
    ("command_underlines_automatic", b"\x02;\xef\xf7"),
    ("command_underlines_off", b"\x02;\xef\xce"),
    ("command_underlines_on", b"\x02<\x00\x01"),
    ("verb_open", b"\x02=\x00\x02"),
    ("verb_primary", b"\x02=\x00\x01"),
    ("calculation_automatic", b"\x02=\xef\xf7"),
    ("calculation_manual", b"\x02=\xef\xd9"),
    ("calculation_semiautomatic", b"\x02>\x00\x02"),
    ("workbook_read_only", b"\x02?\x00\x03"),
    ("workbook_read_write", b"\x02?\x00\x02"),
    ("fit_to_page", b"\x02@\x00\x02"),
    ("full_page", b"\x02@\x00\x03"),
    ("full_screen", b"\x02@\x00\x01"),
    ("part", b"\x02A\x00\x02"),
    ("whole", b"\x02A\x00\x01"),
    ("MAPI", b"\x02B\x00\x01"),
    ("no_mail_system", b"\x02B\x00\x00"),
    ("power_talk", b"\x02B\x00\x02"),
    ("link_info_olelinks", b"\x02C\x00\x02"),
    ("link_info_publishers", b"\x02C\x00\x05"),
    ("link_info_subscribers", b"\x02C\x00\x06"),
    ("cell_type_blanks", b"\x02F\x00\x04"),
    ("cell_type_constants", b"\x02F\x00\x02"),
    ("cell_type_formulas", b"\x02E\xef\xe5"),
    ("cell_type_last_cell", b"\x02F\x00\x0b"),
    ("cell_type_comments", b"\x02E\xef\xd0"),
    ("cell_type_visible", b"\x02F\x00\x0c"),
    ("cell_type_all_format_conditions", b"\x02E\xef\xb4"),
    ("cell_type_same_format_conditions", b"\x02E\xef\xb3"),
    ("cell_type_all_validation", b"\x02E\xef\xb2"),
    ("cell_type_same_validation", b"\x02E\xef\xb1"),
    ("arrange_style_cascade", b"\x02G\x00\x07"),
    ("arrange_style_horizontal", b"\x02F\xef\xe0"),
    ("arrange_style_tiled", b"\x02G\x00\x01"),
    ("arrange_style_vertical", b"\x02F\xef\xba"),
    ("I_beam_cursor", b"\x02H\x00\x03"),
    ("default_cursor", b"\x02G\xef\xd1"),
    ("northwest_arrow_cursor", b"\x02H\x00\x01"),
    ("wait_cursor", b"\x02H\x00\x02"),
    ("fill_copy", b"\x02I\x00\x01"),
    ("fill_days", b"\x02I\x00\x05"),
    ("fill_default", b"\x02I\x00\x00"),
    ("fill_formats", b"\x02I\x00\x03"),
    ("fill_months", b"\x02I\x00\x07"),
    ("fill_series", b"\x02I\x00\x02"),
    ("fill_values", b"\x02I\x00\x04"),
    ("fill_weekdays", b"\x02I\x00\x06"),
    ("fill_years", b"\x02I\x00\x08"),
    ("growth_trend", b"\x02I\x00\n"),
    ("linear_trend", b"\x02I\x00\t"),
    ("flashfill", b"\x02I\x00\x0b"),
    ("autofilter_and", b"\x02J\x00\x01"),
    ("bottom_10_items", b"\x02J\x00\x04"),
    ("bottom_10_percent", b"\x02J\x00\x06"),
    ("autofilter_or", b"\x02J\x00\x02"),
    ("top_10_items", b"\x02J\x00\x03"),
    ("top_10_percent", b"\x02J\x00\x05"),
    ("filter_by_value", b"\x02J\x00\x07"),
    ("filter_by_cell_color", b"\x02J\x00\x08"),
    ("filter_by_font_color", b"\x02J\x00\t"),
    ("filter_by_icon", b"\x02J\x00\n"),
    ("filter_dynamic", b"\x02J\x00\x0b"),
    ("filter_no_fill", b"\x02J\x00\x0c"),
    ("filter_by_automatic_font_color", b"\x02J\x00\r"),
    ("filter_by_no_icon", b"\x02J\x00\x0e"),
    ("clipboard_format_biff", b"\x02K\x00\x08"),
    ("clipboard_format_biff_2", b"\x02K\x00\x12"),
    ("clipboard_format_biff_3", b"\x02K\x00\x14"),
    ("clipboard_format_biff_4", b"\x02K\x00\x1e"),
    ("clipboard_format_binary", b"\x02K\x00\x0f"),
    ("clipboard_format_bitmap", b"\x02K\x00\t"),
    ("clipboard_format_cgm", b"\x02K\x00\r"),
    ("clipboard_format_csv", b"\x02K\x00\x05"),
    ("clipboard_format_dif", b"\x02K\x00\x04"),
    ("clipboard_format_dsp_text", b"\x02K\x00\x0c"),
    ("clipboard_format_embedded_object", b"\x02K\x00\x15"),
    ("clipboard_format_embed_source", b"\x02K\x00\x16"),
    ("clipboard_format_link", b"\x02K\x00\x0b"),
    ("clipboard_format_link_source", b"\x02K\x00\x17"),
    ("clipboard_format_link_source_desc", b"\x02K\x00 "),
    ("clipboard_format_movie", b"\x02K\x00\x18"),
    ("clipboard_format_native", b"\x02K\x00\x0e"),
    ("clipboard_format_object_desc", b"\x02K\x00\x1f"),
    ("clipboard_format_object_link", b"\x02K\x00\x13"),
    ("clipboard_format_owner_link", b"\x02K\x00\x11"),
    ("clipboard_format_pict", b"\x02K\x00\x02"),
    ("clipboard_format_print_pict", b"\x02K\x00\x03"),
    ("clipboard_format_rtf", b"\x02K\x00\x07"),
    ("clipboard_format_screen_pict", b"\x02K\x00\x1d"),
    ("clipboard_format_standard_font", b"\x02K\x00\x1c"),
    ("clipboard_format_standard_scale", b"\x02K\x00\x1b"),
    ("clipboard_format_sylk", b"\x02K\x00\x06"),
    ("clipboard_format_table", b"\x02K\x00\x10"),
    ("clipboard_format_text", b"\x02K\x00\x00"),
    ("clipboard_format_tool_face", b"\x02K\x00\x19"),
    ("clipboard_format_tool_face_pict", b"\x02K\x00\x1a"),
    ("clipboard_format_valu", b"\x02K\x00\x01"),
    ("clipboard_format_wk_1", b"\x02K\x00\n"),
    ("clipboard_format_unicode_text", b"\x02K\x00."),
    ("clipboard_format_style_text", b"\x02K\x005"),
    ("clipboard_format_unicode_style_text", b"\x02K\x007"),
    ("clipboard_format_biff_5", b"\x02K\x00!"),
    ("clipboard_format_picture_build", b'\x02K\x00"'),
    ("clipboard_format_odbc_conn", b"\x02K\x00#"),
    ("clipboard_format_odbc_sql", b"\x02K\x00$"),
    ("clipboard_format_3d_picture", b"\x02K\x00%"),
    ("clipboard_format_unexpected_38", b"\x02K\x00&"),
    ("clipboard_format_drawing_drag_drop", b"\x02K\x00'"),
    ("clipboard_format_drawing", b"\x02K\x00("),
    ("clipboard_format_unexpected_41", b"\x02K\x00)"),
    ("clipboard_format_unexpected_42", b"\x02K\x00*"),
    ("clipboard_format_unexpected_43", b"\x02K\x00+"),
    ("clipboard_format_hyperlink", b"\x02K\x00,"),
    ("clipboard_format_unexpected_45", b"\x02K\x00-"),
    ("clipboard_format_windows_bitmap", b"\x02K\x00/"),
    ("clipboard_format_uniform_resource_locator", b"\x02K\x000"),
    ("clipboard_format_file_name", b"\x02K\x001"),
    ("clipboard_format_unexpected_50", b"\x02K\x002"),
    ("clipboard_format_unexpected_51", b"\x02K\x003"),
    ("clipboard_format_hypertext_markup_language", b"\x02K\x004"),
    ("clipboard_format_office_scrapbook_info", b"\x02K\x006"),
    ("clipboard_format_portable_document_format", b"\x02K\x008"),
    ("clipboard_format_excel_internal_shape", b"\x02K\x009"),
    ("clipboard_format_office_art_shape", b"\x02K\x00:"),
    ("CSV_file_format", b"\x02\xbc\x00\x06"),
    ("CSV_Mac_file_format", b"\x02\xbc\x00\x16"),
    ("CSV_MSDos_file_format", b"\x02\xbc\x00\x18"),
    ("CSV_Windows_file_format", b"\x02\xbc\x00\x17"),
    ("DBF3_file_format", b"\x02\xbc\x00\x08"),
    ("DBF4_file_format", b"\x02\xbc\x00\x0b"),
    ("DIF_file_format", b"\x02\xbc\x00\t"),
    ("Excel2_file_format", b"\x02\xbc\x00\x10"),
    ("Excel_2_east_asian_file_format", b"\x02\xbc\x00\x1b"),
    ("Excel3_file_format", b"\x02\xbc\x00\x1d"),
    ("Excel4_file_format", b"\x02\xbc\x00!"),
    ("Excel5_file_format", b"\x02\xbc\x00'"),
    ("Excel7_file_format", b"\x02\xbc\x00'"),
    ("Excel_4_workbook_file_format", b"\x02\xbc\x00#"),
    ("international_add_in_file_format", b"\x02\xbc\x00\x1a"),
    ("international_macro_file_format", b"\x02\xbc\x00\x19"),
    ("workbook_normal_file_format", b"\x02\xbc\x003"),
    ("SYLK_file_format", b"\x02\xbc\x00\x02"),
    ("current_platform_text_file_format", b"\x02\xbb\xef\xc2"),
    ("text_Mac_file_format", b"\x02\xbc\x00\x13"),
    ("text_MSDos_file_format", b"\x02\xbc\x00\x15"),
    ("text_printer_file_format", b"\x02\xbc\x00$"),
    ("text_windows_file_format", b"\x02\xbc\x00\x14"),
    ("HTML_file_format", b"\x02\xbc\x00-"),
    ("XML_spreadsheet_file_format", b"\x02\xbc\x00."),
    ("PDF_file_format", b"\x02\xbc\x009"),
    ("Excel_binary_file_format", b"\x02\xbc\x002"),
    ("Excel_XML_file_format", b"\x02\xbc\x003"),
    ("macro_enabled_XML_file_format", b"\x02\xbc\x004"),
    ("macro_enabled_template_file_format", b"\x02\xbc\x005"),
    ("template_file_format", b"\x02\xbc\x006"),
    ("add_in_file_format", b"\x02\xbc\x007"),
    ("Excel98to2004_file_format", b"\x02\xbc\x008"),
    ("Excel98to2004_template_file_format", b"\x02\xbc\x00\x11"),
    ("Excel98to2004_add_in_file_format", b"\x02\xbc\x00\x12"),
    ("twenty_four_hour_clock", b"\x02M\x00!"),
    ("four_digit_years", b"\x02M\x00+"),
    ("alternate_array_separator", b"\x02M\x00\x10"),
    ("column_separator", b"\x02M\x00\x0e"),
    ("country_code", b"\x02M\x00\x01"),
    ("country_setting", b"\x02M\x00\x02"),
    ("currency_before", b"\x02M\x00%"),
    ("currency_code", b"\x02M\x00\x19"),
    ("currency_digits", b"\x02M\x00\x1b"),
    ("currency_leading_zeros", b"\x02M\x00("),
    ("currency_minus_sign", b"\x02M\x00&"),
    ("currency_negative", b"\x02M\x00\x1c"),
    ("currency_space_before", b"\x02M\x00$"),
    ("currency_trailing_zeros", b"\x02M\x00'"),
    ("date_order", b"\x02M\x00 "),
    ("date_separator", b"\x02M\x00\x11"),
    ("day_code", b"\x02M\x00\x15"),
    ("day_leading_zero", b"\x02M\x00*"),
    ("decimal_separator", b"\x02M\x00\x03"),
    ("general_format_name", b"\x02M\x00\x1a"),
    ("hour_code", b"\x02M\x00\x16"),
    ("left_brace", b"\x02M\x00\x0c"),
    ("left_bracket", b"\x02M\x00\n"),
    ("list_separator", b"\x02M\x00\x05"),
    ("lower_case_column_letter", b"\x02M\x00\t"),
    ("lower_case_row_letter", b"\x02M\x00\x08"),
    ("mdy", b"\x02M\x00,"),
    ("metric", b"\x02M\x00#"),
    ("minute_code", b"\x02M\x00\x17"),
    ("month_code", b"\x02M\x00\x14"),
    ("month_leading_zero", b"\x02M\x00)"),
    ("month_name_chars", b"\x02M\x00\x1e"),
    ("noncurrency_digits", b"\x02M\x00\x1d"),
    ("non_english_functions", b'\x02M\x00"'),
    ("right_brace", b"\x02M\x00\r"),
    ("right_bracket", b"\x02M\x00\x0b"),
    ("row_separator", b"\x02M\x00\x0f"),
    ("second_code", b"\x02M\x00\x18"),
    ("thousands_separator", b"\x02M\x00\x04"),
    ("time_leading_zero", b"\x02M\x00-"),
    ("time_separator", b"\x02M\x00\x12"),
    ("upper_case_column_letter", b"\x02M\x00\x07"),
    ("upper_case_row_letter", b"\x02M\x00\x06"),
    ("weekday_name_chars", b"\x02M\x00\x1f"),
    ("year_code", b"\x02M\x00\x13"),
    ("page_break_full", b"\x02N\x00\x01"),
    ("page_break_partial", b"\x02N\x00\x02"),
    ("overwrite_cells", b"\x02O\x00\x00"),
    ("insert_delete_cells", b"\x02O\x00\x01"),
    ("insert_entire_rows", b"\x02O\x00\x02"),
    ("no_labels", b"\x02O\xef\xd2"),
    ("row_labels", b"\x02P\x00\x01"),
    ("column_labels", b"\x02P\x00\x02"),
    ("mixed_labels", b"\x02P\x00\x03"),
    ("since_my_last_save", b"\x02Q\x00\x01"),
    ("all_changes", b"\x02Q\x00\x02"),
    ("not_yet_reviewed", b"\x02Q\x00\x03"),
    ("no_indicator", b"\x02R\x00\x00"),
    ("comment_indicator_only", b"\x02Q\xff\xff"),
    ("comment_and_indicator", b"\x02R\x00\x01"),
    ("cell_value", b"\x02S\x00\x01"),
    ("expression", b"\x02S\x00\x02"),
    ("color_scale", b"\x02S\x00\x03"),
    ("databar", b"\x02S\x00\x04"),
    ("top_10", b"\x02S\x00\x05"),
    ("icon_sets", b"\x02S\x00\x06"),
    ("unique_values", b"\x02S\x00\x07"),
    ("text_string", b"\x02S\x00\t"),
    ("blanks_condition", b"\x02S\x00\n"),
    ("time_period", b"\x02S\x00\x0b"),
    ("above_average_condition", b"\x02S\x00\x0c"),
    ("no_blanks_condition", b"\x02S\x00\r"),
    ("errors_condition", b"\x02S\x00\x10"),
    ("no_errors_condition", b"\x02S\x00\x11"),
    ("operator_between", b"\x02T\x00\x01"),
    ("operator_not_between", b"\x02T\x00\x02"),
    ("operator_equal", b"\x02T\x00\x03"),
    ("operator_not_equal", b"\x02T\x00\x04"),
    ("operator_greater", b"\x02T\x00\x05"),
    ("operator_less", b"\x02T\x00\x06"),
    ("operator_greater_equal", b"\x02T\x00\x07"),
    ("operator_less_equal", b"\x02T\x00\x08"),
    ("no_restrictions", b"\x02U\x00\x00"),
    ("unlocked_cells", b"\x02U\x00\x01"),
    ("no_selection", b"\x02T\xef\xd2"),
    ("validate_input_only", b"\x02V\x00\x00"),
    ("validate_whole_number", b"\x02V\x00\x01"),
    ("validate_decimal", b"\x02V\x00\x02"),
    ("validate_list", b"\x02V\x00\x03"),
    ("validated_date", b"\x02V\x00\x04"),
    ("validate_time", b"\x02V\x00\x05"),
    ("validate_text_length", b"\x02V\x00\x06"),
    ("validate_custom", b"\x02V\x00\x07"),
    ("IME_mode_no_control", b"\x02W\x00\x00"),
    ("IME_mode_on", b"\x02W\x00\x01"),
    ("IME_mode_off", b"\x02W\x00\x02"),
    ("IME_mode_disable", b"\x02W\x00\x03"),
    ("IME_mode_hiragana", b"\x02W\x00\x04"),
    ("IME_mode_katakana", b"\x02W\x00\x05"),
    ("IME_mode_katakana_half", b"\x02W\x00\x06"),
    ("IME_mode_alpha_full", b"\x02W\x00\x07"),
    ("IME_mode_alpha", b"\x02W\x00\x08"),
    ("IME_mode_hangul_full", b"\x02W\x00\t"),
    ("IME_mode_hangul", b"\x02W\x00\n"),
    ("valid_alert_none", b"\x02W\xff\xff"),
    ("valid_alert_stop", b"\x02X\x00\x01"),
    ("valid_alert_warning", b"\x02X\x00\x02"),
    ("valid_alert_information", b"\x02X\x00\x03"),
    ("location_as_new_sheet", b"\x02Y\x00\x01"),
    ("location_as_object", b"\x02Y\x00\x02"),
    ("location_automatic", b"\x02Y\x00\x03"),
    ("automatic", b"\x02Y\xef\xf7"),
    ("custom", b"\x02Y\xef\xee"),
    ("pivot_table_version_2000", b"\x03\x84\x00\x00"),
    ("pivot_table_version_10", b"\x03\x84\x00\x01"),
    ("pivot_table_version_11", b"\x03\x84\x00\x02"),
    ("pivot_table_version_12", b"\x03\x84\x00\x03"),
    ("pivot_table_version_14", b"\x03\x84\x00\x04"),
    ("pivot_table_version_current", b"\x03\x83\xff\xff"),
    ("compact_row", b"\x03\x85\x00\x00"),
    ("tabular_row", b"\x03\x85\x00\x01"),
    ("outline_row", b"\x03\x85\x00\x02"),
    ("at_top", b"\x03\x86\x00\x01"),
    ("at_bottom", b"\x03\x86\x00\x02"),
    ("manual_allocation", b"\x03\x87\x00\x01"),
    ("automatic_allocation", b"\x03\x87\x00\x02"),
    ("allocate_value", b"\x03\x88\x00\x01"),
    ("allocate_increment", b"\x03\x88\x00\x02"),
    ("equal_allocation", b"\x03\x89\x00\x01"),
    ("weight_allocation", b"\x03\x89\x00\x02"),
    ("do_not_repeat_labels", b"\x03\x8a\x00\x01"),
    ("repeat_labels", b"\x03\x8a\x00\x02"),
    ("missing_items_default", b"\x03\x8a\xff\xff"),
    ("missing_items_none", b"\x03\x8b\x00\x00"),
    ("missing_items_max", b"\x03\x8b~\xf4"),
    ("missing_items_max2", b"\x03\x9b\x00\x00"),
    ("pivot_cell_value", b"\x03\x8c\x00\x00"),
    ("pivot_cell_pivot_item", b"\x03\x8c\x00\x01"),
    ("pivot_cell_subtotal", b"\x03\x8c\x00\x02"),
    ("pivot_cell_grand_total", b"\x03\x8c\x00\x03"),
    ("pivot_cell_data_field", b"\x03\x8c\x00\x04"),
    ("pivot_cell_pivot_field", b"\x03\x8c\x00\x05"),
    ("pivot_cell_page_field_item", b"\x03\x8c\x00\x06"),
    ("pivot_cell_custom_subtotal", b"\x03\x8c\x00\x07"),
    ("pivot_cell_data_pivot_field", b"\x03\x8c\x00\x08"),
    ("pivot_cell_blank_cell", b"\x03\x8c\x00\t"),
    ("cell_not_changed", b"\x03\x8d\x00\x01"),
    ("cell_changed", b"\x03\x8d\x00\x02"),
    ("cell_change_applied", b"\x03\x8d\x00\x03"),
    ("tabular", b"\x03\x8e\x00\x00"),
    ("outline", b"\x03\x8e\x00\x01"),
    ("pivot_top_count", b"\x03\x8f\x00\x01"),
    ("pivot_bottom_count", b"\x03\x8f\x00\x02"),
    ("pivot_top_percent", b"\x03\x8f\x00\x03"),
    ("pivot_bottom_percent", b"\x03\x8f\x00\x04"),
    ("pivot_top_sum", b"\x03\x8f\x00\x05"),
    ("pivot_bottom_sum", b"\x03\x8f\x00\x06"),
    ("pivot_value_equals", b"\x03\x8f\x00\x07"),
    ("pivot_value_is_not_equal", b"\x03\x8f\x00\x08"),
    ("pivot_value_is_greater_than", b"\x03\x8f\x00\t"),
    ("pivot_value_is_greater_than_or_equal_to", b"\x03\x8f\x00\n"),
    ("pivot_value_is_less_than", b"\x03\x8f\x00\x0b"),
    ("pivot_value_is_less_than_or_equal_to", b"\x03\x8f\x00\x0c"),
    ("pivot_value_is_between", b"\x03\x8f\x00\r"),
    ("pivot_value_is_not_between", b"\x03\x8f\x00\x0e"),
    ("pivot_caption_equals", b"\x03\x8f\x00\x0f"),
    ("pivot_caption_does_not_equal", b"\x03\x8f\x00\x10"),
    ("pivot_caption_begins_with", b"\x03\x8f\x00\x11"),
    ("pivot_caption_does_not_begin_with", b"\x03\x8f\x00\x12"),
    ("pivot_caption_ends_with", b"\x03\x8f\x00\x13"),
    ("pivot_caption_does_not_end_with", b"\x03\x8f\x00\x14"),
    ("pivot_caption_contains", b"\x03\x8f\x00\x15"),
    ("pivot_caption_does_not_contain", b"\x03\x8f\x00\x16"),
    ("pivot_caption_is_greater_than", b"\x03\x8f\x00\x17"),
    ("pivot_caption_is_greater_than_or_equal_to", b"\x03\x8f\x00\x18"),
    ("pivot_caption_is_less_than", b"\x03\x8f\x00\x19"),
    ("pivot_caption_is_less_than_or_equal_to", b"\x03\x8f\x00\x1a"),
    ("pivot_caption_is_between", b"\x03\x8f\x00\x1b"),
    ("pivot_caption_is_now_between", b"\x03\x8f\x00\x1c"),
    ("pivot_specific_date", b"\x03\x8f\x00\x1d"),
    ("pivot_not_specific_date", b"\x03\x8f\x00\x1e"),
    ("pivot_before", b"\x03\x8f\x00\x1f"),
    ("pivot_before_or_equal_to", b"\x03\x8f\x00 "),
    ("pivot_after", b"\x03\x8f\x00!"),
    ("pivot_after_or_equal_to", b'\x03\x8f\x00"'),
    ("pivot_between", b"\x03\x8f\x00#"),
    ("pivot_not_between", b"\x03\x8f\x00$"),
    ("pivot_tomorrow", b"\x03\x8f\x00%"),
    ("pivot_today", b"\x03\x8f\x00&"),
    ("pivot_yesterday", b"\x03\x8f\x00'"),
    ("pivot_next_week", b"\x03\x8f\x00("),
    ("pivot_this_week", b"\x03\x8f\x00)"),
    ("pivot_last_week", b"\x03\x8f\x00*"),
    ("pivot_next_month", b"\x03\x8f\x00+"),
    ("pivot_this_month", b"\x03\x8f\x00,"),
    ("pivot_last_month", b"\x03\x8f\x00-"),
    ("pivot_next_quarter", b"\x03\x8f\x00."),
    ("pivot_this_quarter", b"\x03\x8f\x00/"),
    ("pivot_last_quarter", b"\x03\x8f\x000"),
    ("pivot_next_year", b"\x03\x8f\x001"),
    ("pivot_this_year", b"\x03\x8f\x002"),
    ("pivot_last_year", b"\x03\x8f\x003"),
    ("pivot_year_to_date", b"\x03\x8f\x004"),
    ("pivot_all_dates_in_period_quarter1", b"\x03\x8f\x005"),
    ("pivot_all_dates_in_period_quarter2", b"\x03\x8f\x006"),
    ("pivot_all_dates_in_period_quarter3", b"\x03\x8f\x007"),
    ("pivot_all_dates_in_period_quarter4", b"\x03\x8f\x008"),
    ("pivot_all_dates_in_period_January", b"\x03\x8f\x009"),
    ("pivot_all_dates_in_period_Feberary", b"\x03\x8f\x00:"),
    ("pivot_all_dates_in_period_March", b"\x03\x8f\x00;"),
    ("pivot_all_dates_in_period_April", b"\x03\x8f\x00<"),
    ("pivot_all_dates_in_period_May", b"\x03\x8f\x00="),
    ("pivot_all_dates_in_period_June", b"\x03\x8f\x00>"),
    ("pivot_all_dates_in_period_July", b"\x03\x8f\x00?"),
    ("pivot_all_dates_in_period_August", b"\x03\x8f\x00@"),
    ("pivot_all_dates_in_period_September", b"\x03\x8f\x00A"),
    ("pivot_all_dates_in_period_October", b"\x03\x8f\x00B"),
    ("pivot_all_dates_in_period_November", b"\x03\x8f\x00C"),
    ("pivot_all_dates_in_period_December", b"\x03\x8f\x00D"),
    ("pivot_line_regular", b"\x03\x90\x00\x00"),
    ("pivot_line_subtotal", b"\x03\x90\x00\x01"),
    ("pivot_line_grandtotal", b"\x03\x90\x00\x02"),
    ("pivot_line_blank", b"\x03\x90\x00\x03"),
    ("hierarchy", b"\x03\x91\x00\x01"),
    ("measure", b"\x03\x91\x00\x02"),
    ("set", b"\x03\x91\x00\x03"),
    ("cube_hierarchy", b"\x03\x92\x00\x01"),
    ("cube_measure", b"\x03\x92\x00\x02"),
    ("cube_set", b"\x03\x92\x00\x03"),
    ("cube_attribute", b"\x03\x92\x00\x04"),
    ("cube_calculated_measure", b"\x03\x92\x00\x05"),
    ("cube_KPI_value", b"\x03\x92\x00\x06"),
    ("cube_KPI_goal", b"\x03\x92\x00\x07"),
    ("cube_KPI_status", b"\x03\x92\x00\x08"),
    ("cube_KPI_trend", b"\x03\x92\x00\t"),
    ("cube_KPI_weight", b"\x03\x92\x00\n"),
    ("display_property_in_pivot_table", b"\x03\x93\x00\x01"),
    ("display_property_in_tooltip", b"\x03\x93\x00\x02"),
    ("display_property_in_pivot_table_and_tooltip", b"\x03\x93\x00\x03"),
    ("calculated_member", b"\x03\x94\x00\x00"),
    ("calculated_set", b"\x03\x94\x00\x01"),
    ("connection_type_OLEDB", b"\x03\x95\x00\x01"),
    ("connection_type_ODBC", b"\x03\x95\x00\x02"),
    ("connection_type_XMLMAP", b"\x03\x95\x00\x03"),
    ("connection_type_TEXT", b"\x03\x95\x00\x04"),
    ("connection_type_WEB", b"\x03\x95\x00\x05"),
    ("paste_special_operation_add", b"\x02[\x00\x02"),
    ("paste_special_operation_divide", b"\x02[\x00\x05"),
    ("paste_special_operation_multiply", b"\x02[\x00\x04"),
    ("paste_special_operation_none", b"\x02Z\xef\xd2"),
    ("paste_special_operation_subtract", b"\x02[\x00\x03"),
    ("paste_all", b"\x02[\xef\xf8"),
    ("paste_all_using_source_theme", b"\x02\\\x00\r"),
    ("paste_all_except_borders", b"\x02\\\x00\x07"),
    ("paste_formats", b"\x02[\xef\xe6"),
    ("paste_formulas", b"\x02[\xef\xe5"),
    ("paste_comments", b"\x02[\xef\xd0"),
    ("paste_values", b"\x02[\xef\xbd"),
    ("paste_column_widths", b"\x02\\\x00\x08"),
    ("paste_validation", b"\x02\\\x00\x06"),
    ("paste_formulas_and_number_formats", b"\x02\\\x00\x0b"),
    ("paste_values_and_number_formats", b"\x02\\\x00\x0c"),
    ("phonetic_character_half_width_katakana", b"\x02]\x00\x00"),
    ("phonetic_character_full_width_katakana", b"\x02]\x00\x01"),
    ("phonetic_character_hiragana", b"\x02]\x00\x02"),
    ("no_phonetic_character_conversion", b"\x02]\x00\x03"),
    ("phonetic_align_no_control", b"\x02^\x00\x00"),
    ("phonetic_align_left", b"\x02^\x00\x01"),
    ("phonetic_align_center", b"\x02^\x00\x02"),
    ("phonetic_align_distributed", b"\x02^\x00\x03"),
    ("printer", b"\x02_\x00\x02"),
    ("screen", b"\x02_\x00\x01"),
    ("orient_as_column_field", b"\x02`\x00\x02"),
    ("orient_as_data_field", b"\x02`\x00\x04"),
    ("orient_as_hidden", b"\x02`\x00\x00"),
    ("orient_as_page_field", b"\x02`\x00\x03"),
    ("orient_as_row_field", b"\x02`\x00\x01"),
    ("pivot_field_calculation_difference_from", b"\x02a\x00\x02"),
    ("pivot_field_calculation_index", b"\x02a\x00\t"),
    ("pivot_field_calculation_no_additional_calculation", b"\x02`\xef\xd1"),
    ("pivot_field_calculation_percent_difference_from", b"\x02a\x00\x04"),
    ("pivot_field_calculation_percent_of", b"\x02a\x00\x03"),
    ("pivot_field_calculation_percent_of_column", b"\x02a\x00\x07"),
    ("pivot_field_calculation_percent_of_row", b"\x02a\x00\x06"),
    ("pivot_field_calculation_percent_of_total", b"\x02a\x00\x08"),
    ("pivot_field_calculation_running_total", b"\x02a\x00\x05"),
    ("placement_free_floating", b"\x02b\x00\x03"),
    ("placement_move", b"\x02b\x00\x02"),
    ("placement_move_and_size", b"\x02b\x00\x01"),
    ("Macintosh", b"\x02c\x00\x01"),
    ("MSDos", b"\x02c\x00\x03"),
    ("MSWindows", b"\x02c\x00\x02"),
    ("print_sheet_end", b"\x02d\x00\x01"),
    ("print_in_place", b"\x02d\x00\x10"),
    ("print_no_comments", b"\x02c\xef\xd2"),
    ("priority_high", b"\x02d\xef\xe1"),
    ("priority_low", b"\x02d\xef\xda"),
    ("priority_normal", b"\x02d\xef\xd1"),
    ("selection_mode_label_only", b"\x02f\x00\x01"),
    ("selection_mode_data_and_label", b"\x02f\x00\x00"),
    ("selection_mode_data_only", b"\x02f\x00\x02"),
    ("selection_mode_origin", b"\x02f\x00\x03"),
    ("selection_mode_button", b"\x02f\x00\x0f"),
    ("selection_mode_blanks", b"\x02f\x00\x04"),
    ("range_autoformat_threeD_effects_1", b"\x02g\x00\r"),
    ("range_autoformat_threeD_effects_2", b"\x02g\x00\x0e"),
    ("range_autoformat_accounting_1", b"\x02g\x00\x04"),
    ("range_autoformat_accounting_2", b"\x02g\x00\x05"),
    ("range_autoformat_accounting_3", b"\x02g\x00\x06"),
    ("range_autoformat_accounting_4", b"\x02g\x00\x11"),
    ("range_autoformat_classic_1", b"\x02g\x00\x01"),
    ("range_autoformat_classic_2", b"\x02g\x00\x02"),
    ("range_autoformat_classic_3", b"\x02g\x00\x03"),
    ("range_autoformat_color_1", b"\x02g\x00\x07"),
    ("range_autoformat_color_2", b"\x02g\x00\x08"),
    ("range_autoformat_color_3", b"\x02g\x00\t"),
    ("range_autoformat_list_1", b"\x02g\x00\n"),
    ("range_autoformat_list_2", b"\x02g\x00\x0b"),
    ("range_autoformat_list_3", b"\x02g\x00\x0c"),
    ("range_autoformat_local_format_1", b"\x02g\x00\x0f"),
    ("range_autoformat_local_format_2", b"\x02g\x00\x10"),
    ("range_autoformat_local_format_3", b"\x02g\x00\x13"),
    ("range_autoformat_local_format_4", b"\x02g\x00\x14"),
    ("range_autoformat_none", b"\x02f\xef\xd2"),
    ("range_autoformat_simple", b"\x02f\xef\xc6"),
    ("all_at_once", b"\x02i\x00\x02"),
    ("one_after_another", b"\x02i\x00\x01"),
    ("not_yet_routed", b"\x02j\x00\x00"),
    ("routing_complete", b"\x02j\x00\x02"),
    ("routing_in_progress", b"\x02j\x00\x01"),
    ("auto_activate", b"\x02k\x00\x03"),
    ("auto_close", b"\x02k\x00\x02"),
    ("auto_deactivate", b"\x02k\x00\x04"),
    ("auto_open", b"\x02k\x00\x01"),
    ("exclusive", b"\x02m\x00\x03"),
    ("no_change", b"\x02m\x00\x01"),
    ("shared", b"\x02m\x00\x02"),
    ("local_session_changes", b"\x02n\x00\x02"),
    ("other_session_changes", b"\x02n\x00\x03"),
    ("user_resolution", b"\x02n\x00\x01"),
    ("search_next", b"\x02o\x00\x01"),
    ("search_previous", b"\x02o\x00\x02"),
    ("by_columns", b"\x02p\x00\x02"),
    ("by_rows", b"\x02p\x00\x01"),
    ("sheet_visible", b"\x02p\xff\xff"),
    ("sheet_hidden", b"\x02q\x00\x00"),
    ("sheet_very_hidden", b"\x02q\x00\x02"),
    ("pin_yin", b"\x02r\x00\x01"),
    ("stroke", b"\x02r\x00\x02"),
    ("sort_ascending", b"\x02t\x00\x01"),
    ("sort_descending", b"\x02t\x00\x02"),
    ("sort_manual", b"\x02s\xef\xd9"),
    ("sort_rows", b"\x02u\x00\x02"),
    ("sort_columns", b"\x02u\x00\x01"),
    ("sort_labels", b"\x02v\x00\x02"),
    ("sort_values", b"\x02v\x00\x01"),
    ("errors", b"\x02w\x00\x10"),
    ("logical", b"\x02w\x00\x04"),
    ("numbers", b"\x02w\x00\x01"),
    ("text_values", b"\x02w\x00\x02"),
    ("summary_above", b"\x02x\x00\x00"),
    ("summary_below", b"\x02x\x00\x01"),
    ("summary_on_left", b"\x02x\xef\xdd"),
    ("summary_on_right", b"\x02x\xef\xc8"),
    ("summary_pivot_table", b"\x02y\xef\xcc"),
    ("standard_summary", b"\x02z\x00\x01"),
    ("delimited", b"\x02|\x00\x01"),
    ("fixed_width", b"\x02|\x00\x02"),
    ("text_qualifier_double_quote", b"\x02}\x00\x01"),
    ("text_qualifier_none", b"\x02|\xef\xd2"),
    ("text_qualifier_single_quote", b"\x02}\x00\x02"),
    ("chart", b"\x02}\xef\xf3"),
    ("Excel_4_intl_macro_sheet", b"\x02~\x00\x04"),
    ("Excel_4_macro_sheet", b"\x02~\x00\x03"),
    ("worksheet", b"\x02}\xef\xb9"),
    ("normal_view", b"\x02\x7f\x00\x01"),
    ("page_layout_view", b"\x02\x7f\x00\x03"),
    ("macro_type_command", b"\x02\x80\x00\x02"),
    ("macro_type_function", b"\x02\x80\x00\x01"),
    ("macro_type_not_XLM", b"\x02\x80\x00\x03"),
    ("header_guess", b"\x02\x81\x00\x00"),
    ("header_no", b"\x02\x81\x00\x02"),
    ("header_yes", b"\x02\x81\x00\x01"),
    ("display_shapes", b"\x02\x81\xef\xf8"),
    ("hide", b"\x02\x82\x00\x03"),
    ("placeholders", b"\x02\x82\x00\x02"),
    ("inside_horizontal", b"\x02\x83\x00\x0c"),
    ("inside_vertical", b"\x02\x83\x00\x0b"),
    ("diagonal_down", b"\x02\x83\x00\x05"),
    ("diagonal_up", b"\x02\x83\x00\x06"),
    ("edge_bottom", b"\x02\x83\x00\t"),
    ("edge_left", b"\x02\x83\x00\x07"),
    ("edge_right", b"\x02\x83\x00\n"),
    ("edge_top", b"\x02\x83\x00\x08"),
    ("border_bottom", b"\x02\x82\xef\xf5"),
    ("border_left", b"\x02\x82\xef\xdd"),
    ("border_right", b"\x02\x82\xef\xc8"),
    ("border_top", b"\x02\x82\xef\xc0"),
    ("no_button_changes", b"\x02\x84\x00\x01"),
    ("no_changes", b"\x02\x84\x00\x04"),
    ("no_docking_changes", b"\x02\x84\x00\x03"),
    ("toolbar_protection_none", b"\x02\x83\xef\xd1"),
    ("no_shape_changes", b"\x02\x84\x00\x02"),
    ("dialog_open", b"\x02\x85\x00\x01"),
    ("dialog_open_links", b"\x02\x85\x00\x02"),
    ("dialog_save_as", b"\x02\x85\x00\x05"),
    ("dialog_file_delete", b"\x02\x85\x00\x06"),
    ("dialog_page_setup", b"\x02\x85\x00\x07"),
    ("dialog_print", b"\x02\x85\x00\x08"),
    ("dialog_printer_setup", b"\x02\x85\x00\t"),
    ("dialog_arrange_all", b"\x02\x85\x00\x0c"),
    ("dialog_window_size", b"\x02\x85\x00\r"),
    ("dialog_window_move", b"\x02\x85\x00\x0e"),
    ("dialog_run", b"\x02\x85\x00\x11"),
    ("dialog_set_print_titles", b"\x02\x85\x00\x17"),
    ("dialog_font", b"\x02\x85\x00\x1a"),
    ("dialog_display", b"\x02\x85\x00\x1b"),
    ("dialog_protect_document", b"\x02\x85\x00\x1c"),
    ("dialog_calculation", b"\x02\x85\x00 "),
    ("dialog_extract", b"\x02\x85\x00#"),
    ("dialog_data_delete", b"\x02\x85\x00$"),
    ("dialog_sort", b"\x02\x85\x00'"),
    ("dialog_data_series", b"\x02\x85\x00("),
    ("dialog_table", b"\x02\x85\x00)"),
    ("dialog_format_number", b"\x02\x85\x00*"),
    ("dialog_alignment", b"\x02\x85\x00+"),
    ("dialog_style", b"\x02\x85\x00,"),
    ("dialog_border", b"\x02\x85\x00-"),
    ("dialog_cell_protection", b"\x02\x85\x00."),
    ("dialog_column_width", b"\x02\x85\x00/"),
    ("dialog_clear", b"\x02\x85\x004"),
    ("dialog_paste_special", b"\x02\x85\x005"),
    ("dialog_edit_delete", b"\x02\x85\x006"),
    ("dialog_insert", b"\x02\x85\x007"),
    ("dialog_paste_names", b"\x02\x85\x00:"),
    ("dialog_define_name", b"\x02\x85\x00="),
    ("dialog_create_names", b"\x02\x85\x00>"),
    ("dialog_formula_goto", b"\x02\x85\x00?"),
    ("dialog_formula_find", b"\x02\x85\x00@"),
    ("dialog_gallery_area", b"\x02\x85\x00C"),
    ("dialog_gallery_bar", b"\x02\x85\x00D"),
    ("dialog_gallery_column", b"\x02\x85\x00E"),
    ("dialog_gallery_line", b"\x02\x85\x00F"),
    ("dialog_gallery_pie", b"\x02\x85\x00G"),
    ("dialog_gallery_scatter", b"\x02\x85\x00H"),
    ("dialog_combination", b"\x02\x85\x00I"),
    ("dialog_gridlines", b"\x02\x85\x00L"),
    ("dialog_axes", b"\x02\x85\x00N"),
    ("dialog_attach_text", b"\x02\x85\x00P"),
    ("dialog_patterns", b"\x02\x85\x00T"),
    ("dialog_main_chart", b"\x02\x85\x00U"),
    ("dialog_overlay", b"\x02\x85\x00V"),
    ("dialog_scale", b"\x02\x85\x00W"),
    ("dialog_format_legend", b"\x02\x85\x00X"),
    ("dialog_format_text", b"\x02\x85\x00Y"),
    ("dialog_parse", b"\x02\x85\x00["),
    ("dialog_unhide", b"\x02\x85\x00^"),
    ("dialog_workspace", b"\x02\x85\x00_"),
    ("dialog_activate", b"\x02\x85\x00g"),
    ("dialog_copy_picture", b"\x02\x85\x00l"),
    ("dialog_delete_name", b"\x02\x85\x00n"),
    ("dialog_delete_format", b"\x02\x85\x00o"),
    ("dialog_new", b"\x02\x85\x00w"),
    ("dialog_row_height", b"\x02\x85\x00\x7f"),
    ("dialog_format_move", b"\x02\x85\x00\x80"),
    ("dialog_format_size", b"\x02\x85\x00\x81"),
    ("dialog_formula_replace", b"\x02\x85\x00\x82"),
    ("dialog_select_special", b"\x02\x85\x00\x84"),
    ("dialog_apply_names", b"\x02\x85\x00\x85"),
    ("dialog_replace_font", b"\x02\x85\x00\x86"),
    ("dialog_split", b"\x02\x85\x00\x89"),
    ("dialog_outline", b"\x02\x85\x00\x8e"),
    ("dialog_save_workbook", b"\x02\x85\x00\x91"),
    ("dialog_copy_chart", b"\x02\x85\x00\x93"),
    ("dialog_format_font", b"\x02\x85\x00\x96"),
    ("dialog_note", b"\x02\x85\x00\x9a"),
    ("dialog_set_update_status", b"\x02\x85\x00\x9f"),
    ("dialog_color_palette", b"\x02\x85\x00\xa1"),
    ("dialog_change_link", b"\x02\x85\x00\xa6"),
    ("dialog_app_move", b"\x02\x85\x00\xaa"),
    ("dialog_app_size", b"\x02\x85\x00\xab"),
    ("dialog_main_chart_type", b"\x02\x85\x00\xb9"),
    ("dialog_overlay_chart_type", b"\x02\x85\x00\xba"),
    ("dialog_open_mail", b"\x02\x85\x00\xbc"),
    ("dialog_send_mail", b"\x02\x85\x00\xbd"),
    ("dialog_standard_font", b"\x02\x85\x00\xbe"),
    ("dialog_consolidate", b"\x02\x85\x00\xbf"),
    ("dialog_sort_special", b"\x02\x85\x00\xc0"),
    ("dialog_gallery_threeD_area", b"\x02\x85\x00\xc1"),
    ("dialog_gallery_threeD_column", b"\x02\x85\x00\xc2"),
    ("dialog_gallery_threeD_line", b"\x02\x85\x00\xc3"),
    ("dialog_gallery_threeD_pie", b"\x02\x85\x00\xc4"),
    ("dialog_view_threeD", b"\x02\x85\x00\xc5"),
    ("dialog_goal_seek", b"\x02\x85\x00\xc6"),
    ("dialog_workgroup", b"\x02\x85\x00\xc7"),
    ("dialog_fill_group", b"\x02\x85\x00\xc8"),
    ("dialog_update_link", b"\x02\x85\x00\xc9"),
    ("dialog_promote", b"\x02\x85\x00\xca"),
    ("dialog_demote", b"\x02\x85\x00\xcb"),
    ("dialog_show_detail", b"\x02\x85\x00\xcc"),
    ("dialog_object_properties", b"\x02\x85\x00\xcf"),
    ("dialog_save_new_object", b"\x02\x85\x00\xd0"),
    ("dialog_apply_style", b"\x02\x85\x00\xd4"),
    ("dialog_assign_to_object", b"\x02\x85\x00\xd5"),
    ("dialog_object_protection", b"\x02\x85\x00\xd6"),
    ("dialog_show_toolbar", b"\x02\x85\x00\xdc"),
    ("dialog_print_preview", b"\x02\x85\x00\xde"),
    ("dialog_edit_color", b"\x02\x85\x00\xdf"),
    ("dialog_format_main", b"\x02\x85\x00\xe1"),
    ("dialog_format_overlay", b"\x02\x85\x00\xe2"),
    ("dialog_edit_series", b"\x02\x85\x00\xe4"),
    ("dialog_define_style", b"\x02\x85\x00\xe5"),
    ("dialog_gallery_radar", b"\x02\x85\x00\xf9"),
    ("dialog_zoom", b"\x02\x85\x01\x00"),
    ("dialog_insert_object", b"\x02\x85\x01\x03"),
    ("dialog_size", b"\x02\x85\x01\x05"),
    ("dialog_move", b"\x02\x85\x01\x06"),
    ("dialog_format_auto", b"\x02\x85\x01\r"),
    ("dialog_gallery_threeD_bar", b"\x02\x85\x01\x10"),
    ("dialog_gallery_threeD_surface", b"\x02\x85\x01\x11"),
    ("dialog_customize_toolbar", b"\x02\x85\x01\x14"),
    ("dialog_workbook_add", b"\x02\x85\x01\x19"),
    ("dialog_workbook_move", b"\x02\x85\x01\x1a"),
    ("dialog_workbook_copy", b"\x02\x85\x01\x1b"),
    ("dialog_workbook_options", b"\x02\x85\x01\x1c"),
    ("dialog_save_workspace", b"\x02\x85\x01\x1d"),
    ("dialog_chart_wizard", b"\x02\x85\x01 "),
    ("dialog_assign_to_tool", b"\x02\x85\x01%"),
    ("dialog_placement", b"\x02\x85\x01,"),
    ("dialog_fill_workgroup", b"\x02\x85\x01-"),
    ("dialog_workbook_new", b"\x02\x85\x01."),
    ("dialog_scenario_cells", b"\x02\x85\x011"),
    ("dialog_scenario_add", b"\x02\x85\x013"),
    ("dialog_scenario_edit", b"\x02\x85\x014"),
    ("dialog_scenario_summary", b"\x02\x85\x017"),
    ("dialog_pivot_table_wizard", b"\x02\x85\x018"),
    ("dialog_pivot_field_properties", b"\x02\x85\x019"),
    ("dialog_options_calculation", b"\x02\x85\x01>"),
    ("dialog_options_edit", b"\x02\x85\x01?"),
    ("dialog_options_view", b"\x02\x85\x01@"),
    ("dialog_add_in_manager", b"\x02\x85\x01A"),
    ("dialog_menu_editor", b"\x02\x85\x01B"),
    ("dialog_attach_toolbars", b"\x02\x85\x01C"),
    ("dialog_options_chart", b"\x02\x85\x01E"),
    ("dialog_vba_insert_file", b"\x02\x85\x01H"),
    ("dialog_vba_procedure_definition", b"\x02\x85\x01J"),
    ("dialog_routing_slip", b"\x02\x85\x01P"),
    ("dialog_mail_logon", b"\x02\x85\x01S"),
    ("dialog_insert_picture", b"\x02\x85\x01V"),
    ("dialog_gallery_doughnut", b"\x02\x85\x01X"),
    ("dialog_chart_trend", b"\x02\x85\x01^"),
    ("dialog_workbook_insert", b"\x02\x85\x01b"),
    ("dialog_options_transition", b"\x02\x85\x01c"),
    ("dialog_options_general", b"\x02\x85\x01d"),
    ("dialog_filter_advanced", b"\x02\x85\x01r"),
    ("dialog_mail_next_letter", b"\x02\x85\x01z"),
    ("dialog_data_label", b"\x02\x85\x01{"),
    ("dialog_insert_title", b"\x02\x85\x01|"),
    ("dialog_font_properties", b"\x02\x85\x01}"),
    ("dialog_macro_options", b"\x02\x85\x01~"),
    ("dialog_workbook_unhide", b"\x02\x85\x01\x80"),
    ("dialog_workbook_name", b"\x02\x85\x01\x82"),
    ("dialog_gallery_custom", b"\x02\x85\x01\x84"),
    ("dialog_add_chart_autoformat", b"\x02\x85\x01\x86"),
    ("dialog_chart_add_data", b"\x02\x85\x01\x88"),
    ("dialog_tab_order", b"\x02\x85\x01\x8a"),
    ("dialog_subtotal_create", b"\x02\x85\x01\x8e"),
    ("dialog_workbook_tab_split", b"\x02\x85\x01\x9f"),
    ("dialog_workbook_protect", b"\x02\x85\x01\xa1"),
    ("dialog_scrollbar_properties", b"\x02\x85\x01\xa4"),
    ("dialog_pivot_show_pages", b"\x02\x85\x01\xa5"),
    ("dialog_text_to_columns", b"\x02\x85\x01\xa6"),
    ("dialog_format_charttype", b"\x02\x85\x01\xa7"),
    ("dialog_pivot_field_group", b"\x02\x85\x01\xb1"),
    ("dialog_pivot_field_ungroup", b"\x02\x85\x01\xb2"),
    ("dialog_checkbox_properties", b"\x02\x85\x01\xb3"),
    ("dialog_label_properties", b"\x02\x85\x01\xb4"),
    ("dialog_listbox_properties", b"\x02\x85\x01\xb5"),
    ("dialog_editbox_properties", b"\x02\x85\x01\xb6"),
    ("dialog_open_text", b"\x02\x85\x01\xb9"),
    ("dialog_pushbutton_properties", b"\x02\x85\x01\xbd"),
    ("dialog_filter", b"\x02\x85\x01\xbf"),
    ("dialog_function_wizard", b"\x02\x85\x01\xc2"),
    ("dialog_save_copy_as", b"\x02\x85\x01\xc8"),
    ("dialog_options_lists_add", b"\x02\x85\x01\xca"),
    ("dialog_series_axes", b"\x02\x85\x01\xcc"),
    ("dialog_series_x", b"\x02\x85\x01\xcd"),
    ("dialog_series_y", b"\x02\x85\x01\xce"),
    ("dialog_errorbar_x", b"\x02\x85\x01\xcf"),
    ("dialog_errorbar_y", b"\x02\x85\x01\xd0"),
    ("dialog_format_chart", b"\x02\x85\x01\xd1"),
    ("dialog_series_order", b"\x02\x85\x01\xd2"),
    ("dialog_mail_edit_mailer", b"\x02\x85\x01\xd6"),
    ("dialog_standard_width", b"\x02\x85\x01\xd8"),
    ("dialog_scenario_merge", b"\x02\x85\x01\xd9"),
    ("dialog_properties", b"\x02\x85\x01\xda"),
    ("dialog_summary_info", b"\x02\x85\x01\xda"),
    ("dialog_find_file", b"\x02\x85\x01\xdb"),
    ("dialog_active_cell_font", b"\x02\x85\x01\xdc"),
    ("dialog_vba_make_add_in", b"\x02\x85\x01\xde"),
    ("dialog_file_sharing", b"\x02\x85\x01\xe1"),
    ("dialog_autocorrect", b"\x02\x85\x01\xe5"),
    ("dialog_custom_views", b"\x02\x85\x01\xed"),
    ("dialog_insert_name_label", b"\x02\x85\x01\xf0"),
    ("dialog_series_shape", b"\x02\x85\x01\xf8"),
    ("dialog_chart_options_data_labels", b"\x02\x85\x01\xf9"),
    ("dialog_chart_options_data_table", b"\x02\x85\x01\xfa"),
    ("dialog_set_background_picture", b"\x02\x85\x01\xfd"),
    ("dialog_data_validation", b"\x02\x85\x02\r"),
    ("dialog_chart_type", b"\x02\x85\x02\x0e"),
    ("dialog_chart_location", b"\x02\x85\x02\x0f"),
    ("dialog_chart_source_data", b"\x02\x85\x02\x1d"),
    ("dialog_series_options", b"\x02\x85\x02-"),
    ("dialog_pivot_table_options", b"\x02\x85\x027"),
    ("dialog_pivot_solve_order", b"\x02\x85\x028"),
    ("dialog_pivot_calculated_field", b"\x02\x85\x02:"),
    ("dialog_pivot_calculated_item", b"\x02\x85\x02<"),
    ("dialog_conditional_formatting", b"\x02\x85\x02G"),
    ("dialog_insert_hyperlink", b"\x02\x85\x02T"),
    ("dialog_protect_sharing", b"\x02\x85\x02l"),
    ("dialog_phonetic", b"\x02\x85\x02\x8b"),
    ("dialog_import_text_file", b"\x02\x85\x02\x9a"),
    ("dialog_web_options_general", b"\x02\x85\x02\xb4"),
    ("dialog_web_options_pictures", b"\x02\x85\x02\xb6"),
    ("dialog_web_options_files", b"\x02\x85\x02\xb5"),
    ("dialog_web_options_fonts", b"\x02\x85\x02\xb8"),
    ("dialog_web_options_encoding", b"\x02\x85\x02\xb7"),
    ("prompt", b"\x02\x86\x00\x00"),
    ("constant", b"\x02\x86\x00\x01"),
    ("range", b"\x02\x86\x00\x02"),
    ("param_type_unknown", b"\x02\x87\x00\x00"),
    ("param_type_char", b"\x02\x87\x00\x01"),
    ("param_type_numeric", b"\x02\x87\x00\x02"),
    ("param_type_decimal", b"\x02\x87\x00\x03"),
    ("param_type_number", b"\x02\x87\x00\x04"),
    ("param_type_small_int", b"\x02\x87\x00\x05"),
    ("param_type_float", b"\x02\x87\x00\x06"),
    ("param_type_real", b"\x02\x87\x00\x07"),
    ("param_type_double", b"\x02\x87\x00\x08"),
    ("param_type_var_char", b"\x02\x87\x00\x0c"),
    ("param_type_date", b"\x02\x87\x00\t"),
    ("param_type_time", b"\x02\x87\x00\n"),
    ("param_type_timestamp", b"\x02\x87\x00\x0b"),
    ("param_type_long_var_char", b"\x02\x86\xff\xff"),
    ("param_type_binary", b"\x02\x86\xff\xfe"),
    ("param_type_var_binary", b"\x02\x86\xff\xfd"),
    ("param_type_long_var_binary", b"\x02\x86\xff\xfc"),
    ("param_type_big_int", b"\x02\x86\xff\xfb"),
    ("param_type_tiny_int", b"\x02\x86\xff\xfa"),
    ("param_type_bit", b"\x02\x86\xff\xf9"),
    ("button_control", b"\x02\x88\x00\x00"),
    ("check_box", b"\x02\x88\x00\x01"),
    ("drop_down", b"\x02\x88\x00\x02"),
    ("edit_box", b"\x02\x88\x00\x03"),
    ("group_box", b"\x02\x88\x00\x04"),
    ("label", b"\x02\x88\x00\x05"),
    ("list_box", b"\x02\x88\x00\x06"),
    ("option_button", b"\x02\x88\x00\x07"),
    ("scroll_bar", b"\x02\x88\x00\x08"),
    ("spinner", b"\x02\x88\x00\t"),
    ("general_format", b"\x02\x89\x00\x01"),
    ("text_format", b"\x02\x89\x00\x02"),
    ("MDY_format", b"\x02\x89\x00\x03"),
    ("DMY_format", b"\x02\x89\x00\x04"),
    ("YMD_format", b"\x02\x89\x00\x05"),
    ("MYD_format", b"\x02\x89\x00\x06"),
    ("DYM_format", b"\x02\x89\x00\x07"),
    ("YDM_format", b"\x02\x89\x00\x08"),
    ("skip_column", b"\x02\x89\x00\t"),
    ("ODBC_query", b"\x02\x8a\x00\x01"),
    ("DAO_record_set", b"\x02\x8a\x00\x02"),
    ("web_query", b"\x02\x8a\x00\x04"),
    ("OLE_DB_query", b"\x02\x8a\x00\x05"),
    ("text_import", b"\x02\x8a\x00\x06"),
    ("ADO_recordset", b"\x02\x8a\x00\x07"),
    ("FileMaker_query", b"\x02\x8a\x00\x08"),
    ("cmd_cube", b"\x02\x8b\x00\x01"),
    ("cmd_sql", b"\x02\x8b\x00\x02"),
    ("cmd_table", b"\x02\x8b\x00\x03"),
    ("cmd_default", b"\x02\x8b\x00\x04"),
    ("cmd_list", b"\x02\x8b\x00\x05"),
    ("src_none", b"\x02\x8d\x00\x01"),
    ("src_range", b"\x02\x8d\x00\x02"),
    ("src_external", b"\x02\x8d\x00\x03"),
    ("criteria_equals", b"\x02\x91\x00\x00"),
    ("criteria_less_than_or_equal_to", b"\x02\x91\x00\x01"),
    ("criteria_greater_than_or_equal_to", b"\x02\x91\x00\x02"),
    ("criteria_less_than", b"\x02\x91\x00\x03"),
    ("criteria_greater_than", b"\x02\x91\x00\x04"),
    ("criteria_begins_with", b"\x02\x91\x00\x05"),
    ("criteria_ends_with", b"\x02\x91\x00\x06"),
    ("criteria_contains", b"\x02\x91\x00\x07"),
    ("no_condition", b"\x02\x92\x00\x00"),
    ("and_condition", b"\x02\x92\x00\x01"),
    ("or_condition", b"\x02\x92\x00\x02"),
    ("range_value_default", b"\x02\x93\x00\n"),
    ("range_value_XML_spreadsheet", b"\x02\x93\x00\x0b"),
    ("range_value_MS_persist_XML", b"\x02\x93\x00\x0c"),
    ("inches", b"\x02\x94\x00\x01"),
    ("centimeters", b"\x02\x94\x00\x02"),
    ("millimeters", b"\x02\x94\x00\x03"),
    ("subtotal_automatic", b"\x02\x95\x00\x01"),
    ("subtotal_sum", b"\x02\x95\x00\x02"),
    ("subtotal_count", b"\x02\x95\x00\x03"),
    ("subtotal_average", b"\x02\x95\x00\x04"),
    ("subtotal_max", b"\x02\x95\x00\x05"),
    ("subtotal_min", b"\x02\x95\x00\x06"),
    ("subtotal_product", b"\x02\x95\x00\x07"),
    ("subtotal_count_numbers", b"\x02\x95\x00\x08"),
    ("subtotal_standard_deviation", b"\x02\x95\x00\t"),
    ("subtotal_standard_deviation_p", b"\x02\x95\x00\n"),
    ("subtotal_variable", b"\x02\x95\x00\x0b"),
    ("subtotal_variable_p", b"\x02\x95\x00\x0c"),
    ("data_entry_on", b"\x02\x96\x00\x01"),
    ("data_entry_strict", b"\x02\x96\x00\x02"),
    ("data_entry_off", b"\x02\x95\xef\xce"),
    ("status_text", b"\x02\x96\xff\xff"),
    ("a_Boolean", b"\x02\x97\x00\x00"),
    ("excel_menus", b"\x02\x98\x00\x01"),
    ("left_to_right", b"\x02\x98\xecu"),
    ("right_to_left", b"\x02\x98\xect"),
    ("context", b"\x02\x98\xecv"),
    ("normal_cursor", b"\x02\x9a\x00\x00"),
    ("logical_cursor", b"\x02\x9a\x00\x01"),
    ("visual_cursor", b"\x02\x9a\x00\x02"),
    ("range_object", b"\x02\x9b\x00\x01"),
    ("A1_style_range_reference", b"\x02\x9b\x00\x02"),
    ("named_range", b"\x02\x9b\x00\x03"),
    ("automatic_subtotal", b"\x02\x9c\x00\x01"),
    ("sum_subtotal", b"\x02\x9c\x00\x02"),
    ("count_subtotal", b"\x02\x9c\x00\x03"),
    ("average_subtotal", b"\x02\x9c\x00\x04"),
    ("maximum_value", b"\x02\x9c\x00\x05"),
    ("minimum_value", b"\x02\x9c\x00\x06"),
    ("product_subtotal", b"\x02\x9c\x00\x07"),
    ("count_numbers_subtotal", b"\x02\x9c\x00\x08"),
    ("standard_deviation", b"\x02\x9c\x00\t"),
    ("standard_deviation_P", b"\x02\x9c\x00\n"),
    ("variance_subtotal", b"\x02\x9c\x00\x0b"),
    ("variance_P_subtotal", b"\x02\x9c\x00\x0c"),
    ("type_automatic", b"\x02\x9c\xef\xf7"),
    ("type_manual", b"\x02\x9c\xef\xd9"),
    ("position_top", b"\x02\x9d\xef\xc0"),
    ("position_bottom", b"\x02\x9d\xef\xf5"),
    ("scroll_tab_position_first", b"\x02\x9f\x00\x00"),
    ("scroll_tab_position_last", b"\x02\x9f\x00\x01"),
    ("range", b"\x02\xa0\x00\x00"),
    ("a_list_of_ranges", b"\x02\xa0\x00\x01"),
    ("report_name", b"\x02\xa0\x00\x02"),
    ("a_list_of_string_that_is_a_SQL_query", b"\x02\xa0\x00\x03"),
    ("built_in_chart_template", b"\x02\xa1\x00\x01"),
    ("format_name", b"\x02\xa1\x00\x02"),
    ("built_in_chart_type", b"\x02\xa2\x00\x15"),
    ("custom_chart", b"\x02\xa1\xef\xee"),
    ("range_object", b"\x02\xa3\x00\x01"),
    ("A1_style_range_reference", b"\x02\xa3\x00\x02"),
    ("named_range", b"\x02\xa3\x00\x03"),
    ("list_of_strings", b"\x02\xa3\x00\x04"),
    ("range_object", b"\x02\xa4\x00\x01"),
    ("A1_style_range_reference", b"\x02\xa4\x00\x02"),
    ("named_range", b"\x02\xa4\x00\x03"),
    ("input_default_as_string", b"\x02\xa4\x00\x04"),
    ("a_number", b"\x02\xa5\x00\x01"),
    ("input_type_as_string", b"\x02\xa5\x00\x02"),
    ("a_number_or_a_string", b"\x02\xa5\x00\x03"),
    ("a_bool", b"\x02\xa5\x00\x04"),
    ("range_object", b"\x02\xa5\x00\x08"),
    ("list_of_numbers", b"\x02\xa5\x00A"),
    ("list_of_strings", b"\x02\xa5\x00B"),
    ("list_of_number_or_string", b"\x02\xa5\x00C"),
    ("list_of_bools", b"\x02\xa5\x00D"),
    ("list_of_range_objects", b"\x02\xa5\x00H"),
    ("a_number", b"\x02\xa6\x00\x01"),
    ("a_bool", b"\x02\xa6\x00\x04"),
    ("range_object", b"\x02\xa7\x00\x01"),
    ("A1_style_range_reference", b"\x02\xa7\x00\x02"),
    ("named_range", b"\x02\xa7\x00\x03"),
    ("list_of_strings", b"\x02\xa7\x00\x04"),
    ("percentable", b"\x02\xa8\x00\x01"),
    ("a_bool", b"\x02\xa8\x00\x04"),
    ("script", b"\x02\xa9\x00\x01"),
    ("script_Text", b"\x02\xa9\x00\x02"),
    ("application", b"\x02\xaa\x00\x01"),
    ("worksheet", b"\x02\xaa\x00\x02"),
    ("A1_style_range_reference", b"\x02\xaa\x00\x03"),
    ("horizontal_aligment_bottom", b"\x02\xaa\xef\xf5"),
    ("horizontal_aligment_left", b"\x02\xaa\xef\xdd"),
    ("horizontal_aligment_right", b"\x02\xaa\xef\xc8"),
    ("horizontal_aligment_top", b"\x02\xaa\xef\xc0"),
    ("vertical_alignment_top", b"\x02\xab\xef\xc0"),
    ("vertical_alignment_center", b"\x02\xab\xef\xf4"),
    ("vertical_alignment_bottom", b"\x02\xab\xef\xf5"),
    ("vertical_alignment_justify", b"\x02\xab\xef\xde"),
    ("vertical_alignment_distributed", b"\x02\xab\xef\xeb"),
    ("checkbox_off", b"\x02\xac\xef\xce"),
    ("checkbox_on", b"\x02\xad\x00\x01"),
    ("checkbox_mixed", b"\x02\xad\x00\x02"),
    ("text", b"\x02\xad\xef\xc2"),
    ("a_number", b"\x02\xae\x00\x02"),
    ("xl_number", b"\x02\xad\xef\xcf"),
    ("reference", b"\x02\xae\x00\x04"),
    ("formula", b"\x02\xae\x00\x05"),
    ("select_none", b"\x02\xb1\xef\xd2"),
    ("select_simple", b"\x02\xb1\xef\xc6"),
    ("select_extended", b"\x02\xb2\x00\x03"),
    ("text_to_replace", b"\x02\xb3\x00\x01"),
    ("replacement_text", b"\x02\xb3\x00\x02"),
    ("range_object", b"\x02\xb4\x00\x01"),
    ("A1_style_range_reference", b"\x02\xb4\x00\x02"),
    ("named_range", b"\x02\xb4\x00\x03"),
    ("list_of_category_names", b"\x02\xb4\x00\x04"),
    ("do_not_update_links", b"\x02\xb6\x00\x00"),
    ("update_external_links_only", b"\x02\xb6\x00\x01"),
    ("update_remote_links_only", b"\x02\xb6\x00\x02"),
    ("update_remote_and_external_links", b"\x02\xb6\x00\x03"),
    ("tab_delimiter", b"\x02\xb7\x00\x01"),
    ("commas_delimiter", b"\x02\xb7\x00\x02"),
    ("spaces_delimiter", b"\x02\xb7\x00\x03"),
    ("semicolon_delimiter", b"\x02\xb7\x00\x04"),
    ("no_delimiter", b"\x02\xb7\x00\x05"),
    ("custom_character_delimiter", b"\x02\xb7\x00\x06"),
    ("vary_by_color", b"\x02\xb8\x00\x01"),
    ("vary_by_shade", b"\x02\xb8\x00\x02"),
    ("vary_by_grayscale", b"\x02\xb8\x00\x03"),
    ("vary_by_same_color", b"\x02\xb8\x00\x04"),
    ("range_object", b"\x02\xb9\x00\x01"),
    ("A1_style_range_reference", b"\x02\xb9\x00\x02"),
    ("named_range", b"\x02\xb9\x00\x03"),
    ("worksheet_object", b"\x02\xba\x00\x01"),
    ("worksheet_name", b"\x02\xba\x00\x02"),
    ("align_tick_label_center", b"\x02\xba\xef\xf4"),
    ("align_tick_label_left", b"\x02\xba\xef\xdd"),
    ("align_tick_label_right", b"\x02\xba\xef\xc8"),
    ("Basque", b"\x02\xbc\x04-"),
    ("Catalan", b"\x02\xbc\x04\x03"),
    ("Chinese", b"\x02\xbc\x08\x04"),
    ("Chinese_Taiwan", b"\x02\xbc\x04\x04"),
    ("Czech", b"\x02\xbc\x04\x05"),
    ("Danish", b"\x02\xbc\x04\x06"),
    ("Dutch", b"\x02\xbc\x04\x13"),
    ("English_US", b"\x02\xbc\x04\t"),
    ("English_AUS", b"\x02\xbc\x0c\t"),
    ("English_British", b"\x02\xbc\x08\t"),
    ("English_CAN", b"\x02\xbc\x10\t"),
    ("Finnish", b"\x02\xbc\x04\x0b"),
    ("French", b"\x02\xbc\x04\x0c"),
    ("French_Canadian", b"\x02\xbc\x0c\x0c"),
    ("German", b"\x02\xbc\x04\x07"),
    ("German_Austria", b"\x02\xbc\x0c\x07"),
    ("Swiss_German", b"\x02\xbc\x08\x07"),
    ("Greek", b"\x02\xbc\x04\x08"),
    ("Hungarian", b"\x02\xbc\x04\x0e"),
    ("Italian", b"\x02\xbc\x04\x10"),
    ("Japanese", b"\x02\xbc\x04\x11"),
    ("Korean", b"\x02\xbc\x04\x12"),
    ("Malaysian", b"\x02\xbc\x04>"),
    ("Norwegian_Bokmal", b"\x02\xbc\x04\x14"),
    ("Norwegian", b"\x02\xbc\x04,"),
    ("Polish", b"\x02\xbc\x04\x15"),
    ("Portuguese_Brazil", b"\x02\xbc\x04\x16"),
    ("Portuguese_Iberian", b"\x02\xbc\x08\x16"),
    ("Russian", b"\x02\xbc\x04\x19"),
    ("Slovak", b"\x02\xbc\x04\x1b"),
    ("Slovenian", b"\x02\xbc\x04$"),
    ("Spanish", b"\x02\xbc\x04\n"),
    ("Swedish", b"\x02\xbc\x04\x1d"),
    ("Turkish", b"\x02\xbc\x04\x1f"),
    ("sort_on_cell_value", b"\x02\xbd\x00\x00"),
    ("sort_on_cell_color", b"\x02\xbd\x00\x01"),
    ("sort_on_font_color", b"\x02\xbd\x00\x02"),
    ("sort_on_icon", b"\x02\xbd\x00\x03"),
    ("sort_normal", b"\x02\xbe\x00\x00"),
    ("sort_text_as_numbers", b"\x02\xbe\x00\x01"),
    ("none_totals_calc", b"\x02\xbf\x00\x00"),
    ("average_totals_calc", b"\x02\xbf\x00\x01"),
    ("count_totals_calc", b"\x02\xbf\x00\x02"),
    ("count_number_totals_calc", b"\x02\xbf\x00\x03"),
    ("max_totals_calc", b"\x02\xbf\x00\x04"),
    ("min_totals_calc", b"\x02\xbf\x00\x05"),
    ("sum_totals_calc", b"\x02\xbf\x00\x06"),
    ("deviation_totals_calc", b"\x02\xbf\x00\x07"),
    ("var_totals_calc", b"\x02\xbf\x00\x08"),
    ("custom_totals_calc", b"\x02\xbf\x00\t"),
    ("no_chart_title", b"\x03\x86\x00\x00"),
    ("chart_title_centered_overlay", b"\x03\x86\x00\x01"),
    ("chart_title_above_chart", b"\x03\x86\x00\x02"),
    ("no_legend", b"\x03\x86\x00d"),
    ("legend_right", b"\x03\x86\x00e"),
    ("legend_top", b"\x03\x86\x00f"),
    ("legend_left", b"\x03\x86\x00g"),
    ("legend_bottom", b"\x03\x86\x00h"),
    ("legend_right_overlay", b"\x03\x86\x00i"),
    ("legend_left_overlay", b"\x03\x86\x00j"),
    ("no_data_label", b"\x03\x86\x00\xc8"),
    ("show_data_label", b"\x03\x86\x00\xc9"),
    ("data_label_center", b"\x03\x86\x00\xca"),
    ("data_label_inside_end", b"\x03\x86\x00\xcb"),
    ("data_label_inside_base", b"\x03\x86\x00\xcc"),
    ("data_label_outside_end", b"\x03\x86\x00\xcd"),
    ("data_label_left", b"\x03\x86\x00\xce"),
    ("data_label_right", b"\x03\x86\x00\xcf"),
    ("data_label_top", b"\x03\x86\x00\xd0"),
    ("data_label_bottom", b"\x03\x86\x00\xd1"),
    ("data_label_best_fit", b"\x03\x86\x00\xd2"),
    ("no_primary_category_axis_title", b"\x03\x86\x01,"),
    ("primary_category_axis_title_adjacent_to_axis", b"\x03\x86\x01-"),
    ("primary_category_axis_title_below_axis", b"\x03\x86\x01."),
    ("primary_category_axis_title_rotated", b"\x03\x86\x01/"),
    ("primary_category_axis_title_vertical", b"\x03\x86\x010"),
    ("primary_category_axis_title_horizontal", b"\x03\x86\x011"),
    ("no_primary_value_axis_title", b"\x03\x86\x012"),
    ("primary_value_axis_title_adjacent_to_axis", b"\x03\x86\x013"),
    ("primary_value_axis_title_below_axis", b"\x03\x86\x014"),
    ("primary_value_axis_title_rotated", b"\x03\x86\x015"),
    ("primary_value_axis_title_vertical", b"\x03\x86\x016"),
    ("primary_value_axis_title_horizontal", b"\x03\x86\x017"),
    ("no_secondary_category_axis_title", b"\x03\x86\x018"),
    ("secondary_category_axis_title_adjacent_to_axis", b"\x03\x86\x019"),
    ("secondary_category_axis_title_below_Axis", b"\x03\x86\x01:"),
    ("secondary_category_axis_title_rotated", b"\x03\x86\x01;"),
    ("secondary_category_axis_title_vertical", b"\x03\x86\x01<"),
    ("secondary_category_axis_title_horizontal", b"\x03\x86\x01="),
    ("no_secondary_value_axis_title", b"\x03\x86\x01>"),
    ("secondary_value_axis_title_Adjacent_to_axis", b"\x03\x86\x01?"),
    ("secondary_value_axis_title_below_axis", b"\x03\x86\x01@"),
    ("secondary_value_axis_title_rotated", b"\x03\x86\x01A"),
    ("secondary_value_axis_title_vertical", b"\x03\x86\x01B"),
    ("secondary_value_axis_title_horizontal", b"\x03\x86\x01C"),
    ("no_series_axis_title", b"\x03\x86\x01D"),
    ("series_axis_title_rotated", b"\x03\x86\x01E"),
    ("series_axis_title_vertical", b"\x03\x86\x01F"),
    ("series_axis_title_horizontal", b"\x03\x86\x01G"),
    ("no_primary_value_grid_lines", b"\x03\x86\x01H"),
    ("primary_value_grid_lines_minor", b"\x03\x86\x01I"),
    ("primary_value_grid_lines_major", b"\x03\x86\x01J"),
    ("primary_value_grid_lines_minor_major", b"\x03\x86\x01K"),
    ("no_primary_category_grid_lines", b"\x03\x86\x01L"),
    ("primary_category_grid_lines_minor", b"\x03\x86\x01M"),
    ("primary_category_grid_lines_major", b"\x03\x86\x01N"),
    ("primary_category_grid_lines_minor_major", b"\x03\x86\x01O"),
    ("no_secondary_value_grid_lines", b"\x03\x86\x01P"),
    ("secondary_value_grid_lines_minor", b"\x03\x86\x01Q"),
    ("secondary_value_grid_lines_major", b"\x03\x86\x01R"),
    ("secondary_value_grid_lines_minor_major", b"\x03\x86\x01S"),
    ("no_secondary_category_grid_lines", b"\x03\x86\x01T"),
    ("secondary_category_grid_lines_minor", b"\x03\x86\x01U"),
    ("secondary_category_grid_lines_major", b"\x03\x86\x01V"),
    ("secondary_category_grid_lines_minor_major", b"\x03\x86\x01W"),
    ("no_series_axis_grid_lines", b"\x03\x86\x01X"),
    ("series_axis_grid_lines_minor", b"\x03\x86\x01Y"),
    ("series_axis_grid_lines_major", b"\x03\x86\x01Z"),
    ("series_axis_grid_lines_minor_major", b"\x03\x86\x01["),
    ("no_primary_category_axis", b"\x03\x86\x01\\"),
    ("primary_category_axis_show", b"\x03\x86\x01]"),
    ("primary_category_axis_without_labels", b"\x03\x86\x01^"),
    ("primary_category_axis_reverse", b"\x03\x86\x01_"),
    ("no_primary_value_axis", b"\x03\x86\x01`"),
    ("show_primary_value_axis", b"\x03\x86\x01a"),
    ("primary_value_axis_thousands", b"\x03\x86\x01b"),
    ("primary_value_axis_millions", b"\x03\x86\x01c"),
    ("primary_value_axis_billions", b"\x03\x86\x01d"),
    ("primary_value_axis_log_scale", b"\x03\x86\x01e"),
    ("no_secondary_category_axis", b"\x03\x86\x01f"),
    ("show_secondary_category_axis", b"\x03\x86\x01g"),
    ("secondary_category_axis_without_labels", b"\x03\x86\x01h"),
    ("secondary_category_axis_reverse", b"\x03\x86\x01i"),
    ("no_secondary_value_axis", b"\x03\x86\x01j"),
    ("show_secondary_value_axis", b"\x03\x86\x01k"),
    ("secondary_value_axis_thousands", b"\x03\x86\x01l"),
    ("secondary_value_axis_millions", b"\x03\x86\x01m"),
    ("secondary_value_axis_billions", b"\x03\x86\x01n"),
    ("secondary_value_axis_log_scale", b"\x03\x86\x01o"),
    ("no_series_axis", b"\x03\x86\x01p"),
    ("show_series_axis", b"\x03\x86\x01q"),
    ("series_axis_without_labeling", b"\x03\x86\x01r"),
    ("series_axis_reverse", b"\x03\x86\x01s"),
    ("primary_category_axis_thousands", b"\x03\x86\x01t"),
    ("primary_category_axis_millions", b"\x03\x86\x01u"),
    ("primary_category_axis_billions", b"\x03\x86\x01v"),
    ("primary_category_axis_log_scale", b"\x03\x86\x01w"),
    ("secondary_category_axis_thousands", b"\x03\x86\x01x"),
    ("secondary_category_axis_millions", b"\x03\x86\x01y"),
    ("secondary_category_axis_billions", b"\x03\x86\x01z"),
    ("secondary_category_axis_log_scale", b"\x03\x86\x01{"),
    ("no_data_table", b"\x03\x86\x01\xf4"),
    ("show_data_table", b"\x03\x86\x01\xf5"),
    ("data_table_with_legend_keys", b"\x03\x86\x01\xf6"),
    ("no_Trendline", b"\x03\x86\x02X"),
    ("trendline_add_linear", b"\x03\x86\x02Y"),
    ("trendline_add_exponential", b"\x03\x86\x02Z"),
    ("trendline_add_linear_forecast", b"\x03\x86\x02["),
    ("trendline_add_two_period_moving_average", b"\x03\x86\x02\\"),
    ("no_error_bar", b"\x03\x86\x02\xbc"),
    ("error_bar_standard_error", b"\x03\x86\x02\xbd"),
    ("error_bar_percentage", b"\x03\x86\x02\xbe"),
    ("error_bar_standard_deviation", b"\x03\x86\x02\xbf"),
    ("no_line", b"\x03\x86\x03 "),
    ("line_drop_line", b"\x03\x86\x03!"),
    ("line_hiLo_line", b'\x03\x86\x03"'),
    ("line_series_line", b"\x03\x86\x03#"),
    ("line_drop_hilo_line", b"\x03\x86\x03$"),
    ("no_up_down_bars", b"\x03\x86\x03\x84"),
    ("show_up_down_bars", b"\x03\x86\x03\x85"),
    ("no_plot_area", b"\x03\x86\x03\xe8"),
    ("show_plot_area", b"\x03\x86\x03\xe9"),
    ("no_chart_wall", b"\x03\x86\x04L"),
    ("show_chart_wall", b"\x03\x86\x04M"),
    ("no_chart_floor", b"\x03\x86\x04\xb0"),
    ("show_chart_floor", b"\x03\x86\x04\xb1"),
    ("filter_above_average", b"\x03\x87\x00!"),
    ("filter_all_dates_in_april", b"\x03\x87\x00\x18"),
    ("filter_all_dates_in_august", b"\x03\x87\x00\x1c"),
    ("filter_all_dates_in_december", b"\x03\x87\x00 "),
    ("filter_all_dates_in_february", b"\x03\x87\x00\x16"),
    ("filter_all_dates_in_january", b"\x03\x87\x00\x15"),
    ("filter_all_dates_in_july", b"\x03\x87\x00\x1b"),
    ("filter_all_dates_in_june", b"\x03\x87\x00\x1a"),
    ("filter_all_dates_in_march", b"\x03\x87\x00\x17"),
    ("filter_all_dates_in_may", b"\x03\x87\x00\x19"),
    ("filter_all_dates_in_november", b"\x03\x87\x00\x1f"),
    ("filter_all_dates_in_october", b"\x03\x87\x00\x1e"),
    ("filter_all_dates_in_quarter1", b"\x03\x87\x00\x11"),
    ("filter_all_dates_in_quarter2", b"\x03\x87\x00\x12"),
    ("filter_all_dates_in_quarter3", b"\x03\x87\x00\x13"),
    ("filter_all_dates_in_quarter4", b"\x03\x87\x00\x14"),
    ("filter_all_dates_in_september", b"\x03\x87\x00\x1d"),
    ("filter_below_average", b'\x03\x87\x00"'),
    ("filter_last_month", b"\x03\x87\x00\x08"),
    ("filter_last_quarter", b"\x03\x87\x00\x0b"),
    ("filter_last_week", b"\x03\x87\x00\x05"),
    ("filter_last_year", b"\x03\x87\x00\x0e"),
    ("filter_next_month", b"\x03\x87\x00\t"),
    ("filter_next_quarter", b"\x03\x87\x00\x0c"),
    ("filter_next_week", b"\x03\x87\x00\x06"),
    ("filter_next_year", b"\x03\x87\x00\x0f"),
    ("filter_this_month", b"\x03\x87\x00\x07"),
    ("filter_this_quarter", b"\x03\x87\x00\n"),
    ("filter_this_week", b"\x03\x87\x00\x04"),
    ("filter_this_year", b"\x03\x87\x00\r"),
    ("filter_today", b"\x03\x87\x00\x01"),
    ("filter_tomorrow", b"\x03\x87\x00\x03"),
    ("filter_year_to_date", b"\x03\x87\x00\x10"),
    ("filter_yesterday", b"\x03\x87\x00\x02"),
    ("theme_font_index_none", b"\x02\xc0\x00\x00"),
    ("theme_font_index_major", b"\x02\xc0\x00\x01"),
    ("theme_font_index_minor", b"\x02\xc0\x00\x02"),
    ("color_index_none", b"\x02\xc0\xef\xd2"),
    ("first_dark_theme_color", b"\x02\xc1\x00\x01"),
    ("first_light_theme_color", b"\x02\xc1\x00\x02"),
    ("second_dark_theme_color", b"\x02\xc1\x00\x03"),
    ("second_light_theme_color", b"\x02\xc1\x00\x04"),
    ("first_accent_theme_color", b"\x02\xc1\x00\x05"),
    ("second_accent_theme_color", b"\x02\xc1\x00\x06"),
    ("third_accent_theme_color", b"\x02\xc1\x00\x07"),
    ("fourth_accent_theme_color", b"\x02\xc1\x00\x08"),
    ("fifth_accent_theme_color", b"\x02\xc1\x00\t"),
    ("sixth_accent_theme_color", b"\x02\xc1\x00\n"),
    ("hyperlink_theme_color", b"\x02\xc1\x00\x0b"),
    ("followed_hyperlink_theme_color", b"\x02\xc1\x00\x0c"),
    ("minor_version", b"\x02\xc4\x00\x00"),
    ("major_version", b"\x02\xc4\x00\x01"),
    ("overwrite_current_version", b"\x02\xc4\x00\x02"),
    ("entire_page", b"\x02\xc5\x00\x00"),
    ("all_tables", b"\x02\xc5\x00\x01"),
    ("specified_tables", b"\x02\xc5\x00\x02"),
    ("web_formatting_all", b"\x02\xc6\x00\x00"),
    ("web_formatting_rtf", b"\x02\xc6\x00\x01"),
    ("web_formatting_none", b"\x02\xc6\x00\x02"),
    ("as_required", b"\x02\xc7\x00\x00"),
    ("always", b"\x02\xc7\x00\x01"),
    ("never", b"\x02\xc7\x00\x02"),
    ("condition_value_none", b"\x02\xc7\xff\xff"),
    ("condition_value_number", b"\x02\xc8\x00\x00"),
    ("condition_value_lowest_value", b"\x02\xc8\x00\x01"),
    ("condition_value_highest_value", b"\x02\xc8\x00\x02"),
    ("condition_value_percent", b"\x02\xc8\x00\x03"),
    ("condition_value_formula", b"\x02\xc8\x00\x04"),
    ("condition_value_percentile", b"\x02\xc8\x00\x05"),
    ("condition_value_automatic_minimum", b"\x02\xc8\x00\x06"),
    ("condition_value_automatic_maximum", b"\x02\xc8\x00\x07"),
    ("pivot_condition_selection_scope", b"\x02\xc9\x00\x00"),
    ("pivot_condition_fields_scope", b"\x02\xc9\x00\x01"),
    ("pivot_condition_data_field_scope", b"\x02\xc9\x00\x02"),
    ("databar_fill_solid", b"\x02\xca\x00\x00"),
    ("databar_fill_gradient", b"\x02\xca\x00\x01"),
    ("databar_border_none", b"\x02\xcb\x00\x00"),
    ("databar_border_solid", b"\x02\xcb\x00\x01"),
    ("databar_axis_automatic", b"\x02\xcc\x00\x00"),
    ("databar_axis_midpoint", b"\x02\xcc\x00\x01"),
    ("databar_axis_none", b"\x02\xcc\x00\x02"),
    ("databar_automatic", b"\x02\xcd\x00\x00"),
    ("databar_positive_format", b"\x02\xcd\x00\x01"),
    ("databar_custom_format", b"\x02\xcd\x00\x02"),
    ("format_condition_icon_no_cell_icon", b"\x02\xcd\xff\xff"),
    ("format_condition_icon_green_up_arrow", b"\x02\xce\x00\x01"),
    ("format_condition_icon_yellow_side_arrow", b"\x02\xce\x00\x02"),
    ("format_condition_icon_red_down_arrow", b"\x02\xce\x00\x03"),
    ("format_condition_icon_gray_up_arrow", b"\x02\xce\x00\x04"),
    ("format_condition_icon_gray_side_arrow", b"\x02\xce\x00\x05"),
    ("format_condition_icon_gray_down_arrow", b"\x02\xce\x00\x06"),
    ("format_condition_icon_green_flag", b"\x02\xce\x00\x07"),
    ("format_condition_icon_yellow_flag", b"\x02\xce\x00\x08"),
    ("format_condition_icon_red_flag", b"\x02\xce\x00\t"),
    ("format_condition_icon_green_circle", b"\x02\xce\x00\n"),
    ("format_condition_icon_yellow_circle", b"\x02\xce\x00\x0b"),
    ("format_condition_icon_red_circle_with_border", b"\x02\xce\x00\x0c"),
    ("format_condition_icon_black_circle_with_border", b"\x02\xce\x00\r"),
    ("format_condition_icon_green_traffic_light", b"\x02\xce\x00\x0e"),
    ("format_condition_icon_yellow_traffic_light", b"\x02\xce\x00\x0f"),
    ("format_condition_icon_red_traffic_light", b"\x02\xce\x00\x10"),
    ("format_condition_icon_yellow_triangle", b"\x02\xce\x00\x11"),
    ("format_condition_icon_red_diamond", b"\x02\xce\x00\x12"),
    ("format_condition_icon_green_check_symbol", b"\x02\xce\x00\x13"),
    ("format_condition_icon_yellow_exclamation_symbol", b"\x02\xce\x00\x14"),
    ("format_condition_icon_red_cross_symbol", b"\x02\xce\x00\x15"),
    ("format_condition_icon_green_check", b"\x02\xce\x00\x16"),
    ("format_condition_icon_yellow_exclamation", b"\x02\xce\x00\x17"),
    ("format_condition_icon_red_cross", b"\x02\xce\x00\x18"),
    ("format_condition_icon_yellow_up_incline_arrow", b"\x02\xce\x00\x19"),
    ("format_condition_icon_yellow_down_incline_arrow", b"\x02\xce\x00\x1a"),
    ("format_condition_icon_gray_up_incline_arrow", b"\x02\xce\x00\x1b"),
    ("format_condition_icon_gray_down_incline_arrow", b"\x02\xce\x00\x1c"),
    ("format_condition_icon_red_circle", b"\x02\xce\x00\x1d"),
    ("format_condition_icon_pink_circle", b"\x02\xce\x00\x1e"),
    ("format_condition_icon_gray_circle", b"\x02\xce\x00\x1f"),
    ("format_condition_icon_black_circle", b"\x02\xce\x00 "),
    ("format_condition_icon_circle_with_one_white_quarter", b"\x02\xce\x00!"),
    ("format_condition_icon_circle_with_two_white_quarters", b'\x02\xce\x00"'),
    ("format_condition_icon_circle_with_three_white_quarters", b"\x02\xce\x00#"),
    ("format_condition_icon_white_circle_all_white_quarters", b"\x02\xce\x00$"),
    ("format_condition_icon_0_bars", b"\x02\xce\x00%"),
    ("format_condition_icon_1_bar", b"\x02\xce\x00&"),
    ("format_condition_icon_2_bars", b"\x02\xce\x00'"),
    ("format_condition_icon_3_bars", b"\x02\xce\x00("),
    ("format_condition_icon_4_bars", b"\x02\xce\x00)"),
    ("format_condition_icon_gold_star", b"\x02\xce\x00*"),
    ("format_condition_icon_half_gold_star", b"\x02\xce\x00+"),
    ("format_condition_icon_silver_star", b"\x02\xce\x00,"),
    ("format_condition_icon_green_up_triangle", b"\x02\xce\x00-"),
    ("format_condition_icon_yellow_dash", b"\x02\xce\x00."),
    ("format_condition_icon_red_down_triangle", b"\x02\xce\x00/"),
    ("format_condition_icon_4_filled_boxes", b"\x02\xce\x000"),
    ("format_condition_icon_3_filled_boxes", b"\x02\xce\x001"),
    ("format_condition_icon_2_filled_boxes", b"\x02\xce\x002"),
    ("format_condition_icon_1_filled_box", b"\x02\xce\x003"),
    ("format_condition_icon_0_filled_boxes", b"\x02\xce\x004"),
    ("icon_set_custom", b"\x02\xce\xff\xff"),
    ("icon_set_3_arrows", b"\x02\xcf\x00\x01"),
    ("icon_set_3_arrows_gray", b"\x02\xcf\x00\x02"),
    ("icon_set_3_flags", b"\x02\xcf\x00\x03"),
    ("icon_set_3_traffic_lights_1", b"\x02\xcf\x00\x04"),
    ("icon_set_3_traffic_lights_2", b"\x02\xcf\x00\x05"),
    ("icon_set_3_signs", b"\x02\xcf\x00\x06"),
    ("icon_set_3_symbols", b"\x02\xcf\x00\x07"),
    ("icon_set_3_symbols_2", b"\x02\xcf\x00\x08"),
    ("icon_set_4_arrows", b"\x02\xcf\x00\t"),
    ("icon_set_4_arrows_gray", b"\x02\xcf\x00\n"),
    ("icon_set_4_red_to_black", b"\x02\xcf\x00\x0b"),
    ("icon_set_4_CRV", b"\x02\xcf\x00\x0c"),
    ("icon_set_4_traffic_lights", b"\x02\xcf\x00\r"),
    ("icon_set_5_arrows", b"\x02\xcf\x00\x0e"),
    ("icon_set_5_arrows_gray", b"\x02\xcf\x00\x0f"),
    ("icon_set_5_CRV", b"\x02\xcf\x00\x10"),
    ("icon_set_5_quarters", b"\x02\xcf\x00\x11"),
    ("icon_set_3_stars", b"\x02\xcf\x00\x12"),
    ("icon_set_3_triangles", b"\x02\xcf\x00\x13"),
    ("icon_set_5_boxes", b"\x02\xcf\x00\x14"),
    ("top_10_top", b"\x02\xd0\x00\x01"),
    ("top_10_bottom", b"\x02\xd0\x00\x00"),
    ("calc_for_all_values", b"\x02\xd1\x00\x00"),
    ("calc_for_row_groups", b"\x02\xd1\x00\x01"),
    ("calc_for_col_groups", b"\x02\xd1\x00\x02"),
    ("format_above_average", b"\x02\xd2\x00\x00"),
    ("format_below_average", b"\x02\xd2\x00\x01"),
    ("format_equal_above_average", b"\x02\xd2\x00\x02"),
    ("format_equal_below_average", b"\x02\xd2\x00\x03"),
    ("format_above_standard_deviation", b"\x02\xd2\x00\x04"),
    ("format_below_standard_deviation", b"\x02\xd2\x00\x05"),
    ("format_unique_values", b"\x02\xd3\x00\x00"),
    ("format_duplicate_values", b"\x02\xd3\x00\x01"),
    ("text_contains", b"\x02\xd4\x00\x00"),
    ("text_does_not_contain", b"\x02\xd4\x00\x01"),
    ("text_begins_with", b"\x02\xd4\x00\x02"),
    ("text_ends_with", b"\x02\xd4\x00\x03"),
    ("date_is_today", b"\x02\xd5\x00\x00"),
    ("date_is_yesterday", b"\x02\xd5\x00\x01"),
    ("date_is_within_the_last_seven_days", b"\x02\xd5\x00\x02"),
    ("date_is_this_week", b"\x02\xd5\x00\x03"),
    ("date_is_last_week", b"\x02\xd5\x00\x04"),
    ("date_is_last_month", b"\x02\xd5\x00\x05"),
    ("date_is_tomorrow", b"\x02\xd5\x00\x06"),
    ("date_is_next_week", b"\x02\xd5\x00\x07"),
    ("date_is_next_month", b"\x02\xd5\x00\x08"),
    ("date_is_this_month", b"\x02\xd5\x00\t"),
    ("databar_color_type_color", b"\x02\xd6\x00\x00"),
    ("databar_color_type_same_as_positive", b"\x02\xd6\x00\x01"),
    ("window", b"cwin"),
    ("pane", b"X189"),
    ("sheet", b"X128"),
    ("workbook", b"X141"),
    ("application", b"capp"),
    ("button", b"Xbtn"),
    ("checkbox", b"Xckb"),
    ("option_button", b"XObn"),
    ("groupbox", b"XGBc"),
    ("label", b"Xlbl"),
    ("textbox", b"XTbx"),
    ("scrollbar", b"XSrl"),
    ("listbox", b"XLbx"),
    ("dropdown", b"XdpD"),
    ("spinner", b"XSpn"),
    ("dialog", b"X165"),
    ("scenario", b"X191"),
    ("format_condition", b"X227"),
    ("color_scale_format_condition", b"X325"),
    ("databar_format_condition", b"X312"),
    ("icon_set_format_condition", b"X315"),
    ("top_10_format_condition", b"X321"),
    ("above_average_format_condition", b"X322"),
    ("unique_values_format_condition", b"X323"),
    ("pivot_table", b"X155"),
    ("pivot_field", b"X157"),
    ("cube_field", b"X900"),
    ("calculated_member", b"X901"),
    ("pivot_filter", b"X903"),
    ("value_change", b"X905"),
    ("pivot_item", b"X160"),
    ("pivot_cache", b"X151"),
    ("query_table", b"X231"),
    ("display_format", b"X306"),
    ("overflow", b"\x02\xc2\x00\x00"),
    ("clip", b"\x02\xc2\x00\x01"),
    ("ellipsis", b"\x02\xc2\x00\x02"),
    ("overflow", b"\x02\xc3\x00\x00"),
    ("clip", b"\x02\xc3\x00\x01"),
    ("callout_format", b"X101"),
    ("callout", b"cD00"),
    ("rectangle", b"XRct"),
    ("oval", b"XOvl"),
    ("arc", b"Xarc"),
    ("line", b"Xlne"),
    ("shape", b"pShp"),
    ("chart_fill_format", b"X253"),
    ("chart_title", b"X256"),
    ("axis_title", b"X257"),
    ("series_point", b"X262"),
    ("series", b"X263"),
    ("data_label", b"X265"),
    ("legend_key", b"X269"),
    ("down_bars", b"X279"),
    ("floor", b"X280"),
    ("walls", b"X281"),
    ("plot_area", b"X283"),
    ("chart_area", b"X284"),
    ("legend", b"X285"),
    ("display_unit_label", b"X299"),
    ("trendline", b"X271"),
    ("error_bars", b"X286"),
    ("chart", b"X119"),
    ("chart_object", b"X221"),
    ("axis", b"X255"),
]

properties = [
    ("class_", b"pcls"),
    ("properties", b"pALL"),
    ("frontmost", b"pisf"),
    ("name", b"pnam"),
    ("version", b"vers"),
    ("modified", b"imod"),
    ("bounds", b"pbnd"),
    ("closeable", b"hclb"),
    ("titled", b"ptit"),
    ("entry_index", b"pidx"),
    ("floating", b"isfl"),
    ("modal", b"pmod"),
    ("position", b"posn"),
    ("resizable", b"prsz"),
    ("zoomable", b"iszm"),
    ("zoomed", b"pzum"),
    ("visible", b"pvis"),
    ("collapsable", b"iscp"),
    ("collapsed", b"wshd"),
    ("sheet", b"issh"),
    ("copies", b"lwcp"),
    ("collating", b"lwcl"),
    ("starting_page", b"lwfp"),
    ("ending_page", b"lwlp"),
    ("pages_across", b"lwla"),
    ("pages_down", b"lwld"),
    ("requested_print_time", b"lwqt"),
    ("error_handling", b"lweh"),
    ("fax_number", b"faxn"),
    ("target_printer", b"trpr"),
    ("button_face_is_default", b"BTBi"),
    ("button_state", b"BTST"),
    ("button_style", b"BTSy"),
    ("face_id", b"BTFi"),
    ("combobox_style", b"CBSy"),
    ("combobox_text", b"CBtX"),
    ("drop_down_lines", b"CBdd"),
    ("drop_down_width", b"CBdw"),
    ("list_index", b"CBlI"),
    ("begin_group", b"BCbg"),
    ("built_in", b"pBtN"),
    ("control_type", b"cBcT"),
    ("description_text", b"BCDt"),
    ("enabled", b"enbl"),
    ("entry_index", b"MSix"),
    ("height", b"hght"),
    ("help_context_ID", b"BCHi"),
    ("help_file", b"BCHf"),
    ("id", b"BCId"),
    ("left_position", b"plft"),
    ("parameter", b"BCPa"),
    ("priority", b"BCPr"),
    ("tag", b"BCTg"),
    ("tooltip_text", b"BCTt"),
    ("top", b"ptop"),
    ("width", b"pwid"),
    ("bar_position", b"bPos"),
    ("bar_type", b"bTyp"),
    ("context", b"CbCT"),
    ("embeddable", b"pMbl"),
    ("embedded", b"pMbd"),
    ("local_name", b"CbNL"),
    ("protection", b"CBPt"),
    ("row_index", b"CBRi"),
    ("document_property_type", b"mDty"),
    ("link_source", b"DPLs"),
    ("link_to_content", b"DPLc"),
    ("value", b"DPVu"),
    ("fixed_width_font", b"WFfw"),
    ("fixed_width_font_size", b"WFfs"),
    ("proportional_font", b"WFpf"),
    ("proportional_font_size", b"WFps"),
    ("author", b"2123"),
    ("shape_object", b"2124"),
    ("error_string", b"1920"),
    ("sql_state", b"2167"),
    ("allow_deleting_columns", b"2442"),
    ("allow_deleting_rows", b"2443"),
    ("allow_filtering", b"2445"),
    ("allow_formatting_cells", b"2436"),
    ("allow_formatting_columns", b"2437"),
    ("allow_formatting_rows", b"2438"),
    ("allow_inserting_columns", b"2439"),
    ("allow_inserting_hyperlinks", b"2441"),
    ("allow_inserting_rows", b"2440"),
    ("allow_sorting", b"2444"),
    ("allow_using_pivot_table", b"2446"),
    ("above_or_below", b"2604"),
    ("applies_to", b"2568"),
    ("calc_for", b"2603"),
    ("font_object", b"XftO"),
    ("format_condition_priority", b"2566"),
    ("format_condition_type", b"2119"),
    ("interior_object", b"XitO"),
    ("number_format", b"1593"),
    ("number_of_standard_deviations", b"2605"),
    ("pivot_condition_scope_type", b"2574"),
    ("pivot_table_condition", b"2573"),
    ("stop_if_true", b"2567"),
    ("full_name", b"1773"),
    ("installed", b"1774"),
    ("path", b"1289"),
    ("AutoFormatAsYouTypeReplaceHyperlinks", b"LLnm"),
    ("Excel_cursor", b"1219"),
    ("ODBC_timeout", b"1282"),
    ("active_cell", b"1104"),
    ("active_chart", b"1105"),
    ("active_printer", b"1170"),
    ("active_sheet", b"1107"),
    ("active_window", b"1171"),
    ("active_workbook", b"1172"),
    ("alert_before_overwriting", b"1201"),
    ("alt_startup_path", b"1202"),
    ("arbitrary_XML_support_available", b"LLnp"),
    ("ask_to_update_links", b"1203"),
    ("autocorrect_object", b"XocO"),
    ("automation_security", b"Xaso"),
    ("build", b"1206"),
    ("calculate_before_save", b"1207"),
    ("calculation", b"1208"),
    ("calculation_version", b"1339"),
    ("caption", b"1108"),
    ("cell_drag_and_drop", b"1210"),
    ("command_underlines", b"1215"),
    ("copy_objects_with_cells", b"1218"),
    ("custom_list_count", b"1220"),
    ("cut_copy_mode", b"1221"),
    ("data_entry_mode", b"1222"),
    ("default_file_path", b"1234"),
    ("default_save_format", b"1314"),
    ("default_web_options_object", b"1332"),
    ("display_alerts", b"1238"),
    ("display_comment_indicator", b"1242"),
    ("display_excel_4_menus", b"1243"),
    ("display_formula_bar", b"XdFb"),
    ("display_full_screen", b"1240"),
    ("display_function_tooltips", b"XdfT"),
    ("display_insert_options", b"XdIo"),
    ("display_note_indicator", b"1241"),
    ("display_recent_files", b"1244"),
    ("display_scroll_bars", b"1245"),
    ("display_status_bar", b"1246"),
    ("edit_directly_in_cell", b"1248"),
    ("enable_animations", b"1204"),
    ("enable_autocomplete", b"1249"),
    ("enable_cancel_key", b"1250"),
    ("enable_events", b"1331"),
    ("enable_formula_autocomplete", b"fAcm"),
    ("enable_formula_type_ahead", b"fTyp"),
    ("enable_sound", b"1251"),
    ("extend_list", b"1335"),
    ("fixed_decimal", b"1255"),
    ("fixed_decimal_places", b"1256"),
    ("formula_autocomplete_wait", b"typW"),
    ("include_empty_cells_in_lists", b"XiEc"),
    ("iteration", b"1267"),
    ("keep_four_digit_years", b"1333"),
    ("keyboard_script", b"kbSc"),
    ("library_path", b"1268"),
    ("localized_language", b"LLng"),
    ("math_coprocessor_available", b"1270"),
    ("max_change", b"1271"),
    ("max_iterations", b"1272"),
    ("measurement_unit", b"1342"),
    ("memory_free", b"1273"),
    ("memory_total", b"1274"),
    ("memory_used", b"1275"),
    ("move_after_return", b"1277"),
    ("move_after_return_direction", b"1278"),
    ("network_templates_path", b"1280"),
    ("operating_system", b"1287"),
    ("organization_name", b"1288"),
    ("path_separator", b"1290"),
    ("pivot_table_selection", b"1292"),
    ("prompt_for_summary_info", b"1293"),
    ("reference_style", b"1297"),
    ("ribbon_expanded", b"EPRB"),
    ("roll_zoom", b"1301"),
    ("save_interval", b"1341"),
    ("screen_updating", b"1303"),
    ("selection", b"sele"),
    ("sheets_in_new_workbook", b"1305"),
    ("show_chart_tip_names", b"1306"),
    ("show_chart_tip_values", b"1307"),
    ("show_ribbon", b"SHRB"),
    ("show_tool_tips", b"1313"),
    ("spelling_options", b"xlsp"),
    ("standard_font", b"1308"),
    ("standard_font_size", b"1309"),
    ("startup_dialog", b"1337"),
    ("startup_path", b"1310"),
    ("status_bar", b"1311"),
    ("templates_path", b"1312"),
    ("this_cell", b"LLnn"),
    ("transition_menu_key", b"1315"),
    ("transition_menu_key_action", b"1316"),
    ("two_digit_cutoff_year", b"1334"),
    ("usable_height", b"1143"),
    ("usable_width", b"1144"),
    ("user_name", b"1320"),
    ("autofiltermode", b"2530"),
    ("range_object", b"2180"),
    ("sort_object", b"2533"),
    ("color", b"colr"),
    ("color_index", b"1098"),
    ("line_style", b"XlnS"),
    ("line_weight", b"XlnW"),
    ("theme_color", b"DThC"),
    ("tint_and_shade", b"2535"),
    ("weight", b"1031"),
    ("accelerator", b"1996"),
    ("add_indent", b"1514"),
    ("auto_scale_font", b"1992"),
    ("auto_size", b"1993"),
    ("bottom_right_cell", b"1983"),
    ("cancel_button", b"1997"),
    ("control_text", b"XcTx"),
    ("default_button", b"1998"),
    ("dismiss_button", b"1999"),
    ("formula", b"1562"),
    ("help_button", b"2000"),
    ("horizontal_alignment", b"1575"),
    ("locked", b"1584"),
    ("locked_text", b"1994"),
    ("on_action", b"BCOa"),
    ("orientation", b"1596"),
    ("phonetic_accelerator", b"2001"),
    ("placement", b"1986"),
    ("print_object", b"1987"),
    ("reading_order", b"1639"),
    ("top_left_cell", b"1989"),
    ("vertical_alignment", b"1631"),
    ("wrap_auto_text", b"1995"),
    ("z_order_position", b"2364"),
    ("_default_", b"CL12"),
    ("display_folder", b"CL15"),
    ("dynamic", b"CL14"),
    ("flatten_hierarchies", b"CL17"),
    ("formula", b"CL08"),
    ("hierarchize_distinct", b"CL16"),
    ("is_valid", b"CL11"),
    ("name", b"CL07"),
    ("solve_order", b"CL10"),
    ("source_name", b"1954"),
    ("type", b"CL13"),
    ("border", b"X251"),
    ("display_threeD_shading", b"2002"),
    ("linked_cell", b"2003"),
    ("color_scale_criterion_index", b"2575"),
    ("color_scale_criterion_type", b"2576"),
    ("color_scale_criterion_value", b"2577"),
    ("format_color", b"X307"),
    ("color_scale_criteria", b"X310"),
    ("color_scale_type", b"2609"),
    ("colorstop_position", b"2548"),
    ("condition_value_type", b"2564"),
    ("condition_value_value", b"2565"),
    ("all_items_visible", b"CL00"),
    ("cube_field_sub_type", b"CK99"),
    ("cube_field_type", b"CK80"),
    ("current_page_name", b"CL02"),
    ("drag_to_column", b"1962"),
    ("drag_to_data", b"1966"),
    ("drag_to_hide", b"1963"),
    ("drag_to_page", b"1964"),
    ("drag_to_row", b"1965"),
    ("enable_multiple_page_items", b"CK94"),
    ("flatten_hierarchies", b"CL05"),
    ("has_member_properties", b"CK91"),
    ("hierarchize_distinct", b"CL06"),
    ("include_new_items_in_filter", b"CK98"),
    ("is_date", b"CL03"),
    ("layout_form", b"CK92"),
    ("layout_subtotal_location", b"CK95"),
    ("name", b"CK81"),
    ("orientation", b"CK83"),
    ("position", b"CK84"),
    ("show_in_field_list", b"CK96"),
    ("treeview_control", b"CK85"),
    ("value", b"CK82"),
    ("custom_view_print_settings", b"2116"),
    ("row_col_settings", b"2117"),
    ("databar_border_color", b"2589"),
    ("databar_border_type", b"2588"),
    ("databar_axis_color", b"2587"),
    ("databar_axis_position", b"2586"),
    ("databar_bar_color", b"2582"),
    ("databar_border", b"X313"),
    ("databar_direction", b"2584"),
    ("databar_fill_type", b"2585"),
    ("format_condition_show_value", b"2583"),
    ("max_point_condition_value", b"2579"),
    ("max_point_percent", b"2581"),
    ("min_point_condition_value", b"2578"),
    ("min_point_percent", b"2580"),
    ("negative_bar_format", b"X314"),
    ("allow_png", b"2400"),
    ("always_save_in_default_encoding", b"2405"),
    ("encoding", b"2404"),
    ("location_of_components", b"2403"),
    ("pixels_per_inch", b"2402"),
    ("screen_size", b"2401"),
    ("update_links_on_save", b"2395"),
    ("formula_hidden", b"1565"),
    ("indent_level", b"1576"),
    ("merge_cells", b"1588"),
    ("shrink_to_fit", b"1618"),
    ("style_object", b"1029"),
    ("text_orientation", b"XtOr"),
    ("wrap_text", b"1633"),
    ("drop_down_lines", b"2021"),
    ("list_fill_range", b"2015"),
    ("number_of_items_in_list", b"XnIL"),
    ("criteria1", b"2191"),
    ("criteria2", b"2192"),
    ("filter_on", b"2190"),
    ("operator", b"2120"),
    ("format_condition_icon_index", b"2598"),
    ("icon_set_id", b"2599"),
    ("condition_operator", b"XfcO"),
    ("format_condition_date_operator", b"2608"),
    ("format_condition_text", b"XfcT"),
    ("format_condition_text_operator", b"2607"),
    ("formula_1", b"2121"),
    ("formula_2", b"2122"),
    ("brightness", b"1037"),
    ("color_type", b"1038"),
    ("contrast", b"1039"),
    ("crop_bottom", b"1040"),
    ("crop_left", b"1041"),
    ("crop_right", b"1042"),
    ("crop_top", b"1043"),
    ("file_name", b"AFLN"),
    ("lock_aspect_ratio", b"2207"),
    ("extent", b"1725"),
    ("horizontal_page_break_type", b"1726"),
    ("location", b"1694"),
    ("address", b"2182"),
    ("email_subject", b"2185"),
    ("hyperlink_type", b"2183"),
    ("screen_tip", b"2186"),
    ("sub_address", b"2181"),
    ("text_to_display", b"2187"),
    ("icon_criterion_icon", b"2597"),
    ("icon_criterion_index", b"2594"),
    ("icon_criterion_type", b"2595"),
    ("icon_criterion_value", b"2596"),
    ("format_condition_icon_set", b"X319"),
    ("icon_criteria", b"X316"),
    ("percentile_values", b"2592"),
    ("reverse_icon_set_order", b"2591"),
    ("show_icon_only", b"2593"),
    ("enable_selection", b"1740"),
    ("colorstops", b"2541"),
    ("linear_gradient_degree", b"2540"),
    ("cell_table", b"2193"),
    ("index", b"2508"),
    ("total_row", b"2194"),
    ("totals_calculation", b"2499"),
    ("active", b"2504"),
    ("autofilter_object", b"AfOj"),
    ("comment", b"2501"),
    ("default", b"2503"),
    ("display_name", b"2500"),
    ("display_right_to_left", b"2505"),
    ("header_row", b"2506"),
    ("insert_row", b"2507"),
    ("query_table", b"X231"),
    ("show_autofilter", b"2198"),
    ("show_headers", b"2502"),
    ("show_table_style_column_stripes", b"CJ39"),
    ("show_table_style_first_column", b"SJ30"),
    ("show_table_style_last_column", b"CJ37"),
    ("show_table_style_row_stripes", b"CJ38"),
    ("source_type", b"2509"),
    ("table_style", b"1936"),
    ("total", b"2196"),
    ("multi_select", b"2017"),
    ("category", b"2092"),
    ("category_local", b"2094"),
    ("macro_type", b"2095"),
    ("name_local", b"1772"),
    ("reference_local", b"2101"),
    ("reference_local_r1c1", b"2104"),
    ("reference_r1c1", b"2102"),
    ("reference_range", b"2105"),
    ("references", b"2098"),
    ("shortcut_key", b"2100"),
    ("negative_bar_border_color_type", b"2590"),
    ("negative_bar_color_type", b"2585"),
    ("group_box", b"2004"),
    ("automatic_styles", b"2048"),
    ("summary_column", b"2050"),
    ("summary_row", b"2051"),
    ("black_and_white", b"2055"),
    ("bottom_margin", b"2056"),
    ("center_footer", b"2057"),
    ("center_footer_picture", b"2087"),
    ("center_header", b"2058"),
    ("center_header_picture", b"2086"),
    ("center_horizontally", b"2059"),
    ("center_vertically", b"2060"),
    ("chart_size", b"2061"),
    ("draft", b"2062"),
    ("first_page_number", b"2063"),
    ("fit_to_pages_tall", b"2064"),
    ("fit_to_pages_wide", b"2065"),
    ("footer_margin", b"2066"),
    ("header_margin", b"2067"),
    ("left_footer", b"2068"),
    ("left_footer_picture", b"2089"),
    ("left_header", b"2069"),
    ("left_header_picture", b"2088"),
    ("left_margin", b"2070"),
    ("order", b"2071"),
    ("page_orientation", b"XPgO"),
    ("print_Excel_comments", b"2085"),
    ("print_area", b"2073"),
    ("print_gridlines", b"2074"),
    ("print_headings", b"2075"),
    ("print_notes", b"2076"),
    ("print_quality", b"XVpQ"),
    ("print_title_columns", b"2079"),
    ("print_title_rows", b"2080"),
    ("right_footer", b"2081"),
    ("right_footer_picture", b"2091"),
    ("right_header", b"2082"),
    ("right_header_picture", b"2090"),
    ("right_margin", b"2083"),
    ("top_margin", b"2084"),
    ("zoom", b"1148"),
    ("scroll_column", b"1131"),
    ("scroll_row", b"1132"),
    ("visible_range", b"1145"),
    ("character_type", b"2336"),
    ("phonetic_alignment", b"XpoA"),
    ("phonetic_text", b"phTx"),
    ("ADO_connection", b"CJ78"),
    ("OLAP", b"CJ81"),
    ("SQL_query", b"1886"),
    ("background_query", b"1878"),
    ("command_text", b"1889"),
    ("command_type", b"CJ69"),
    ("connection", b"1879"),
    ("enable_refresh", b"1880"),
    ("is_connected", b"CJ79"),
    ("local_connection", b"CJ75"),
    ("maintain_connection", b"CJ71"),
    ("missing_items_limit", b"CJ83"),
    ("optimize_cache", b"1881"),
    ("query_type", b"2155"),
    ("record_count", b"1882"),
    ("refresh_date", b"1883"),
    ("refresh_name", b"1884"),
    ("refresh_on_file_open", b"1885"),
    ("refresh_period", b"CJ72"),
    ("robust_connect", b"CJ85"),
    ("save_password", b"1887"),
    ("source_connection_file", b"CJ84"),
    ("source_data", b"1888"),
    ("source_type", b"CJ82"),
    ("upgrade_on_refresh", b"CJ89"),
    ("use_local_connection", b"CJ77"),
    ("version", b"CJ88"),
    ("workbook_connection", b"CJ87"),
    ("MDX", b"CK06"),
    ("cell_changed", b"CK05"),
    ("custom_subtotal_function", b"CJ99"),
    ("data_field", b"CJ93"),
    ("data_source_value", b"CK04"),
    ("pivot_cell_type", b"CJ91"),
    ("pivot_column_line", b"CK01"),
    ("pivot_field", b"CJ94"),
    ("pivot_item", b"CJ95"),
    ("pivot_row_line", b"CK00"),
    ("pivot_table", b"CJ92"),
    ("range", b"CJ98"),
    ("row_items", b"CJ96"),
    ("all_items_visible", b"CK40"),
    ("auto_show_count", b"1975"),
    ("auto_show_field", b"1976"),
    ("auto_show_range", b"1974"),
    ("auto_show_type", b"1973"),
    ("auto_sort_custom_subtotal", b"CK37"),
    ("auto_sort_field", b"1972"),
    ("auto_sort_order", b"1971"),
    ("auto_sort_pivot_line", b"CK36"),
    ("base_field", b"1957"),
    ("base_item", b"1958"),
    ("child_field", b"1941"),
    ("cube_field", b"CK14"),
    ("current_page", b"1943"),
    ("current_page_list", b"CK23"),
    ("current_page_name", b"CK15"),
    ("data_range", b"1944"),
    ("database_sort", b"CK18"),
    ("display_as_caption", b"CK31"),
    ("display_as_tooltip", b"CK29"),
    ("display_in_report", b"CK30"),
    ("drilled_down", b"CK13"),
    ("enable_item_selection", b"CK22"),
    ("enable_multiple_page_items", b"CK39"),
    ("function", b"1946"),
    ("group_level", b"1947"),
    ("hidden", b"1574"),
    ("hidden_items_list", b"CK17"),
    ("include_new_items_in_filter", b"CK33"),
    ("is_calculated", b"1967"),
    ("is_member_property", b"CK19"),
    ("label_range", b"1949"),
    ("layout_blank_line", b"CK07"),
    ("layout_compact_row", b"CK32"),
    ("layout_form", b"CK10"),
    ("layout_pagebreak", b"CK09"),
    ("layout_subtotal_location", b"CK08"),
    ("member_property_caption", b"CK28"),
    ("parent_field", b"1951"),
    ("pivot_field_data_type", b"XpfT"),
    ("pivot_field_orientation", b"XpfO"),
    ("property_order", b"CK21"),
    ("property_parent_field", b"CK20"),
    ("repeat_labels", b"CK48"),
    ("server_based", b"1968"),
    ("show_all_items", b"1950"),
    ("show_detail", b"1615"),
    ("showing_in_axis", b"CK38"),
    ("source_caption", b"CK46"),
    ("standard_formula", b"CK16"),
    ("subtotal_name", b"CK11"),
    ("total_levels", b"1959"),
    ("use_member_property_as_caption", b"CK27"),
    ("visible_items", b"PFvi"),
    ("visible_items_list", b"CK34"),
    ("active", b"CK64"),
    ("data_cube_field", b"CK67"),
    ("data_field", b"CK66"),
    ("description", b"CK62"),
    ("filter_type", b"CK50"),
    ("is_member_property_filter", b"CK71"),
    ("member_property_field", b"CK70"),
    ("name", b"CK61"),
    ("order", b"CK49"),
    ("pivot_field", b"CK65"),
    ("value1", b"CK68"),
    ("value2", b"DPV2"),
    ("standard_formula", b"CK72"),
    ("drilled_down", b"CK74"),
    ("parent_item", b"XPIp"),
    ("parent_show_detail", b"1978"),
    ("source_name_standard", b"CK76"),
    ("standard_formula", b"CK75"),
    ("line_type", b"CK77"),
    ("pivot_line_cells", b"CK79"),
    ("position", b"CK78"),
    ("CompactRowIndent", b"CJ28"),
    ("allocation", b"CJ52"),
    ("allocation_method", b"CJ54"),
    ("allocation_value", b"CJ53"),
    ("allocation_weight_expression", b"CJ55"),
    ("allow_multiple_filters", b"CJ43"),
    ("alternative_text", b"CJ63"),
    ("cache_index", b"1913"),
    ("calculated_members_in_filters", b"CJ67"),
    ("change_list", b"CJ61"),
    ("column_grand", b"1892"),
    ("column_range", b"1893"),
    ("compact_layout_column_header", b"CJ45"),
    ("compact_layout_row_header", b"CJ44"),
    ("data_body_range", b"1895"),
    ("data_label_range", b"1897"),
    ("data_pivot_field", b"CJ07"),
    ("display_context_tooltips", b"CJ26"),
    ("display_empty_column", b"CJ20"),
    ("display_empty_row", b"CJ19"),
    ("display_error_string", b"1915"),
    ("display_field_captions", b"CJ30"),
    ("display_immediate_items", b"CJ13"),
    ("display_member_property_tooltips", b"CJ25"),
    ("display_null_string", b"1916"),
    ("enable_data_value_editing", b"CJ08"),
    ("enable_drilldown", b"1917"),
    ("enable_field_dialog", b"1918"),
    ("enable_field_list", b"CJ14"),
    ("enable_wizard", b"1919"),
    ("enable_writeback", b"CJ51"),
    ("file_list_sort_ascending", b"CJ46"),
    ("grand_total_name", b"CJ02"),
    ("has_autoformat", b"1898"),
    ("in_grid_drop_zones", b"CJ34"),
    ("inner_detail", b"1900"),
    ("layout_row_default", b"CJ29"),
    ("location", b"CJ50"),
    ("manual_update", b"1923"),
    ("mdx", b"CJ10"),
    ("merge_labels", b"1924"),
    ("null_string", b"1925"),
    ("page_field_order", b"1929"),
    ("page_field_style", b"1930"),
    ("page_field_wrap_count", b"1931"),
    ("page_range", b"1902"),
    ("page_range_cells", b"1903"),
    ("pivot_cache", b"X151"),
    ("pivot_column_axis", b"CJ21"),
    ("pivot_row_axis", b"CJ22"),
    ("pivot_selection", b"1934"),
    ("pivot_selection_standard", b"CJ05"),
    ("preserve_formatting", b"1932"),
    ("print_drill_indicators", b"Cj24"),
    ("print_titles", b"CJ00"),
    ("repeat_items_on_each_printed_page", b"CJ03"),
    ("row_grand", b"1907"),
    ("row_range", b"1908"),
    ("save_data", b"1909"),
    ("selection_mode", b"1935"),
    ("show_drill_indicators", b"CJ23"),
    ("show_page_multiple_label", b"CJ16"),
    ("show_table_style_column_headers", b"CJ41"),
    ("show_table_style_row_headers", b"CJ40"),
    ("show_values_row", b"CJ66"),
    ("small_grid", b"1940"),
    ("sort_using_custom_lists", b"CJ47"),
    ("subtotal_hidden_page_items", b"1928"),
    ("summary", b"1622"),
    ("table_range1", b"1910"),
    ("table_range2", b"1911"),
    ("table_style2", b"CJ36"),
    ("totals_annotation", b"CJ04"),
    ("vacated_style", b"1939"),
    ("version", b"CJ17"),
    ("view_calculated_members", b"CJ11"),
    ("visual_totals", b"CJ15"),
    ("visual_totals_for_sets", b"CJ65"),
    ("FileMaker_fields", b"2158"),
    ("FileMaker_num_criteria", b"2159"),
    ("FileMaker_use_table", b"2212"),
    ("adjust_column_width", b"2141"),
    ("command_type", b"CmTe"),
    ("destination", b"2134"),
    ("enable_editing", b"2140"),
    ("fetched_row_overflow", b"2131"),
    ("field_names", b"2127"),
    ("fill_adjacent_formulas", b"2129"),
    ("post_text", b"2135"),
    ("refresh_style", b"2133"),
    ("refreshing", b"2130"),
    ("result_range", b"2136"),
    ("row_numbers", b"2128"),
    ("sql", b"1886"),
    ("tables_only_from_html", b"2139"),
    ("text_file_column_data_types", b"2152"),
    ("text_file_comma_delimiter", b"2149"),
    ("text_file_consecutive_delimiter", b"2146"),
    ("text_file_decimal_separator", b"2156"),
    ("text_file_fixed_column_widths", b"2153"),
    ("text_file_other_delimiter", b"2151"),
    ("text_file_parse_type", b"2144"),
    ("text_file_platform", b"2142"),
    ("text_file_prompt_on_refresh", b"2154"),
    ("text_file_semicolon_delimiter", b"2148"),
    ("text_file_space_delimiter", b"2150"),
    ("text_file_start_row", b"2143"),
    ("text_file_tab_delimiter", b"2147"),
    ("text_file_text_qualifier", b"2145"),
    ("text_file_thousands_separator", b"2157"),
    ("use_list_object", b"2162"),
    ("rectangular_gradient_bottom", b"2542"),
    ("rectangular_gradient_left", b"2543"),
    ("rectangular_gradient_right", b"2544"),
    ("rectangular_gradient_top", b"2545"),
    ("Excel_comment", b"X229"),
    ("changing_cells", b"2023"),
    ("large_change", b"2010"),
    ("maximum_value", b"XSMv"),
    ("minimum_value", b"XSmv"),
    ("small_change", b"2009"),
    ("autofilter_mode", b"1732"),
    ("circular_reference", b"1734"),
    ("consolidation_function", b"1736"),
    ("consolidation_options", b"COPT"),
    ("consolidation_sources", b"1738"),
    ("display_page_breaks", b"1760"),
    ("enable_autofilter", b"1739"),
    ("enable_calculation", b"1733"),
    ("enable_outlining", b"1741"),
    ("enable_pivot_table", b"1742"),
    ("filter_mode", b"1743"),
    ("next_", b"1591"),
    ("outline_object", b"1745"),
    ("page_setup_object", b"1654"),
    ("previous_", b"1606"),
    ("protect_contents", b"1656"),
    ("protect_drawing_objects", b"1657"),
    ("protection_mode", b"1658"),
    ("protection_object", b"2447"),
    ("scroll_area", b"1749"),
    ("sheet_tab", b"2551"),
    ("standard_height", b"1752"),
    ("standard_width", b"1753"),
    ("transition_expression_evaluation", b"1731"),
    ("used_range", b"1756"),
    ("worksheet_type", b"1755"),
    ("match_case", b"2512"),
    ("sort_header", b"2513"),
    ("sort_method", b"2515"),
    ("sort_orientation", b"2514"),
    ("sortfields", b"Xsfs"),
    ("sortrange", b"2511"),
    ("custom_order", b"2524"),
    ("sort_data_option", b"2525"),
    ("sort_key", b"2522"),
    ("sort_on", b"2520"),
    ("sort_on_values", b"2521"),
    ("sort_order", b"2523"),
    ("sort_priority", b"2526"),
    ("has_format", b"2610"),
    ("namelocal", b"2611"),
    ("show_as_available_pivot_table_style", b"2613"),
    ("show_as_available_table_style", b"2612"),
    ("rounded_corners", b"2028"),
    ("shadow", b"shad"),
    ("string_value", b"XRgt"),
    ("top_10_percentage", b"2602"),
    ("top_10_rank", b"2601"),
    ("top_or_bottom", b"2600"),
    ("drilled", b"CK25"),
    ("duplicate_or_unique", b"2606"),
    ("IME_mode", b"2171"),
    ("alert_style", b"2169"),
    ("error_message", b"2173"),
    ("error_title", b"2174"),
    ("formula1", b"2121"),
    ("formula2", b"2122"),
    ("ignore_blank", b"2170"),
    ("in_cell_dropdown", b"2172"),
    ("input_message", b"2175"),
    ("input_title", b"2176"),
    ("show_error", b"2177"),
    ("show_input", b"2178"),
    ("validation_operator", b"XlVo"),
    ("validation_type", b"2179"),
    ("allocation_method", b"CL30"),
    ("allocation_value", b"CL29"),
    ("allocation_weight_expression", b"CL31"),
    ("order", b"CL22"),
    ("pivot_cell", b"CL24"),
    ("tuple", b"CL27"),
    ("value", b"CL28"),
    ("visible_in_pivot_table", b"CL23"),
    ("vertical_page_break_type", b"1724"),
    ("web_page_keywords", b"2410"),
    ("web_page_title", b"2409"),
    ("active_pane", b"1106"),
    ("date_grouping", b"2534"),
    ("display_formulas", b"1110"),
    ("display_gridlines", b"1111"),
    ("display_headings", b"1112"),
    ("display_horizontal_scroll_bar", b"1113"),
    ("display_outline", b"1114"),
    ("display_vertical_scroll_bar", b"1116"),
    ("display_workbook_tabs", b"1117"),
    ("display_zeros", b"1118"),
    ("enable_resize", b"1119"),
    ("freeze_panes", b"1120"),
    ("gridline_color", b"1121"),
    ("gridline_color_index", b"1122"),
    ("range_selection", b"1130"),
    ("selected_sheets", b"1134"),
    ("split", b"1136"),
    ("split_column", b"1137"),
    ("split_horizontal", b"1138"),
    ("split_row", b"1139"),
    ("split_vertical", b"1140"),
    ("tab_ratio", b"1141"),
    ("view", b"1149"),
    ("window_number", b"1146"),
    ("window_state", b"1147"),
    ("window_type", b"1142"),
    ("_default_", b"CL20"),
    ("description", b"CL19"),
    ("name", b"CL18"),
    ("ranges", b"CL24"),
    ("type", b"CL21"),
    ("accept_labels_in_formulas", b"1796"),
    ("accuracy_version", b"1340"),
    ("auto_update_frequency", b"1797"),
    ("auto_update_save_changes", b"1798"),
    ("change_history_duration", b"1799"),
    ("conflict_resolution", b"1805"),
    ("create_backup", b"1807"),
    ("date_1904", b"1809"),
    ("default_pivottable_style", b"2619"),
    ("default_table_style", b"DTS3"),
    ("display_drawing_objects", b"1811"),
    ("enable_auto_recover", b"2557"),
    ("excel_8_compatibility_mode", b"2562"),
    ("file_format", b"1813"),
    ("full_name_urlencoded", b"2558"),
    ("has_password", b"1814"),
    ("has_vb_project", b"2561"),
    ("highlight_changes_on_screen", b"1857"),
    ("inactive_list_border_visible", b"2559"),
    ("is_add_in", b"1816"),
    ("keep_change_history", b"1858"),
    ("list_changes_on_new_sheet", b"1859"),
    ("multi_user_editing", b"1820"),
    ("password", b"1872"),
    ("personal_view_list_settings", b"1822"),
    ("personal_view_print_settings", b"1823"),
    ("precision_as_displayed", b"1826"),
    ("protect_structure", b"1828"),
    ("protect_windows", b"1829"),
    ("read_only", b"1830"),
    ("read_only_recommended", b"1874"),
    ("remove_personal_information", b"1870"),
    ("revision_number", b"1835"),
    ("save_link_values", b"1843"),
    ("saved", b"1842"),
    ("show_conflict_history", b"1845"),
    ("template_remove_external_data", b"1855"),
    ("theme", b"pThm"),
    ("update_remote_references", b"1850"),
    ("user_status", b"1851"),
    ("web_options", b"X301"),
    ("workbook_comments", b"XWBc"),
    ("write_password", b"1873"),
    ("write_reserved", b"1853"),
    ("write_reserved_by", b"1854"),
    ("protect_scenarios", b"1730"),
    ("dictionary_lang", b"dila"),
    ("adjustment_value", b"mAjv"),
    ("bullet_character", b"BtCh"),
    ("bullet_font", b"OblF"),
    ("bullet_number", b"BlNm"),
    ("bullet_start_value", b"bSvu"),
    ("bullet_style", b"bStl"),
    ("bullet_type", b"BLty"),
    ("relative_size", b"BRlS"),
    ("use_text_color", b"ButC"),
    ("use_text_font", b"bUtf"),
    ("accent", b"1007"),
    ("angle", b"1008"),
    ("auto_attach", b"1009"),
    ("auto_length", b"1010"),
    ("callout_format_length", b"1015"),
    ("callout_format_type", b"1016"),
    ("drop", b"1012"),
    ("drop_type", b"1013"),
    ("gap", b"1014"),
    ("callout_format", b"X101"),
    ("callout_type", b"coTY"),
    ("begin_connected", b"2381"),
    ("begin_connected_shape", b"2382"),
    ("begin_connection_site", b"2383"),
    ("connector_format_type", b"2387"),
    ("end_connected", b"2384"),
    ("end_connected_shape", b"2385"),
    ("end_connection_site", b"2386"),
    ("back_color", b"1019"),
    ("back_color_theme_index", b"fBCT"),
    ("fill_format_type", b"1095"),
    ("fore_color", b"1027"),
    ("fore_color_theme_index", b"fFCT"),
    ("gradient_color_type", b"1087"),
    ("gradient_degree", b"1088"),
    ("gradient_style", b"XgSy"),
    ("gradient_variant", b"1090"),
    ("pattern", b"1028"),
    ("preset_gradient_type", b"1091"),
    ("preset_texture", b"1092"),
    ("rotate_with_object", b"SsRo"),
    ("texture_alignment", b"FfTa"),
    ("texture_horizontal_scale", b"pTsX"),
    ("texture_name", b"1093"),
    ("texture_offset_X", b"pToX"),
    ("texture_offset_Y", b"pToY"),
    ("texture_tile", b"FfTt"),
    ("texture_type", b"1094"),
    ("texture_vertical_scale", b"pTsY"),
    ("transparency", b"1030"),
    ("color_theme_index", b"DThC"),
    ("radius", b"GRad"),
    ("begin_arrowhead_length", b"1020"),
    ("begin_arrowhead_style", b"1021"),
    ("begin_arrowhead_width", b"1022"),
    ("dash_style", b"1023"),
    ("end_arrowhead_length", b"1024"),
    ("end_arrowhead_style", b"1025"),
    ("end_arrowhead_width", b"1026"),
    ("arrowhead_length", b"2025"),
    ("arrowhead_style", b"2026"),
    ("arrowhead_width", b"2027"),
    ("theme_color_scheme", b"DTcS"),
    ("theme_effect_scheme", b"DTeS"),
    ("theme_font_scheme", b"DTfS"),
    ("alignment", b"1053"),
    ("baseline_alignment", b"BlAg"),
    ("bullet", b"xbf2"),
    ("east_asian_line_break_level", b"FelB"),
    ("first_line_indent", b"fLIn"),
    ("hanging_punctuation", b"Hfpu"),
    ("left_indent", b"IndL"),
    ("line_rule_after", b"lRAr"),
    ("line_rule_before", b"lRB4"),
    ("line_rule_within", b"lRwI"),
    ("right_indent", b"IndR"),
    ("space_after", b"SpcA"),
    ("space_before", b"SpcB"),
    ("space_within", b"SpcW"),
    ("text_direction", b"txTD"),
    ("word_wrap", b"PfWW"),
    ("transparency_color", b"1044"),
    ("transparent_background", b"1045"),
    ("link_to_file", b"l2Fl"),
    ("picture_format", b"X106"),
    ("save_with_document", b"SwFl"),
    ("reflection_type", b"ReFT"),
    ("first_margin", b"1Mar"),
    ("left_margin", b"lMar"),
    ("blur", b"1035"),
    ("obscured", b"1048"),
    ("offset_X", b"1049"),
    ("offset_Y", b"1050"),
    ("rotate_with_shape", b"SsRs"),
    ("shadow_style", b"Swss"),
    ("shadow_type", b"1051"),
    ("size", b"SwSs"),
    ("connector_format", b"X294"),
    ("connector_type", b"CFTy"),
    ("ASCII_name", b"fANm"),
    ("auto_rotate_numbers", b"FarN"),
    ("base_line_offset", b"fBlO"),
    ("bold", b"bold"),
    ("caps_type", b"fSCf"),
    ("east_asian_name", b"FEnm"),
    ("embedable", b"fEbD"),
    ("embedded", b"fEbF"),
    ("equalize_character_height", b"fEQu"),
    ("fill_format", b"X110"),
    ("font_color", b"Fclr"),
    ("font_color_theme_index", b"FCTI"),
    ("font_name", b"1056"),
    ("font_name_other", b"FNor"),
    ("font_size", b"ptsz"),
    ("glow_format", b"DGoF"),
    ("highlight_color", b"fHlC"),
    ("highlight_color_theme_index", b"fHCT"),
    ("italic", b"ital"),
    ("kerning", b"fKrn"),
    ("line_format", b"X103"),
    ("reflection_format", b"DReF"),
    ("shadow_format", b"X107"),
    ("soft_edge_type", b"SeFT"),
    ("spacing", b"fSpc"),
    ("strike_type", b"fSTf"),
    ("subscript", b"sbsc"),
    ("superscript", b"spsc"),
    ("underline_color", b"fUlC"),
    ("underline_color_theme_index", b"fUCT"),
    ("underline_style", b"fUls"),
    ("word_art_styles_format", b"TEpE"),
    ("begin_line_X", b"wLBx"),
    ("begin_line_Y", b"wLBy"),
    ("end_line_X", b"wLex"),
    ("end_line_Y", b"wLey"),
    ("has_text", b"TFht"),
    ("horizontal_anchor", b"TfHA"),
    ("margin_bottom", b"2371"),
    ("margin_left", b"2372"),
    ("margin_right", b"2373"),
    ("margin_top", b"2374"),
    ("path_format", b"TfPF"),
    ("ruler", b"xRul"),
    ("text_column", b"Tcl2"),
    ("text_range", b"TObj"),
    ("threeD_format", b"X109"),
    ("vertical_anchor", b"TfVA"),
    ("warp_format", b"TfWF"),
    ("word_wrap", b"TfWW"),
    ("wordart_auto_size", b"2427"),
    ("wordart_format", b"TEpE"),
    ("alternative_text", b"atxt"),
    ("auto_shape_type", b"2349"),
    ("background_style", b"sHBs"),
    ("black_white_mode", b"2366"),
    ("chart", b"X119"),
    ("child", b"sHCl"),
    ("connection_site_count", b"2351"),
    ("connector", b"2352"),
    ("has_chart", b"fCrt"),
    ("horizontal_flip", b"2355"),
    ("hyperlink", b"X239"),
    ("parentgroup", b"prng"),
    ("rotation", b"1703"),
    ("shape_on_action", b"ShOa"),
    ("shape_style", b"sHSs"),
    ("shape_text_frame", b"X295"),
    ("shape_type", b"2361"),
    ("soft_edge_format", b"DSeF"),
    ("text_frame", b"X293"),
    ("vertical_flip", b"2362"),
    ("word_art_format", b"X108"),
    ("tab_position", b"TSPn"),
    ("tab_stop_type", b"TSty"),
    ("column_number", b"T2Nm"),
    ("auto_margins", b"2376"),
    ("horizontal_overflow", b"hzno"),
    ("vertical_overflow", b"vrto"),
    ("RGB", b"tRGB"),
    ("theme_color_scheme_index", b"TCSi"),
    ("Z_distance", b"TfZd"),
    ("bevel_bottom_depth", b"TfBd"),
    ("bevel_bottom_inset", b"TfBi"),
    ("bevel_bottom_type", b"TfBb"),
    ("bevel_top_depth", b"TfTd"),
    ("bevel_top_inset", b"TfTi"),
    ("bevel_top_type", b"TfBt"),
    ("contour_color", b"TfCc"),
    ("contour_color_theme_index", b"cCTi"),
    ("contour_width", b"TfCw"),
    ("depth", b"1068"),
    ("extrusion_color", b"1069"),
    ("extrusion_color_theme_index", b"eCTi"),
    ("extrusion_color_type", b"1070"),
    ("field_of_view", b"TfFv"),
    ("light_angle", b"TfLa"),
    ("perspective", b"1071"),
    ("preset_camera", b"TfSp"),
    ("preset_extrusion_direction", b"1072"),
    ("preset_lighting_direction", b"1073"),
    ("preset_lighting_rig", b"TfPl"),
    ("preset_lighting_softness", b"1074"),
    ("preset_material", b"1075"),
    ("preset_threeD_format", b"1076"),
    ("project_text", b"TfPt"),
    ("rotation_X", b"1077"),
    ("rotation_Y", b"1078"),
    ("rotation_Z", b"TfZr"),
    ("kerned_pairs", b"1057"),
    ("normalized_height", b"1058"),
    ("preset_shape", b"1059"),
    ("preset_word_art_effect", b"4028"),
    ("rotated_chars", b"1061"),
    ("tracking", b"1062"),
    ("word_art_text", b"XWAt"),
    ("content", b"XtCt"),
    ("phonetic_characters", b"1979"),
    ("font_background", b"XfBg"),
    ("font_color_index", b"XfcI"),
    ("font_style", b"1099"),
    ("outline_font", b"1100"),
    ("strikethrough", b"strk"),
    ("theme_font_index", b"2550"),
    ("underline", b"undl"),
    ("include_alignment", b"1766"),
    ("include_border", b"1767"),
    ("include_font", b"1768"),
    ("include_number", b"1769"),
    ("include_patterns", b"1770"),
    ("include_protection", b"1771"),
    ("merged_cells", b"1588"),
    ("number_format_local", b"1594"),
    ("bound_height", b"TRbh"),
    ("bound_left", b"TRlb"),
    ("bound_top", b"TRtb"),
    ("bound_width", b"TRbw"),
    ("font", b"X111"),
    ("paragraph_format", b"xpf2"),
    ("text_length", b"TRln"),
    ("text_start", b"TRst"),
    ("areas", b"1520"),
    ("column_width", b"1536"),
    ("count_large", b"XCcs"),
    ("current_array", b"1541"),
    ("current_region", b"1542"),
    ("dependents", b"1548"),
    ("direct_dependents", b"1550"),
    ("direct_precedents", b"1551"),
    ("display_format", b"X306"),
    ("entire_column", b"1553"),
    ("entire_row", b"1554"),
    ("first_column_index", b"XfcX"),
    ("first_row_index", b"XfrX"),
    ("formula_array", b"1563"),
    ("formula_label", b"1564"),
    ("formula_local", b"1566"),
    ("formula_r1c1", b"1567"),
    ("formula_r1c1_local", b"1568"),
    ("has_array", b"1572"),
    ("has_formula", b"1573"),
    ("list_header_rows", b"1581"),
    ("list_object", b"X244"),
    ("location_in_table", b"1583"),
    ("merge_area", b"1587"),
    ("named_item", b"X220"),
    ("outline_level", b"1597"),
    ("page_break", b"1598"),
    ("phonetic_object", b"1637"),
    ("pivot_field", b"X157"),
    ("pivot_item", b"X160"),
    ("pivot_table", b"X155"),
    ("precedents", b"1604"),
    ("prefix_character", b"1605"),
    ("row_height", b"1611"),
    ("use_standard_height", b"1625"),
    ("use_standard_width", b"1626"),
    ("validation", b"X237"),
    ("worksheet_object", b"1632"),
    ("Automatically_expand_tables_as_I_type", b"Expd"),
    ("Automatically_fill_formulas", b"AtFF"),
    ("correct_caps_lock", b"2216"),
    ("correct_days", b"2209"),
    ("correct_initial_caps", b"2214"),
    ("correct_sentence_caps", b"2215"),
    ("replace_text", b"2213"),
    ("axis_title_text", b"AtTx"),
    ("chart_fill_format_object", b"XCFf"),
    ("include_in_layout", b"1985"),
    ("axis_between_categories", b"2223"),
    ("axis_group", b"AgOb"),
    ("axis_title", b"X257"),
    ("axis_type", b"XAty"),
    ("base_unit", b"2250"),
    ("base_unit_is_auto", b"2251"),
    ("category_names", b"2226"),
    ("category_type", b"2254"),
    ("chart_format", b"X115"),
    ("crosses", b"2227"),
    ("crosses_at", b"2228"),
    ("display_unit", b"2255"),
    ("display_unit_custom", b"2256"),
    ("display_unit_label", b"X299"),
    ("has_display_unit_label", b"2257"),
    ("has_major_gridlines", b"2229"),
    ("has_minor_gridlines", b"2230"),
    ("has_title", b"1689"),
    ("log_base", b"2258"),
    ("major_gridlines", b"2231"),
    ("major_tick_mark", b"2232"),
    ("major_unit", b"2233"),
    ("major_unit_is_auto", b"2234"),
    ("major_unit_scale", b"2252"),
    ("maximum_scale", b"2235"),
    ("maximum_scale_is_auto", b"2236"),
    ("minimum_scale", b"2237"),
    ("minimum_scale_is_auto", b"2238"),
    ("minor_gridlines", b"2239"),
    ("minor_tick_mark", b"2240"),
    ("minor_unit", b"2241"),
    ("minor_unit_is_auto", b"2242"),
    ("minor_unit_scale", b"2253"),
    ("reverse_plot_order", b"2243"),
    ("scale_type", b"2244"),
    ("tick_label_position", b"2245"),
    ("tick_label_spacing", b"2247"),
    ("tick_label_spacing_is_auto", b"2259"),
    ("tick_labels", b"X282"),
    ("tick_mark_spacing", b"2248"),
    ("background_scheme_color", b"bgSC"),
    ("chart_fill_format_type", b"2221"),
    ("foreground_scheme_color", b"fgSC"),
    ("bubble_scale", b"2277"),
    ("doughnut_hole_size", b"2260"),
    ("down_bars_object", b"XdbO"),
    ("drop_lines_object", b"XdlO"),
    ("first_slice_angle", b"2263"),
    ("gap_width", b"2264"),
    ("has_drop_lines", b"2265"),
    ("has_hi_lo_lines", b"2266"),
    ("has_radar_axis_labels", b"2267"),
    ("has_series_lines", b"2268"),
    ("has_threeD_shading", b"2282"),
    ("has_up_down_bars", b"2269"),
    ("hilo_lines_Object", b"XhlO"),
    ("overlap", b"2271"),
    ("radar_axis_labels", b"2272"),
    ("second_plot_size", b"2281"),
    ("series_lines_object", b"XslO"),
    ("show_negative_bubbles", b"2278"),
    ("size_represents", b"2276"),
    ("split_type", b"2279"),
    ("split_value", b"2280"),
    ("up_bars_object", b"XubO"),
    ("vary_by_categories", b"2275"),
    ("protect_chart_object", b"2107"),
    ("chart_title_text", b"XctT"),
    ("area_threeD_group", b"1663"),
    ("auto_scaling", b"1665"),
    ("back_wall", b"2429"),
    ("bar_shape", b"1713"),
    ("bar_threeD_group", b"1668"),
    ("chart_area_object", b"1670"),
    ("chart_style", b"2433"),
    ("chart_title", b"X256"),
    ("chart_type", b"1708"),
    ("column_threeD_group", b"1675"),
    ("corners_object", b"XcrO"),
    ("data_table_object", b"1678"),
    ("depth_percent", b"1679"),
    ("display_blanks_as", b"1681"),
    ("elevation", b"1683"),
    ("floor_object", b"1455"),
    ("gap_depth", b"1684"),
    ("has_data_table", b"1687"),
    ("has_legend", b"1688"),
    ("height_percent", b"1690"),
    ("legend_object", b"1691"),
    ("line_threeD_group", b"C3DG"),
    ("pie_threeD_group", b"1697"),
    ("plot_area_object", b"1699"),
    ("plot_by", b"1714"),
    ("plot_visible_only", b"1700"),
    ("protect_data", b"1716"),
    ("protect_formatting", b"1715"),
    ("protect_goal_seek", b"1717"),
    ("protect_selection", b"1718"),
    ("right_angle_axes", b"1702"),
    ("show_data_labels_over_maximum", b"2434"),
    ("show_window", b"1706"),
    ("side_wall", b"2428"),
    ("size_with_window", b"1705"),
    ("surface_group", b"1707"),
    ("walls_and_gridlines_twoD", b"1711"),
    ("walls_object", b"1710"),
    ("auto_text", b"2313"),
    ("data_label_text", b"XdlT"),
    ("data_label_type", b"2316"),
    ("number_format_linked", b"2314"),
    ("show_legend_key", b"2315"),
    ("has_border_horizontal", b"2333"),
    ("has_border_outline", b"2335"),
    ("has_border_vertical", b"2334"),
    ("display_label_unit_text", b"DLuT"),
    ("end_style", b"2332"),
    ("picture_type", b"2292"),
    ("thickness", b"Thck"),
    ("invert_if_negative", b"2218"),
    ("linear_gradient", b"X302"),
    ("pattern_color", b"2219"),
    ("pattern_color_index", b"2220"),
    ("pattern_theme_color", b"2536"),
    ("pattern_tint_and_shade", b"2537"),
    ("rectangular_gradient", b"X303"),
    ("legend_key", b"X269"),
    ("marker_background_color", b"2286"),
    ("marker_background_color_index", b"2287"),
    ("marker_foreground_color", b"2288"),
    ("marker_foreground_color_index", b"2289"),
    ("marker_size", b"2290"),
    ("marker_style", b"2291"),
    ("picture_unit", b"2293"),
    ("smooth", b"2304"),
    ("inside_height", b"2330"),
    ("inside_left", b"2327"),
    ("inside_top", b"2328"),
    ("inside_width", b"2329"),
    ("apply_pict_to_end", b"2296"),
    ("apply_pict_to_front", b"2295"),
    ("apply_pict_to_sides", b"2294"),
    ("data_label_object", b"XdlO"),
    ("explosion", b"2284"),
    ("has_data_label", b"2285"),
    ("has_threeD_effect", b"2310"),
    ("secondary_plot", b"2297"),
    ("apply_picture_to_end", b"2296"),
    ("apply_picture_to_front", b"2295"),
    ("apply_picture_to_sides", b"2294"),
    ("bubble_sizes", b"2309"),
    ("error_bars", b"X286"),
    ("has_data_labels", b"2301"),
    ("has_error_bars", b"2302"),
    ("has_leader_lines", b"2311"),
    ("leader_lines", b"X277"),
    ("plot_color_index", b"pcli"),
    ("plot_order", b"2303"),
    ("series_values", b"XsrV"),
    ("xvalues", b"2308"),
    ("multi_level", b"2312"),
    ("offset", b"2326"),
    ("tick_alignment", b"XtAl"),
    ("backward", b"2318"),
    ("display_R_squared", b"2320"),
    ("display_equation", b"2319"),
    ("forward", b"2321"),
    ("intercept", b"1481"),
    ("intercept_is_auto", b"2322"),
    ("name_is_auto", b"2323"),
    ("period", b"2324"),
    ("trendline_type", b"2325"),
]

elements = [
    ("base_documents", b"bDoc"),
    ("basic_windows", b"bwin"),
    ("command_bar_buttons", b"mCBB"),
    ("command_bar_comboboxes", b"mCBX"),
    ("command_bar_controls", b"mCBC"),
    ("command_bar_popups", b"mCBP"),
    ("command_bars", b"msCB"),
    ("custom_document_properties", b"mCDP"),
    ("document_properties", b"mDPr"),
    ("Excel_comments", b"X229"),
    ("ODBC_errors", b"X235"),
    ("active_filters", b"Y903"),
    ("add_ins", b"X133"),
    ("applications", b"capp"),
    ("autofilters", b"X240"),
    ("borders", b"X251"),
    ("buttons", b"Xbtn"),
    ("calculated_fields", b"XPFc"),
    ("calculated_items", b"XPIi"),
    ("calculated_members", b"X901"),
    ("checkboxes", b"Xckb"),
    ("child_items", b"XPIc"),
    ("column_fields", b"XPFn"),
    ("column_items", b"XPIo"),
    ("cube_fields", b"X900"),
    ("custom_views", b"X225"),
    ("data_fields", b"XPFd"),
    ("dialogs", b"X165"),
    ("documents", b"docu"),
    ("dropdowns", b"XdpD"),
    ("filters", b"X242"),
    ("format_conditions", b"X227"),
    ("graphics", b"X308"),
    ("groupboxes", b"XGBc"),
    ("hidden_fields", b"XPFh"),
    ("hidden_items", b"XPIh"),
    ("horizontal_page_breaks", b"X122"),
    ("hyperlinks", b"X239"),
    ("international_macro_sheets", b"XiSH"),
    ("labels", b"Xlbl"),
    ("list_columns", b"X248"),
    ("list_objects", b"X244"),
    ("list_rows", b"X246"),
    ("listboxes", b"XLbx"),
    ("macro_sheets", b"XmSH"),
    ("named_items", b"X220"),
    ("option_buttons", b"XObn"),
    ("outlines", b"X212"),
    ("page_fields", b"XPFp"),
    ("page_setups", b"X218"),
    ("panes", b"X189"),
    ("parent_items", b"XPIp"),
    ("phonetics", b"X288"),
    ("pivot_caches", b"X151"),
    ("pivot_fields", b"X157"),
    ("pivot_filters", b"X903"),
    ("pivot_formulas", b"X153"),
    ("pivot_items", b"X160"),
    ("pivot_lines", b"X907"),
    ("pivot_tables", b"X155"),
    ("query_tables", b"X231"),
    ("recent_files", b"X125"),
    ("row_fields", b"XPFr"),
    ("row_items", b"XPIr"),
    ("scenarios", b"X191"),
    ("scrollbars", b"XSrl"),
    ("sheets", b"X128"),
    ("slicers", b"X906"),
    ("spinners", b"XSpn"),
    ("table_style_elements", b"TSET"),
    ("table_styles", b"1936"),
    ("textboxes", b"XTbx"),
    ("validations", b"X237"),
    ("vertical_page_breaks", b"X121"),
    ("windows", b"cwin"),
    ("workbooks", b"X141"),
    ("worksheets", b"XwSH"),
    ("adjustments", b"mAdj"),
    ("arcs", b"Xarc"),
    ("callout_formats", b"X101"),
    ("callouts", b"cD00"),
    ("connector_formats", b"X294"),
    ("fill_formats", b"X110"),
    ("gradient_stops", b"GrdS"),
    ("line_formats", b"X103"),
    ("lines", b"Xlne"),
    ("major_theme_fonts", b"1ThF"),
    ("minor_theme_fonts", b"2ThF"),
    ("ovals", b"XOvl"),
    ("picture_formats", b"X106"),
    ("pictures", b"cD04"),
    ("rectangles", b"XRct"),
    ("ruler_levels", b"xRlL"),
    ("shadow_formats", b"X107"),
    ("shape_connectors", b"cD01"),
    ("shape_lines", b"cD12"),
    ("shape_text_frames", b"X295"),
    ("shape_textboxes", b"cD07"),
    ("shapes", b"pShp"),
    ("tab_stops", b"Tab2"),
    ("text_frames", b"X293"),
    ("theme_fonts", b"DThF"),
    ("threeD_formats", b"X109"),
    ("word_art_formats", b"X108"),
    ("characters", b"cha "),
    ("fonts", b"X111"),
    ("paragraphs", b"cpar"),
    ("sentences", b"csen"),
    ("styles", b"X129"),
    ("text_flows", b"cflo"),
    ("text_range_characters", b"TrCh"),
    ("text_range_lines", b"TrLn"),
    ("words", b"cwor"),
    ("cells", b"ccel"),
    ("columns", b"ccol"),
    ("ranges", b"X117"),
    ("rows", b"crow"),
    ("area_groups", b"cg01"),
    ("axis_titles", b"X257"),
    ("axes", b"X255"),
    ("bar_groups", b"cg05"),
    ("chart_areas", b"X284"),
    ("chart_fill_formats", b"X253"),
    ("chart_groups", b"X258"),
    ("chart_objects", b"X221"),
    ("chart_sheets", b"XcSH"),
    ("chart_titles", b"X256"),
    ("charts", b"X119"),
    ("column_groups", b"cg04"),
    ("data_labels", b"X265"),
    ("data_tables", b"X287"),
    ("display_unit_labels", b"X299"),
    ("doughnut_groups", b"cg03"),
    ("floors", b"X280"),
    ("interiors", b"X252"),
    ("legend_entries", b"X267"),
    ("legend_keys", b"X269"),
    ("legends", b"X285"),
    ("line_groups", b"cg02"),
    ("pie_groups", b"cg06"),
    ("plot_areas", b"X283"),
    ("radar_groups", b"cg07"),
    ("series_points", b"X262"),
    ("series_collection", b"X263"),
    ("trendlines", b"X271"),
    ("walls_collection", b"X281"),
    ("xy_groups", b"cg09"),
    ("xlspelling_options", b"Xspo"),
    ("error_bars", b"X286"),
    ("pivot_cell", b"X908"),
    ("up_bars", b"X278"),
    ("tick_labels", b"X282"),
    ("rectangular_gradient", b"X303"),
    ("format_color", b"X307"),
    ("icon_criterion", b"X317"),
    ("theme_effect_scheme", b"DTeS"),
    ("format_condition_icon_object", b"X318"),
    ("workbook_connection", b"X904"),
    ("databar_border", b"X313"),
    ("web_page_font", b"mWPF"),
    ("soft_edge_format", b"DSeF"),
    ("databar_format_condition", b"X312"),
    ("negative_bar_format", b"X314"),
    ("default_web_options", b"X300"),
    ("color_scale_criterion", b"X311"),
    ("sort", b"Xsrt"),
    ("treeview_control", b"X911"),
    ("autocorrect", b"X250"),
    ("reflection_format", b"DReF"),
    ("down_bars", b"X279"),
    ("theme_color", b"DThC"),
    ("text_column", b"Tcl2"),
    ("format_condition_icon_sets", b"X320"),
    ("colorstops", b"X304"),
    ("unique_values_format_condition", b"X323"),
    ("office_theme", b"DOfT"),
    ("ruler", b"xRul"),
    ("theme_font_scheme", b"DTfS"),
    ("corners", b"X272"),
    ("above_average_format_condition", b"X322"),
    ("leader_lines", b"X277"),
    ("color_scale_format_condition", b"X325"),
    ("drop_lines", b"X276"),
    ("chart_format", b"X115"),
    ("sortfields", b"Xsfs"),
    ("paragraph_format", b"xpf2"),
    ("colorstop", b"X305"),
    ("linear_gradient", b"X302"),
    ("Protection", b"Xpot"),
    ("display_format", b"X306"),
    ("format_condition_icon_set", b"X319"),
    ("condition_value", b"X324"),
    ("base_application", b"cbap"),
    ("icon_criteria", b"X316"),
    ("base_object", b"oItm"),
    ("bullet_format", b"xbf2"),
    ("value_change", b"X905"),
    ("tab", b"Xtab"),
    ("web_options", b"X301"),
    ("print_settings", b"pset"),
    ("text_range", b"TObj"),
    ("pivot_axis", b"X902"),
    ("glow_format", b"DGoF"),
    ("top_10_format_condition", b"X321"),
    ("color_scale_criteria", b"X310"),
    ("shape_font", b"Fon2"),
    ("gridlines", b"X275"),
    ("icon_set_format_condition", b"X315"),
    ("series_lines", b"X273"),
    ("sortfield", b"Xsfd"),
    ("theme_color_scheme", b"DTcS"),
    ("hilo_lines", b"X274"),
]

commands = [
    (
        "protect_workbook",
        b"smXLXPTw",
        [("password", b"5236"), ("structure", b"5293"), ("windows", b"1192")],
    ),
    ("preset_drop", b"sDRw1006", [("drop_type", b"5004")]),
    (
        "intersect",
        b"smXL1182",
        [
            ("range1", b"XLr1"),
            ("range2", b"XLr2"),
            ("range3", b"XLr3"),
            ("range4", b"XLr4"),
            ("range5", b"XLr5"),
            ("range6", b"XLr6"),
            ("range7", b"XLr7"),
            ("range8", b"XLr8"),
            ("range9", b"XLr9"),
            ("range10", b"Xr10"),
            ("range11", b"Xr11"),
            ("range12", b"Xr12"),
            ("range13", b"Xr13"),
            ("range14", b"Xr14"),
            ("range15", b"Xr15"),
            ("range16", b"Xr16"),
            ("range17", b"Xr17"),
            ("range18", b"Xr18"),
            ("range19", b"Xr19"),
            ("range20", b"Xr20"),
            ("range21", b"Xr21"),
            ("range22", b"Xr22"),
            ("range23", b"Xr23"),
            ("range24", b"Xr24"),
            ("range25", b"Xr25"),
            ("range26", b"Xr26"),
            ("range27", b"Xr27"),
            ("range28", b"Xr28"),
            ("range29", b"Xr29"),
            ("range30", b"Xr30"),
        ],
    ),
    ("get_end", b"sTBL1552", [("direction", b"5165")]),
    ("delete_chart_autoformat", b"smXL1235", [("name", b"pnam")]),
    ("quit", b"aevtquit", [("saving", b"savo")]),
    (
        "chart_one_color_gradient",
        b"sCRTXc1c",
        [("gradient_style", b"XgSy"), ("variant", b"5008"), ("degree", b"5009")],
    ),
    ("user_textured", b"sDRw1086", [("texture_file", b"5013")]),
    ("select", b"coreslct", []),
    ("get_international", b"smXL1266", [("data_type", b"5216")]),
    ("apply_layout", b"sCRTACLo", [("layout", b"5358"), ("chart_type", b"5359")]),
    (
        "check_spelling_for",
        b"smXLXcsW",
        [
            ("text_to_check", b"5080"),
            ("custom_dictionary", b"5081"),
            ("ignore_uppercase", b"5082"),
        ],
    ),
    (
        "change_link",
        b"smXL1802",
        [("name", b"pnam"), ("new_name", b"5289"), ("type", b"5103")],
    ),
    ("load_theme_font_scheme", b"sDRwlTFS", [("file_name", b"5015")]),
    ("update", b"smXL1938", []),
    ("find_previous", b"sTBL1560", [("after_", b"5167")]),
    ("chart_patterned", b"sCRT1080", [("pattern", b"1028")]),
    (
        "link_info",
        b"smXL1817",
        [("name", b"pnam"), ("link_info", b"1817"), ("type", b"5103")],
    ),
    ("cancel_refresh", b"smXL2132", []),
    ("get_file_converters", b"smXL1252", []),
    (
        "add_chart_autoformat",
        b"smXL1199",
        [("chart", b"5075"), ("name", b"pnam"), ("description", b"5076")],
    ),
    ("print_preview", b"smXL1129", [("enable_changes", b"5029")]),
    ("merge_workbook", b"smXL1819", [("file_name", b"5016")]),
    ("remove_user", b"smXL1834", [("entry_index", b"MSix")]),
    ("remove_item", b"smXLXrMI", [("entry_index", b"MSix"), ("count", b"1000")]),
    ("apply", b"sDRw2337", []),
    ("subtotal_location", b"smXLCJ32", [("location", b"6038")]),
    ("clear_circles", b"smXL1762", []),
    ("activate_previous", b"smXL1103", []),
    ("discard_change", b"smXLCK03", []),
    ("load_theme_color_scheme", b"sDRwlTCS", [("file_name", b"5016")]),
    ("autofit", b"sTBL1524", []),
    ("ungroup", b"sDRw1624", []),
    ("parse", b"sTBL1599", [("parse_line", b"5190"), ("destination", b"5134")]),
    ("clear_colorstops", b"smXL2547", []),
    ("get", b"coregetd", [("as_", b"rtyp")]),
    ("allocate_change", b"smXLCK02", []),
    ("calculate_full", b"smXL1338", []),
    ("user_picture", b"sDRw1085", [("picture_file", b"5012")]),
    ("clear_to_match_style", b"sCRT2434", []),
    ("get_dialog", b"smXLXgDg", []),
    ("exclusive_access", b"smXL1812", []),
    (
        "auto_show",
        b"smXL1970",
        [("type", b"5103"), ("range", b"5312"), ("count", b"1000"), ("field", b"5135")],
    ),
    ("set", b"coresetd", [("to", b"data")]),
    (
        "add_item_to_list",
        b"smXLXAIL",
        [("item_text", b"DITx"), ("entry_index", b"MSix")],
    ),
    (
        "run_XLM_Macro",
        b"smXL1186",
        [
            ("arg1", b"5040"),
            ("arg2", b"5041"),
            ("arg3", b"5042"),
            ("arg4", b"5043"),
            ("arg5", b"5044"),
            ("arg6", b"5045"),
            ("arg7", b"5046"),
            ("arg8", b"5047"),
            ("arg9", b"5048"),
            ("arg10", b"5049"),
            ("arg11", b"5050"),
            ("arg12", b"5051"),
            ("arg13", b"5052"),
            ("arg14", b"5053"),
            ("arg15", b"5054"),
            ("arg16", b"5055"),
            ("arg17", b"5056"),
            ("arg18", b"5057"),
            ("arg19", b"5058"),
            ("arg20", b"5059"),
            ("arg21", b"5060"),
            ("arg22", b"5061"),
            ("arg23", b"5062"),
            ("arg24", b"5063"),
            ("arg25", b"5064"),
            ("arg26", b"5065"),
            ("arg27", b"5066"),
            ("arg28", b"5067"),
            ("arg29", b"5068"),
            ("arg30", b"5069"),
        ],
    ),
    (
        "modify_condition_value",
        b"smXL2563",
        [("type", b"5103"), ("condition_value", b"5389")],
    ),
    (
        "protect_chart",
        b"sCRTXPTc",
        [
            ("password", b"5236"),
            ("drawing_objects", b"5237"),
            ("chart_contents", b"XcCt"),
            ("user_interface_only", b"5240"),
        ],
    ),
    ("clear_outline", b"sTBL1534", []),
    ("modify_sort_key", b"smXL2528", [("rng", b"5385")]),
    ("duplicate", b"coreclon", [("to", b"insh")]),
    (
        "error_bar",
        b"sCRT2299",
        [
            ("direction", b"5165"),
            ("include", b"5337"),
            ("type", b"5103"),
            ("amount", b"5338"),
            ("minus_values", b"5339"),
        ],
    ),
    (
        "create_cube_file",
        b"smXLCJ18",
        [
            ("file", b"6032"),
            ("measures", b"6033"),
            ("levels", b"6034"),
            ("members", b"6035"),
            ("properties", b"6036"),
        ],
    ),
    ("pick_up", b"sDRw2342", []),
    (
        "Excel_comment_text",
        b"smXLXCmT",
        [("text", b"ctxt"), ("start", b"5144"), ("over_write", b"5321")],
    ),
    ("refresh", b"smXL1722", []),
    ("commit_changes", b"smXLCJ57", []),
    (
        "create_new_document",
        b"smXL2188",
        [("file_name", b"5262"), ("edit_now", b"5328"), ("overwrite", b"5321")],
    ),
    ("remove_subtotal", b"sTBL1608", []),
    ("insert_into_range", b"sTBLXiiR", [("shift", b"5164")]),
    (
        "group",
        b"sTBL1571",
        [("start", b"5144"), ("end_", b"5180"), ("by", b"5181"), ("periods", b"5182")],
    ),
    (
        "run_XLM_macro",
        b"sTBL1186",
        [
            ("arg1", b"5040"),
            ("arg2", b"5041"),
            ("arg3", b"5042"),
            ("arg4", b"5043"),
            ("arg5", b"5044"),
            ("arg6", b"5045"),
            ("arg7", b"5046"),
            ("arg8", b"5047"),
            ("arg9", b"5048"),
            ("arg10", b"5049"),
            ("arg11", b"5050"),
            ("arg12", b"5051"),
            ("arg13", b"5052"),
            ("arg14", b"5053"),
            ("arg15", b"5054"),
            ("arg16", b"5055"),
            ("arg17", b"5056"),
            ("arg18", b"5057"),
            ("arg19", b"5058"),
            ("arg20", b"5059"),
            ("arg21", b"5060"),
            ("arg22", b"5061"),
            ("arg23", b"5062"),
            ("arg24", b"5063"),
            ("arg25", b"5064"),
            ("arg26", b"5065"),
            ("arg27", b"5066"),
            ("arg28", b"5067"),
            ("arg29", b"5068"),
            ("arg30", b"5069"),
        ],
    ),
    ("help_", b"smXL1262", [("help_file", b"5099"), ("help_context_id", b"5100")]),
    (
        "apply_names",
        b"sTBL1518",
        [
            ("names", b"1183"),
            ("ignore_relative_absolute", b"5128"),
            ("use_row_column_names", b"5129"),
            ("omit_column", b"5130"),
            ("omit_row", b"5131"),
            ("order", b"5132"),
            ("append_last", b"5133"),
        ],
    ),
    ("merge", b"sTBL1585", [("across", b"5184")]),
    ("unprotect", b"smXL1660", [("password", b"5236")]),
    (
        "union",
        b"smXL1191",
        [
            ("range1", b"XLr1"),
            ("range2", b"XLr2"),
            ("range3", b"XLr3"),
            ("range4", b"XLr4"),
            ("range5", b"XLr5"),
            ("range6", b"XLr6"),
            ("range7", b"XLr7"),
            ("range8", b"XLr8"),
            ("range9", b"XLr9"),
            ("range10", b"Xr10"),
            ("range11", b"Xr11"),
            ("range12", b"Xr12"),
            ("range13", b"Xr13"),
            ("range14", b"Xr14"),
            ("range15", b"Xr15"),
            ("range16", b"Xr16"),
            ("range17", b"Xr17"),
            ("range18", b"Xr18"),
            ("range19", b"Xr19"),
            ("range20", b"Xr20"),
            ("range21", b"Xr21"),
            ("range22", b"Xr22"),
            ("range23", b"Xr23"),
            ("range24", b"Xr24"),
            ("range25", b"Xr25"),
            ("range26", b"Xr26"),
            ("range27", b"Xr27"),
            ("range28", b"Xr28"),
            ("range29", b"Xr29"),
            ("range30", b"Xr30"),
        ],
    ),
    ("copy_text_range", b"sTXTPytr", []),
    ("clear_combobox", b"sMSOZCCB", []),
    ("chart_user_textured", b"sCRTXuTd", [("texture_file", b"5013")]),
    ("fill_up", b"sTBL1558", []),
    ("remove_all_items", b"smXLXrAi", []),
    ("clear", b"sCRT1530", []),
    ("change_connection", b"smXLCJ48", [("connection", b"6040")]),
    ("paste_text_range", b"sTXTPStr", []),
    ("get_rotated_text_bounds", b"sTXTTRRb", []),
    (
        "begin_connect",
        b"sDRw2377",
        [("connected_shape", b"5345"), ("connection_site", b"5346")],
    ),
    (
        "end_connect",
        b"sDRw2379",
        [("connected_shape", b"5345"), ("connection_site", b"5346")],
    ),
    ("add_periods_to", b"sTXTTRaP", []),
    ("create_pivot_fields", b"smXLCL02", []),
    ("web_page_preview", b"smXL1868", []),
    (
        "get_address_local",
        b"sTBL1516",
        [
            ("row_absolute", b"5121"),
            ("column_absolute", b"5122"),
            ("reference_style", b"1297"),
            ("external", b"5123"),
            ("relative_to", b"5087"),
        ],
    ),
    ("custom_drop", b"sDRw1004", [("drop", b"5002")]),
    ("insert_indent", b"sTBL1577", [("insert_amount", b"5183")]),
    (
        "get_pivot_data",
        b"smXLCJ06",
        [
            ("data_field", b"6000"),
            ("field1", b"6001"),
            ("item1", b"6002"),
            ("field2", b"6003"),
            ("item2", b"6004"),
            ("field3", b"6005"),
            ("item3", b"6006"),
            ("field4", b"6007"),
            ("item4", b"6008"),
            ("field5", b"6009"),
            ("item5", b"6010"),
            ("field6", b"6011"),
            ("item6", b"6012"),
            ("field7", b"6013"),
            ("item7", b"6014"),
            ("field8", b"6015"),
            ("item8", b"6016"),
            ("field9", b"6017"),
            ("item9", b"6018"),
            ("field10", b"6019"),
            ("item10", b"6020"),
            ("field11", b"6021"),
            ("item11", b"6022"),
            ("field12", b"6023"),
            ("item12", b"6024"),
            ("field13", b"6025"),
            ("item13", b"6026"),
            ("field14", b"6027"),
            ("item14", b"6028"),
        ],
    ),
    ("change_pivot_cache", b"smXLCJ49", [("pivot_cache", b"6041")]),
    (
        "data_series",
        b"sTBL1544",
        [
            ("rowcol", b"5158"),
            ("data_series_type", b"XdsT"),
            ("date", b"5159"),
            ("increment", b"XBzo"),
            ("stop", b"XBny"),
            ("trend", b"1370"),
        ],
    ),
    ("modify_applies_to_range", b"smXL2572", [("range", b"5387")]),
    ("delete_custom_list", b"smXL1236", [("list_num", b"5088")]),
    ("activate_object", b"smXLxACT", []),
    ("update_from_file", b"smXL1848", []),
    ("move", b"coremove", [("to", b"insh")]),
    ("list_names", b"sTBL1582", []),
    ("clear_arrows", b"smXL1735", []),
    (
        "chart_two_color_gradient",
        b"sCRTXc2G",
        [("gradient_style", b"XgSy"), ("variant", b"5008")],
    ),
    ("data_table", b"sTBLXdtF", [("row_input", b"5214"), ("column_input", b"5215")]),
    ("convert_to_formulas", b"smXLCJ42", [("convert_filters", b"6039")]),
    (
        "sort_special",
        b"sTBL1620",
        [
            ("sort_method", b"5206"),
            ("key1", b"5198"),
            ("order1", b"5199"),
            ("type", b"5103"),
            ("key2", b"5200"),
            ("order2", b"5201"),
            ("key3", b"5202"),
            ("order3", b"5203"),
            ("header", b"5204"),
            ("order_custom", b"5205"),
            ("match_case", b"5172"),
            ("orientation", b"1596"),
            ("dataoption1", b"5376"),
            ("dataoption2", b"5377"),
            ("dataoption3", b"5378"),
        ],
    ),
    ("preset_textured", b"sDRw1082", [("preset_texture", b"5011")]),
    ("chart_location", b"sCRTXcLo", [("where", b"5260"), ("name", b"pnam")]),
    ("check_out", b"smXL2555", [("file_name", b"Cofn")]),
    ("send_to_back", b"smXL1988", []),
    (
        "highlight_changes_options",
        b"smXL1856",
        [("when", b"5298"), ("who", b"5299"), ("where", b"5260")],
    ),
    (
        "save_workbook_as",
        b"smXLxSwA",
        [
            ("filename", b"5016"),
            ("file_format", b"1813"),
            ("password", b"5236"),
            ("write_reservation_password", b"5242"),
            ("read_only_recommended", b"5243"),
            ("create_backup", b"5244"),
            ("access_mode", b"5296"),
            ("conflict_resolution", b"1805"),
            ("add_to_most_recently_used_list", b"5245"),
            ("overwrite", b"5321"),
        ],
    ),
    ("apply_filter", b"smXL2531", []),
    ("get_border", b"smXLXBtr", [("which_border", b"wWbr")]),
    ("row_axis_layout", b"smXLCJ31", [("layout", b"6037")]),
    ("fill_right", b"sTBL1557", []),
    ("paste", b"sCRT1696", []),
    ("clear_formats", b"sCRT1532", []),
    (
        "print_out",
        b"smXL1128",
        [
            ("from_", b"5022"),
            ("to", b"5023"),
            ("copies", b"5024"),
            ("preview", b"5025"),
            ("active_printer", b"5026"),
            ("print_to_file", b"5027"),
            ("collate", b"5028"),
        ],
    ),
    ("send_html_mail", b"smXLSeHM", []),
    ("set_sort_range", b"smXL2516", [("rng", b"5379")]),
    ("cut_range", b"sTBL1543", [("destination_of_cut", b"5134")]),
    (
        "text_to_columns",
        b"sTBL1623",
        [
            ("destination", b"5134"),
            ("data_type", b"5216"),
            ("text_qualifier", b"5217"),
            ("consecutive_delimiter", b"5218"),
            ("tab", b"5219"),
            ("semicolon", b"5220"),
            ("comma", b"5221"),
            ("space", b"5222"),
            ("use_other", b"XuOr"),
            ("other_char", b"5224"),
            ("field_info", b"5225"),
            ("decimal_separator", b"5226"),
            ("thousands_separator", b"5227"),
        ],
    ),
    ("solid", b"sDRw1083", []),
    ("execute", b"sMSOmEXC", []),
    ("scroll_workbook_tabs", b"smXL1133", [("sheets", b"5030"), ("position", b"5031")]),
    ("get_combobox_item", b"sMSOZGCI", [("entry_index", b"MSix")]),
    (
        "large_scroll",
        b"smXL1124",
        [
            ("down", b"5018"),
            ("up", b"5019"),
            ("to_right", b"5020"),
            ("to_left", b"5021"),
        ],
    ),
    ("set_background_picture", b"smXL1667", [("picture_file_name", b"XpFN")]),
    ("find_next", b"sTBL1559", [("after_", b"5167")]),
    ("get_list_item", b"smXLXgLi", [("entry_index", b"MSix")]),
    (
        "make",
        b"corecrel",
        [
            ("new", b"kocl"),
            ("at", b"insh"),
            ("with_data", b"data"),
            ("with_properties", b"prdt"),
        ],
    ),
    (
        "add_sortfield",
        b"smXL2518",
        [
            ("key", b"5380"),
            ("sorton", b"5381"),
            ("order", b"5382"),
            ("customorder", b"5383"),
            ("dataoption", b"5384"),
        ],
    ),
    (
        "save_as",
        b"smXL1659",
        [
            ("filename", b"5016"),
            ("file_format", b"1813"),
            ("password", b"5236"),
            ("write_reservation_password", b"5242"),
            ("read_only_recommended", b"5243"),
            ("create_backup", b"5244"),
            ("add_to_most_recently_used_list", b"5245"),
            ("overwrite", b"5321"),
            ("save_as_local_language", b"locl"),
        ],
    ),
    ("set_subtotals", b"smXL1956", [("subtotal_index", b"XSTT"), ("value", b"XSTv")]),
    ("refresh_data_source_values", b"smXLCJ59", []),
    ("set_first_priority", b"smXL2569", []),
    (
        "run_VB_macro",
        b"sTBL2620",
        [
            ("arg1", b"5040"),
            ("arg2", b"5041"),
            ("arg3", b"5042"),
            ("arg4", b"5043"),
            ("arg5", b"5044"),
            ("arg6", b"5045"),
            ("arg7", b"5046"),
            ("arg8", b"5047"),
            ("arg9", b"5048"),
            ("arg10", b"5049"),
            ("arg11", b"5050"),
            ("arg12", b"5051"),
            ("arg13", b"5052"),
            ("arg14", b"5053"),
            ("arg15", b"5054"),
            ("arg16", b"5055"),
            ("arg17", b"5056"),
            ("arg18", b"5057"),
            ("arg19", b"5058"),
            ("arg20", b"5059"),
            ("arg21", b"5060"),
            ("arg22", b"5061"),
            ("arg23", b"5062"),
            ("arg24", b"5063"),
            ("arg25", b"5064"),
            ("arg26", b"5065"),
            ("arg27", b"5066"),
            ("arg28", b"5067"),
            ("arg29", b"5068"),
            ("arg30", b"5069"),
        ],
    ),
    (
        "protect_sharing",
        b"smXL1827",
        [
            ("file_name", b"5016"),
            ("password", b"5236"),
            ("write_reservation_password", b"5242"),
            ("read_only_recommended", b"5243"),
            ("create_backup", b"5244"),
            ("sharing_password", b"5294"),
            ("file_format", b"5388"),
        ],
    ),
    ("paste_series", b"sCRTXsPt", []),
    (
        "insert_text_text_range",
        b"sTXTTRIt",
        [("insert_where", b"epiP"), ("new_text", b"epNT")],
    ),
    ("clear_all_filters", b"smXLCJ35", []),
    ("fill_down", b"sTBL1555", []),
    ("get_resize", b"sTBL1609", [("row_size", b"5195"), ("column_size", b"5196")]),
    (
        "save_as_picture",
        b"sDRwOSaP",
        [("picture_type", b"5016"), ("file_name", b"5015")],
    ),
    ("end_disconnect", b"sDRw2380", []),
    ("double_click", b"smXL1247", []),
    ("activate_next", b"smXL1102", []),
    (
        "advanced_filter",
        b"sTBL1517",
        [
            ("action", b"5124"),
            ("criteria_range", b"5125"),
            ("copy_to_range", b"5126"),
            ("unique", b"5127"),
        ],
    ),
    ("paste_chart", b"sCRTXprt", [("format", b"5140")]),
    ("copy_worksheet", b"smXLXcpW", [("before_", b"5235"), ("after_", b"5167")]),
    ("get_count_of_combobox_items", b"sMSOZCCi", []),
    (
        "navigate_arrow",
        b"sTBL1589",
        [
            ("toward_precedent", b"5185"),
            ("arrow_number", b"5186"),
            ("link_number", b"5187"),
        ],
    ),
    (
        "run_VB_Macro",
        b"smXL2620",
        [
            ("arg1", b"5040"),
            ("arg2", b"5041"),
            ("arg3", b"5042"),
            ("arg4", b"5043"),
            ("arg5", b"5044"),
            ("arg6", b"5045"),
            ("arg7", b"5046"),
            ("arg8", b"5047"),
            ("arg9", b"5048"),
            ("arg10", b"5049"),
            ("arg11", b"5050"),
            ("arg12", b"5051"),
            ("arg13", b"5052"),
            ("arg14", b"5053"),
            ("arg15", b"5054"),
            ("arg16", b"5055"),
            ("arg17", b"5056"),
            ("arg18", b"5057"),
            ("arg19", b"5058"),
            ("arg20", b"5059"),
            ("arg21", b"5060"),
            ("arg22", b"5061"),
            ("arg23", b"5062"),
            ("arg24", b"5063"),
            ("arg25", b"5064"),
            ("arg26", b"5065"),
            ("arg27", b"5066"),
            ("arg28", b"5067"),
            ("arg29", b"5068"),
            ("arg30", b"5069"),
        ],
    ),
    ("autofill", b"sTBL1522", [("destination", b"5134"), ("type", b"5103")]),
    (
        "one_color_gradient",
        b"sDRw1079",
        [("gradient_style", b"XgSy"), ("variant", b"5008"), ("degree", b"5009")],
    ),
    ("reroute_connections", b"sDRw2343", []),
    (
        "get_open_filename",
        b"smXL1259",
        [("file_filter", b"5091"), ("button_text", b"5094"), ("multi_select", b"5095")],
    ),
    ("show_pages", b"smXL1894", [("page_field", b"5311")]),
    ("clear_range", b"sTBLXrgC", []),
    ("get_values", b"smXL2024", []),
    (
        "follow",
        b"smXL2184",
        [
            ("new_window", b"1125"),
            ("extra_info", b"5304"),
            ("method", b"5305"),
            ("header_info", b"5306"),
        ],
    ),
    ("unmerge", b"sTBL1586", []),
    (
        "convert_formula",
        b"smXL1217",
        [
            ("formula_to_convert", b"5083"),
            ("from_reference_style", b"5084"),
            ("to_reference_style", b"5085"),
            ("to_absolute", b"5086"),
            ("relative_to", b"5087"),
        ],
    ),
    ("send_mail", b"smXLSeML", []),
    ("can_check_in", b"smXL2553", []),
    (
        "preset_gradient",
        b"sDRw1081",
        [
            ("gradient_style", b"XgSy"),
            ("variant", b"5008"),
            ("preset_gradient_type", b"5010"),
        ],
    ),
    ("link_sources", b"smXL1818", [("type", b"5103")]),
    (
        "open_links",
        b"smXL1821",
        [("name", b"pnam"), ("read_only", b"5291"), ("type", b"5103")],
    ),
    ("reset_rotation", b"sDRw1065", []),
    ("clear_contents", b"smXL1531", []),
    ("deselect", b"sCRT1680", []),
    ("count", b"corecnte", [("each", b"kocl")]),
    ("get_registered_functions", b"smXL1298", []),
    ("calculate_full_rebuild", b"smXLLLni", []),
    (
        "add_item_to_combobox",
        b"sMSOZAIB",
        [("combobox_item", b"Z001"), ("entry_index", b"MSix")],
    ),
    ("get_webpage_font", b"smXLwGwF", []),
    ("clear_label_filters", b"smXLCK44", []),
    (
        "consolidate",
        b"sTBL1537",
        [
            ("sources", b"5150"),
            ("consolidation_function", b"XcdF"),
            ("top_row", b"5152"),
            ("left_column", b"5153"),
            ("create_links", b"5154"),
        ],
    ),
    ("auto_outline", b"sTBL1526", []),
    (
        "add_data_validation",
        b"smXL2168",
        [
            ("type", b"5103"),
            ("alert_style", b"5327"),
            ("operator", b"2120"),
            ("formula1", b"2121"),
            ("formula2", b"2122"),
        ],
    ),
    (
        "modify_condition",
        b"smXLXMFc",
        [
            ("type", b"5103"),
            ("operator", b"5137"),
            ("formula1", b"5319"),
            ("formula2", b"5320"),
            ("string", b"5390"),
            ("operator2", b"5391"),
        ],
    ),
    ("z_order", b"sDRw1990", [("z_order_command", b"5344")]),
    ("save", b"coresave", [("in_", b"kfil"), ("as_", b"fltp")]),
    ("save_theme_color_scheme", b"sDRwsTCS", [("file_name", b"5016")]),
    ("list_formulas", b"smXL1922", []),
    ("resize", b"smXL2510", [("range", b"5387")]),
    (
        "apply_data_labels",
        b"sCRT1662",
        [
            ("type", b"5103"),
            ("legend_key", b"5248"),
            ("auto_text", b"5249"),
            ("has_leader_lines", b"5250"),
            ("show_series_name", b"5352"),
            ("show_category_name", b"5353"),
            ("show_value", b"5354"),
            ("show_percentage", b"5355"),
            ("show_bubble_size", b"5356"),
            ("separator", b"5357"),
        ],
    ),
    ("delete_colorstop", b"smXL2549", []),
    ("update_link", b"smXL1849", [("name", b"pnam"), ("type", b"5103")]),
    ("use_default_folder_suffix", b"smXL2408", []),
    ("fill_left", b"sTBL1556", []),
    ("copy_object", b"smXLXcpO", []),
    ("dirty", b"sTBLXCcv", []),
    ("clear_sortfields", b"smXL2519", []),
    (
        "change_file_access",
        b"smXL1801",
        [("mode", b"1500"), ("write_password", b"5287"), ("notify", b"5288")],
    ),
    (
        "replace",
        b"sTBL1393",
        [
            ("what", b"5166"),
            ("replacement", b"5194"),
            ("look_at", b"5169"),
            ("search_order", b"5170"),
            ("match_case", b"5172"),
            ("match_byte", b"5173"),
            ("match_control_characters", b"5174"),
            ("match_diacritics", b"5175"),
        ],
    ),
    (
        "modify",
        b"smXL2118",
        [
            ("type", b"5103"),
            ("alert_style", b"2169"),
            ("operator", b"2120"),
            ("formula1", b"2121"),
            ("formula2", b"2122"),
        ],
    ),
    ("close", b"coreclos", [("saving", b"savo"), ("saving_in", b"kfil")]),
    (
        "arrange_windows",
        b"smXLXaWs",
        [
            ("arrange_style", b"XaSy"),
            ("active_workbook", b"1172"),
            ("sync_horizontal", b"XAsh"),
            ("sync_vertical", b"XAsV"),
        ],
    ),
    ("flip", b"sDRw2338", [("flip_cmd", b"5340")]),
    ("allocate_changes", b"smXLCJ56", []),
    (
        "create_pivot_table",
        b"smXLCJ76",
        [
            ("table_destination", b"6043"),
            ("table_name", b"6044"),
            ("read_data", b"6045"),
            ("default_version", b"6046"),
        ],
    ),
    ("begin_disconnect", b"sDRw2378", []),
    ("discard_changes", b"smXLCJ58", []),
    ("auto_sort", b"smXL1969", [("sort_order", b"XsrO"), ("sort_field", b"XsrF")]),
    (
        "open_xml",
        b"smXLXCdq",
        [("filename", b"BoPf"), ("style_sheets", b"BoGf"), ("load_option", b"BoKf")],
    ),
    ("get_clipboard_formats", b"smXL1213", []),
    ("get_previous_selections", b"smXL1291", []),
    ("inches_to_points", b"smXL1263", [("inches", b"5101")]),
    ("preset_chart_textured", b"sCRTXpCt", [("preset_texture_for_chart", b"5011")]),
    ("item_selected", b"smXLXLis", [("entry_index", b"MSix")]),
    ("Excel_repeat", b"smXL1300", []),
    (
        "scale_width",
        b"sDRwsScW",
        [
            ("factor", b"ep01"),
            ("relative_to_original_size", b"ep02"),
            ("scale", b"ep03"),
        ],
    ),
    ("special_cells", b"sTBL1621", [("type", b"5103"), ("value", b"DPVu")]),
    (
        "border_around",
        b"sTBL1527",
        [
            ("line_style", b"5143"),
            ("weight", b"1031"),
            ("color_index", b"1098"),
            ("color", b"colr"),
        ],
    ),
    ("remove_periods_from", b"sTXTTRrP", []),
    ("get_custom_list_contents", b"smXL1257", [("list_num", b"5088")]),
    ("new_window_on_workbook", b"smXLXnWw", []),
    (
        "change_scenario",
        b"smXL2022",
        [("changing_cells", b"5313"), ("values", b"5314")],
    ),
    ("reset_timer", b"smXLQtRt", []),
    (
        "input_box",
        b"smXL1264",
        [
            ("prompt", b"5102"),
            ("title", b"5093"),
            ("default", b"1233"),
            ("left_position", b"plft"),
            ("top", b"ptop"),
            ("type", b"5103"),
        ],
    ),
    (
        "open_text_file",
        b"smXLXoTx",
        [
            ("filename", b"5016"),
            ("origin", b"XoDw"),
            ("start_row", b"XoSW"),
            ("data_type", b"5216"),
            ("text_qualifier", b"5217"),
            ("consecutive_delimiter", b"5218"),
            ("tab", b"5219"),
            ("semicolon", b"5220"),
            ("comma", b"5221"),
            ("space", b"5222"),
            ("use_other", b"XuOr"),
            ("other_char", b"5224"),
            ("field_info", b"5225"),
            ("decimal_separator", b"5226"),
            ("thousands_separator", b"5227"),
        ],
    ),
    ("clear_value_filters", b"smXLCK43", []),
    (
        "two_color_gradient",
        b"sDRw1084",
        [("gradient_style", b"XgSy"), ("variant", b"5008")],
    ),
    ("toggle_forms_design", b"smXLXPTz", []),
    (
        "chart_user_picture",
        b"sCRTXcup",
        [
            ("picture_file", b"5012"),
            ("picture_format", b"5334"),
            ("picture_stack_unit", b"5335"),
            ("picture_placement", b"5336"),
        ],
    ),
    (
        "apply_custom_chart_type",
        b"sCRTXAcC",
        [("chart_type", b"1708"), ("chart_name", b"5261")],
    ),
    ("show_all", b"smXL2532", []),
    ("set_bullet_picture", b"sDRwBlPc", [("FileName", b"xpBF")]),
    ("remove_an_item_from_combobox", b"sMSOZRCI", [("entry_index", b"MSix")]),
    (
        "get_address",
        b"sTBL1515",
        [
            ("row_absolute", b"5121"),
            ("column_absolute", b"5122"),
            ("reference_style", b"1297"),
            ("external", b"5123"),
            ("relative_to", b"5087"),
        ],
    ),
    (
        "create_summary_for_scenarios",
        b"smXLXcSs",
        [("report_type", b"XRpT"), ("result_cells", b"XRtC")],
    ),
    ("toggle_vertical_text", b"sDRw1052", []),
    ("reset_colors", b"smXL1863", []),
    (
        "protect_worksheet",
        b"smXLXPTs",
        [
            ("password", b"5236"),
            ("drawing_objects", b"5237"),
            ("worksheet_contents", b"XwsC"),
            ("scenarios", b"5239"),
            ("user_interface_only", b"5240"),
            ("allow_formatting_cells", b"5362"),
            ("allow_formatting_columns", b"5363"),
            ("allow_formatting_rows", b"5364"),
            ("allow_inserting_columns", b"5365"),
            ("allow_inserting_rows", b"5366"),
            ("allow_inserting_hyperlinks", b"5367"),
            ("allow_deleting_columns", b"5368"),
            ("allow_deleting_rows", b"5369"),
            ("allow_sorting", b"5370"),
            ("allow_filtering", b"5371"),
            ("allow_using_pivot_table", b"5372"),
        ],
    ),
    (
        "purge_change_history_now",
        b"smXL1860",
        [("days", b"5300"), ("sharing_password", b"5294")],
    ),
    ("row_differences", b"sTBL1610", [("comparison", b"5149")]),
    ("copy_range", b"sTBLXcpR", [("destination", b"5134")]),
    ("get_pivot_table_data", b"smXL1921", [("name", b"pnam")]),
    ("clear_manual_filter", b"smXLCK41", []),
    ("show_levels", b"smXL2049", [("row_levels", b"5316"), ("column_levels", b"5317")]),
    ("show_errors", b"sTBL1616", []),
    ("get_FileMaker_criteria", b"smXL2161", [("criteria_index", b"5323")]),
    (
        "save_as_ODC",
        b"smXLCJ86",
        [("ODC_file_name", b"6047"), ("description", b"6048"), ("keywords", b"6049")],
    ),
    (
        "set_combobox_item",
        b"sMSOZSCI",
        [("entry_index", b"MSix"), ("combobox_item", b"Z001")],
    ),
    ("get_visible_fields", b"smXL1912", []),
    (
        "accept_all_changes",
        b"smXL1861",
        [("when", b"5298"), ("who", b"5299"), ("where", b"5260")],
    ),
    (
        "check_in",
        b"smXL2552",
        [("save_changes", b"Spc1"), ("comments", b"Spc2"), ("make_public", b"Spc3")],
    ),
    ("clear_table", b"smXLCJ27", []),
    ("data_size", b"coredsiz", [("as_", b"rtyp")]),
    ("exists", b"coredoex", []),
    ("function_wizard", b"sTBL1569", []),
    (
        "autofilter_range",
        b"sTBL1523",
        [
            ("field", b"5135"),
            ("criteria1", b"5136"),
            ("operator", b"5137"),
            ("criteria2", b"5138"),
            ("visible_drop_down", b"5139"),
        ],
    ),
    (
        "subtotal",
        b"sTBL1506",
        [
            ("group_by", b"5210"),
            ("function", b"5151"),
            ("total_list", b"5211"),
            ("replace", b"1393"),
            ("page_breaks", b"5212"),
            ("summary_below_data", b"5213"),
        ],
    ),
    ("bring_to_front", b"smXL1984", []),
    ("print", b"aevtpdoc", [("with_properties", b"prdt"), ("print_dialog", b"pdlg")]),
    ("set_list_item", b"smXLXSli", [("entry_index", b"MSix"), ("item_text", b"5038")]),
    ("insert_into", b"sTXT1578", [("string", b"5039")]),
    ("clear_Excel_comments", b"sTBL1636", []),
    (
        "reject_all_changes",
        b"smXL1862",
        [("when", b"5298"), ("who", b"5299"), ("where", b"5260")],
    ),
    ("delete_sortfield", b"smXL2527", []),
    ("copy_picture", b"smXL1539", [("appearance", b"5155"), ("format", b"5140")]),
    ("change_case", b"sTXTTRcc", [("to", b"p#CC")]),
    ("repeat_all_labels", b"smXLCJ60", [("repeat", b"6042")]),
    ("show_precedents", b"sTBL1617", [("remove", b"5197")]),
    ("set_XML_value", b"sTBL1629", [("range_value", b"XrgV")]),
    ("automatic_length", b"sDRw1003", []),
    (
        "sort",
        b"sTBL1619",
        [
            ("key1", b"5198"),
            ("order1", b"5199"),
            ("key2", b"5200"),
            ("sort_type", b"Styp"),
            ("order2", b"5201"),
            ("key3", b"5202"),
            ("order3", b"5203"),
            ("header", b"5204"),
            ("order_custom", b"5205"),
            ("match_case", b"5172"),
            ("orientation", b"1596"),
            ("sort_method", b"5206"),
            ("ignore_control_characters", b"5207"),
            ("ignore_diacritics", b"5208"),
            ("dataoption1", b"5373"),
            ("dataoption2", b"5374"),
            ("dataoption3", b"5375"),
        ],
    ),
    ("clear_range_formats", b"sTBL1532", []),
    ("add_colorstop", b"smXL2546", [("position", b"posn")]),
    ("delete_gradient_stop", b"sDRwdGrd", [("stop_index", b"igSI")]),
    (
        "paste_special",
        b"sTBL1600",
        [
            ("what", b"5166"),
            ("operation", b"5192"),
            ("skip_blanks", b"5193"),
            ("transpose", b"1383"),
        ],
    ),
    ("clear_hyperlinks", b"sTBLXCcw", []),
    ("justify", b"sTBL1580", []),
    ("merge_scenarios", b"smXLXMss", [("merge_source", b"XmSr")]),
    ("load_theme_effect_scheme", b"sDRwlTES", [("file_name", b"5015")]),
    ("convert_to_range", b"smXL2201", []),
    ("get_axis", b"sCRTXGAx", [("axis_type", b"XAty"), ("which_axis", b"5251")]),
    ("calculate_row_major_order", b"sTBLXCcu", []),
    ("apply_sort", b"smXL2517", []),
    (
        "scale_height",
        b"sDRwsScH",
        [
            ("factor", b"ep01"),
            ("relative_to_original_size", b"ep02"),
            ("scale", b"ep03"),
        ],
    ),
    ("circle_invalid", b"smXL1763", []),
    ("set_last_priority", b"smXL2570", []),
    ("save_theme_font_scheme", b"sDRwsTFS", [("file_name", b"5015")]),
    ("delete_replacement", b"sPRF2210", [("what", b"5166")]),
    ("reset", b"sMSOmFBr", []),
    ("cut", b"smXL1543", []),
    ("delete_number_format", b"smXL1810", [("number_format", b"1593")]),
    ("refresh_all", b"smXL1831", []),
    ("show", b"smXL1613", []),
    ("delete", b"coredelo", []),
    ("show_data_form", b"smXL1751", []),
    ("delete_range", b"sTBLXdRg", [("shift", b"5164")]),
    (
        "paste_special_on_worksheet",
        b"smXLXpsW",
        [
            ("format", b"5140"),
            ("link", b"5265"),
            ("display_as_icon", b"5266"),
            ("icon_file_name", b"5267"),
            ("icon_index", b"5268"),
            ("icon_label", b"5269"),
            ("no_HTML_formatting", b"nofm"),
        ],
    ),
    (
        "add_data_field",
        b"smXLCJ09",
        [("field", b"6029"), ("caption", b"6030"), ("function", b"6031")],
    ),
    ("undo", b"smXL1318", []),
    ("show_dependents", b"sTBL1614", [("remove", b"5197")]),
    ("chart_solid", b"sCRTXcSd", []),
    ("next_Excel_comment", b"smXLXnxC", []),
    ("can_check_out", b"smXL2556", [("file_name", b"Cofn")]),
    ("get_has_axis", b"sCRT1685", [("axis_type", b"XAty"), ("axis_group", b"5251")]),
    ("add_page_item", b"smXLCK24", [("item", b"6053"), ("clear_list", b"6054")]),
    ("apply_theme", b"smXLxlTh", [("file_name", b"5015")]),
    (
        "add_fields_to_pivot_table",
        b"smXL1890",
        [
            ("row_fields", b"5307"),
            ("column_fields", b"5308"),
            ("page_fields", b"5309"),
            ("add_to_table", b"5310"),
        ],
    ),
    (
        "open_workbook",
        b"smXL1169",
        [
            ("workbook_file_name", b"WbFN"),
            ("update_links", b"XOul"),
            ("read_only", b"5291"),
            ("format", b"5140"),
            ("password", b"5236"),
            ("write_reserved_password", b"XoRP"),
            ("ignore_read_only_recommended", b"XoiR"),
            ("origin", b"XoDw"),
            ("delimiter", b"XoDL"),
            ("editable", b"XoEd"),
            ("notify", b"5288"),
            ("converter", b"XoCV"),
            ("add_to_mru", b"5245"),
        ],
    ),
    ("reset_all_page_breaks", b"smXL1744", []),
    ("goal_seek", b"sTBL1570", [("goal", b"5178"), ("changing_cell", b"5179")]),
    ("add_comment", b"sTBL1634", [("comment_text", b"XCmT")]),
    (
        "on_key",
        b"smXL1283",
        [
            ("key", b"5110"),
            ("command_key_pressed", b"XcKP"),
            ("shift_key_pressed", b"XsKP"),
            ("option_key_pressed", b"XoKP"),
            ("control_key_pressed", b"XrKP"),
            ("procedure", b"5111"),
        ],
    ),
    ("cut_text_range", b"sTXTpCtr", []),
    ("set_source_data", b"sCRT1720", [("source", b"5252"), ("plot_by", b"1714")]),
    (
        "preset_chart_gradient",
        b"sCRTXpCg",
        [
            ("gradient_style", b"XgSy"),
            ("variant", b"5008"),
            ("preset_gradient_type", b"5010"),
        ],
    ),
    ("goto", b"smXL1261", [("reference", b"5097"), ("scroll", b"5098")]),
    (
        "autoformat",
        b"sTBL1525",
        [
            ("format", b"5140"),
            ("include_number", b"5141"),
            ("font", b"5142"),
            ("alignment", b"1053"),
            ("border", b"1011"),
            ("pattern", b"1028"),
            ("width", b"pwid"),
        ],
    ),
    ("get_custom_list_num", b"smXL1258", [("list_array", b"5077")]),
    ("evaluate", b"smXL2435", [("name", b"pnam")]),
    (
        "insert_gradient_stop",
        b"sDRwiGrd",
        [
            ("custom_color", b"igCC"),
            ("position", b"posn"),
            ("transparency", b"igTR"),
            ("stop_index", b"igSI"),
        ],
    ),
    ("refresh_table", b"smXL1905", []),
    ("patterned", b"sDRw1080", [("pattern", b"1028")]),
    ("get_offset", b"sTBL1595", [("row_offset", b"5188"), ("column_offset", b"5189")]),
    ("get_subtotals", b"smXL1955", [("subtotal_index", b"XSTT")]),
    ("register_xll", b"smXL1299", [("filename", b"5016")]),
    ("column_differences", b"sTBL1535", [("comparison", b"5149")]),
    ("get_XML_value", b"sTBL1628", []),
    ("remove_duplicates", b"sTBLXCcr", []),
    ("previous_Excel_comment", b"smXLXpvC", []),
    (
        "follow_hyperlink",
        b"smXL1865",
        [
            ("address", b"5301"),
            ("sub_address", b"5302"),
            ("new_window", b"1125"),
            ("extra_info", b"5304"),
            ("method", b"5305"),
            ("header_info", b"5306"),
        ],
    ),
    (
        "open_data_base",
        b"smXLOpdb",
        [
            ("filename", b"BoDf"),
            ("command_text", b"BoDe"),
            ("rcommand_type", b"BoDg"),
            ("back_ground_query", b"BoDh"),
            ("import_data_as", b"BoDi"),
        ],
    ),
    ("custom_length", b"sDRw1005", [("length", b"5003")]),
    (
        "check_spelling",
        b"smXL1212",
        [
            ("custom_dictionary", b"5081"),
            ("ignore_uppercase", b"5082"),
            ("always_suggest", b"5145"),
        ],
    ),
    (
        "find",
        b"sTBL1395",
        [
            ("what", b"5166"),
            ("after_", b"5167"),
            ("look_in", b"5168"),
            ("look_at", b"5169"),
            ("search_order", b"5170"),
            ("search_direction", b"5171"),
            ("match_case", b"5172"),
            ("match_byte", b"5173"),
        ],
    ),
    ("get_chart_element", b"sCRT1719", [("x", b"5230"), ("y", b"5231")]),
    ("delete_object", b"sCRTXdel", []),
    (
        "check_in_with_version",
        b"smXL2554",
        [
            ("save_changes", b"Spc1"),
            ("comments", b"Spc2"),
            ("make_public", b"Spc3"),
            ("version_type", b"Spc4"),
        ],
    ),
    ("autocomplete", b"sTBL1521", [("string", b"5039")]),
    ("calculate", b"smXL1175", []),
    ("get_custom_color", b"sDRwtGCC", [("name", b"pnam")]),
    (
        "add_member_property_field",
        b"smXLCK97",
        [
            ("property", b"6062"),
            ("property_order", b"6063"),
            ("property_displayed_in", b"6064"),
        ],
    ),
    ("unprotect_sharing", b"smXL1847", [("sharing_password", b"5294")]),
    ("open", b"aevtodoc", []),
    ("pivot_select", b"smXL1933", [("name", b"pnam"), ("mode", b"1500")]),
    (
        "small_scroll",
        b"smXL1135",
        [
            ("down", b"5018"),
            ("up", b"5019"),
            ("to_right", b"5020"),
            ("to_left", b"5021"),
        ],
    ),
    ("show_all_data", b"smXL1750", []),
    (
        "add_replacement",
        b"sPRF2208",
        [("text_to_replace", b"Xt2R"), ("replacement_text", b"XRtx")],
    ),
    ("set_shapes_default_properties", b"sDRw2346", []),
    ("centimeters_to_points", b"smXL1211", [("centimeters", b"5079")]),
    ("refresh_query_table", b"smXLXrQT", [("background_query", b"1878")]),
    (
        "create_names",
        b"sTBL1540",
        [
            ("top", b"ptop"),
            ("left_position", b"plft"),
            ("bottom", b"5156"),
            ("right", b"5157"),
        ],
    ),
    (
        "set_FileMaker_criteria",
        b"smXL2160",
        [
            ("criteria_index", b"5323"),
            ("field_name", b"5324"),
            ("operator", b"2120"),
            ("clause_text", b"5325"),
            ("condition", b"5326"),
        ],
    ),
    ("break_link", b"smXL1871", [("name", b"pnam"), ("type", b"5103")]),
    ("save_workspace", b"smXL1302", [("workspace_file_name", b"WsFN")]),
    (
        "get_save_as_filename",
        b"smXL1260",
        [
            ("initial_filename", b"5096"),
            ("file_filter", b"5091"),
            ("filter_index", b"5092"),
            ("button_text", b"5094"),
        ],
    ),
    ("open_FileMaker_file", b"smXLXoFM", [("filename", b"5016")]),
    ("paste_worksheet", b"smXL1696", [("destination", b"5134"), ("link", b"5265")]),
    ("set_chart_element", b"sCRT2432", [("chart_element", b"5360")]),
    ("apply_outline_styles", b"sTBL1519", []),
    (
        "set_has_axis",
        b"sCRT1686",
        [("axis_exists", b"XAXt"), ("axis_type", b"XAty"), ("axis_group", b"5251")],
    ),
    ("make_connection", b"smXLCJ80", []),
    (
        "copy_chart_as_picture",
        b"sCRTXCcp",
        [("appearance", b"5155"), ("format", b"5140"), ("output_size", b"XopZ")],
    ),
    ("add_custom_list", b"smXL1200", [("list_array", b"5077"), ("by_row", b"5078")]),
    ("drill_to", b"smXLCK26", [("field", b"6055")]),
    ("get_replacement_list", b"sPRF2211", []),
    (
        "chart_wizard",
        b"sCRT1674",
        [
            ("source", b"5252"),
            ("gallery", b"5118"),
            ("format", b"5140"),
            ("plot_by", b"5253"),
            ("category_labels", b"5254"),
            ("series_labels", b"5255"),
            ("has_legend", b"5256"),
            ("title", b"5093"),
            ("category_title", b"5257"),
            ("value_title", b"5258"),
            ("extra_title", b"5259"),
        ],
    ),
    ("show_custom_view", b"smXLXsCv", []),
]
