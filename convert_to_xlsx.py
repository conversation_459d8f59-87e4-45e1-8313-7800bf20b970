#!/usr/bin/env python3
"""Convert corrupted .xlsm to working .xlsx"""

import shutil
import os

def convert_to_xlsx():
    print("🔧 Converting .xlsm to .xlsx")
    print("=" * 30)
    
    xlsm_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    xlsx_file = "ABZ_CompanyName_IR_Project_Workbook_Template_RECOVERED.xlsx"
    
    if not os.path.exists(xlsm_file):
        print(f"❌ Source file not found: {xlsm_file}")
        return
    
    try:
        # Simply copy and rename - the XML structure is fine
        shutil.copy2(xlsm_file, xlsx_file)
        print(f"✅ Created: {xlsx_file}")
        print(f"📊 Size: {os.path.getsize(xlsx_file):,} bytes")
        
        print(f"\n💡 Try opening: {xlsx_file}")
        print(f"   This should work since the XML is valid")
        print(f"   You'll need to re-add your macros later")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    convert_to_xlsx()
