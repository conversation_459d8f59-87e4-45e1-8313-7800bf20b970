Tactic,Technique_ID
Inhibit Response Function (ICS),T0800-Activate Firmware Update Mode - Activate Firmware Update Mode
TA0001-Initial Access (Enterprise),T1078-Valid Accounts - Valid Accounts
TA0001-Initial Access (Enterprise),T1078.001-Default Accounts - Default Accounts
TA0001-Initial Access (Enterprise),T1078.002-Domain Accounts - Domain Accounts
TA0001-Initial Access (Enterprise),T1078.003-Local Accounts - Local Accounts
TA0001-Initial Access (Enterprise),T1078.004-Cloud Accounts - Cloud Accounts
TA0001-Initial Access (Enterprise),T1091-Replication Through Removable Media - Replication Through Removable Media
TA0001-Initial Access (Enterprise),T1133-External Remote Services - External Remote Services
TA0001-Initial Access (Enterprise),T1189-Drive-by Compromise - Drive-by Compromise
TA0001-Initial Access (Enterprise),T1189-Drive-by Compromise - Drive-by Compromise
TA0001-Initial Access (Enterprise),T1190-Exploit Public-Facing Application - Exploit Public-Facing Application
TA0001-Initial Access (Enterprise),T1195-Supply Chain Compromise - Supply Chain Compromise
TA0001-Initial Access (Enterprise),T1195.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools
TA0001-Initial Access (Enterprise),T1195.002-Compromise Software Supply Chain - Compromise Software Supply Chain
TA0001-Initial Access (Enterprise),T1195.003-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain
TA0001-Initial Access (Enterprise),T1199-Trusted Relationships - Trusted Relationships
TA0001-Initial Access (Enterprise),T1200-Hardware Additions - Hardware Additions
TA0001-Initial Access (Enterprise),T1566-Phishing - Phishing
TA0001-Initial Access (Enterprise),T1566.001-Spearphishing Attachment - Spearphishing Attachment
TA0001-Initial Access (Enterprise),T1566.002-Spearphishing Link - Spearphishing Link
TA0001-Initial Access (Enterprise),T1566.003-Spearphishing via Service - Spearphishing via Service
TA0001-Initial Access (Enterprise),T1566.004-Spearphishing Voice - Spearphishing Voice
TA0001-Initial Access (Enterprise),T1659-Content Injection - Content Injection
TA0001-Initial Access (Enterprise),T1659-Content Injection - Content Injection
TA0001-Initial Access (Enterprise),T1669-Wi-Fi Networks - Wi-Fi Networks
TA0002-Execution (Enterprise),T1047-Windows Management Instrumentation - Windows Management Instrumentation
TA0002-Execution (Enterprise),T1053-Scheduled Task/Job - Scheduled Task/Job
TA0002-Execution (Enterprise),T1053.002-At - At
TA0002-Execution (Enterprise),T1053.003-Cron - Cron
TA0002-Execution (Enterprise),T1053.005-Scheduled Task - Scheduled Task
TA0002-Execution (Enterprise),T1053.006-Systemd Timers - Systemd Timers
TA0002-Execution (Enterprise),T1053.007-Container Orchestration Job - Container Orchestration Job
TA0002-Execution (Enterprise),T1059-Command and Scripting Interpreter - Command and Scripting Interpreter
TA0002-Execution (Enterprise),T1059.001-PowerShell - PowerShell
TA0002-Execution (Enterprise),T1059.002-AppleScript - AppleScript
TA0002-Execution (Enterprise),T1059.003-Windows Command Shell - Windows Command Shell
TA0002-Execution (Enterprise),T1059.004-Unix Shell - Unix Shell
TA0002-Execution (Enterprise),T1059.005-Visual Basic - Visual Basic
TA0002-Execution (Enterprise),T1059.006-Python - Python
TA0002-Execution (Enterprise),T1059.007-JavaScript - JavaScript
TA0002-Execution (Enterprise),T1059.008-Network Device CLI - Network Device CLI
TA0002-Execution (Enterprise),T1059.009-Cloud API - Cloud API
TA0002-Execution (Enterprise),T1059.010-AutoHotKey & AutoIT - AutoHotKey & AutoIT
TA0002-Execution (Enterprise),T1059.011-Lua - Lua
TA0002-Execution (Enterprise),T1059.012-Hypervisor CLI - Hypervisor CLI
TA0002-Execution (Enterprise),T1072-Software Deployment Tools - Software Deployment Tools
TA0002-Execution (Enterprise),T1106-Native API - Native API
TA0002-Execution (Enterprise),T1129-Shared Modules - Shared Modules
TA0002-Execution (Enterprise),T1203-Exploitation for Client Execution - Exploitation for Client Execution
TA0002-Execution (Enterprise),T1204-User Execution - User Execution
TA0002-Execution (Enterprise),T1204.001-Malicious Link - Malicious Link
TA0002-Execution (Enterprise),T1204.002-Malicious File - Malicious File
TA0002-Execution (Enterprise),T1204.003-Malicious Image - Malicious Image
TA0002-Execution (Enterprise),T1204.004-Malicious Copy and Paste - Malicious Copy and Paste
TA0002-Execution (Enterprise),T1559-Inter-Process Communication - Inter-Process Communication
TA0002-Execution (Enterprise),T1559.001-Component Object Model - Component Object Model
TA0002-Execution (Enterprise),T1559.002-Dynamic Data Exchange - Dynamic Data Exchange
TA0002-Execution (Enterprise),T1559.003-XPC Services - XPC Services
TA0002-Execution (Enterprise),T1569-System Services - System Services
TA0002-Execution (Enterprise),T1569.001-Launchctl - Launchctl
TA0002-Execution (Enterprise),T1569.002-Service Execution - Service Execution
TA0002-Execution (Enterprise),T1569.003-Systemctl - Systemctl
TA0002-Execution (Enterprise),T1609-Container Administration Command - Container Administration Command
TA0002-Execution (Enterprise),T1610-Deploy Container - Deploy Container
TA0002-Execution (Enterprise),T1648-Serverless Execution - Serverless Execution
TA0002-Execution (Enterprise),T1651-Cloud Administration Command - Cloud Administration Command
TA0002-Execution (Enterprise),T1674-Input Injection - Input Injection
TA0002-Execution (Enterprise),T1675-ESXi Administration Command - ESXi Administration Command
TA0003-Persistence (Enterprise),T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts
TA0003-Persistence (Enterprise),T1037.001-Logon Script (Windows) - Logon Script (Windows)
TA0003-Persistence (Enterprise),T1037.002-Login Hook - Login Hook
TA0003-Persistence (Enterprise),T1037.003-Network Logon Script - Network Logon Script
TA0003-Persistence (Enterprise),T1037.004-RC Scripts - RC Scripts
TA0003-Persistence (Enterprise),T1037.005-Startup Items - Startup Items
TA0003-Persistence (Enterprise),T1053-Scheduled Task/Job - Scheduled Task/Job
TA0003-Persistence (Enterprise),T1053.002-At - At
TA0003-Persistence (Enterprise),T1053.003-Cron - Cron
TA0003-Persistence (Enterprise),T1053.005-Scheduled Task - Scheduled Task
TA0003-Persistence (Enterprise),T1053.006-Systemd Timers - Systemd Timers
TA0003-Persistence (Enterprise),T1053.007-Container Orchestration Job - Container Orchestration Job
TA0003-Persistence (Enterprise),T1078-Valid Accounts - Valid Accounts
TA0003-Persistence (Enterprise),T1078.001-Default Accounts - Default Accounts
TA0003-Persistence (Enterprise),T1078.002-Domain Accounts - Domain Accounts
TA0003-Persistence (Enterprise),T1078.003-Local Accounts - Local Accounts
TA0003-Persistence (Enterprise),T1078.004-Cloud Accounts - Cloud Accounts
TA0003-Persistence (Enterprise),T1098-Account Manipulation - Account Manipulation
TA0003-Persistence (Enterprise),T1098.001-Additional Cloud Credentials - Additional Cloud Credentials
TA0003-Persistence (Enterprise),T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions
TA0003-Persistence (Enterprise),T1098.003-Additional Cloud Roles - Additional Cloud Roles
TA0003-Persistence (Enterprise),T1098.004-SSH Authorized Keys - SSH Authorized Keys
TA0003-Persistence (Enterprise),T1098.005-Device Registration - Device Registration
TA0003-Persistence (Enterprise),T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles
TA0003-Persistence (Enterprise),T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups
TA0003-Persistence (Enterprise),T1112-Modify Registry - Modify Registry
TA0003-Persistence (Enterprise),T1133-External Remote Services - External Remote Services
TA0003-Persistence (Enterprise),T1136-Create Account - Create Account
TA0003-Persistence (Enterprise),T1136.001-Local Account - Local Account
TA0003-Persistence (Enterprise),T1136.002-Domain Account - Domain Account
TA0003-Persistence (Enterprise),T1136.003-Cloud Account - Cloud Account
TA0003-Persistence (Enterprise),T1137-Office Application Startup - Office Application Startup
TA0003-Persistence (Enterprise),T1137.001-Office Template Macros - Office Template Macros
TA0003-Persistence (Enterprise),T1137.002-Office Test - Office Test
TA0003-Persistence (Enterprise),T1137.003-Outlook Forms - Outlook Forms
TA0003-Persistence (Enterprise),T1137.004-Outlook Home Page - Outlook Home Page
TA0003-Persistence (Enterprise),T1137.005-Outlook Rules - Outlook Rules
TA0003-Persistence (Enterprise),T1137.006-Add-ins - Add-ins
TA0003-Persistence (Enterprise),T1176-Software Extensions - Software Extensions
TA0003-Persistence (Enterprise),T1176.001-Browser Extensions - Browser Extensions
TA0003-Persistence (Enterprise),T1176.002-IDE Extensions - IDE Extensions
TA0003-Persistence (Enterprise),T1197-BITS Jobs - BITS Jobs
TA0003-Persistence (Enterprise),T1205-Traffic Signaling - Traffic Signaling
TA0003-Persistence (Enterprise),T1205.001-Port Knocking - Port Knocking
TA0003-Persistence (Enterprise),T1205.002-Socket Filters - Socket Filters
TA0003-Persistence (Enterprise),T1505-Server Software Component - Server Software Component
TA0003-Persistence (Enterprise),T1505.001-SQL Stored Procedures - SQL Stored Procedures
TA0003-Persistence (Enterprise),T1505.002-Transport Agent - Transport Agent
TA0003-Persistence (Enterprise),T1505.003-Web Shell - Web Shell
TA0003-Persistence (Enterprise),T1505.004-IIS Components - IIS Components
TA0003-Persistence (Enterprise),T1505.005-Terminal Services DLL - Terminal Services DLL
TA0003-Persistence (Enterprise),T1505.006-vSphere Installation Bundles - vSphere Installation Bundles
TA0003-Persistence (Enterprise),T1525-Implant Internal Image - Implant Internal Image
TA0003-Persistence (Enterprise),T1542-Pre-OS Boot - Pre-OS Boot
TA0003-Persistence (Enterprise),T1542.001-System Firmware - System Firmware
TA0003-Persistence (Enterprise),T1542.002-Component Firmware - Component Firmware
TA0003-Persistence (Enterprise),T1542.003-Bootkit - Bootkit
TA0003-Persistence (Enterprise),T1542.004-ROMMONkit - ROMMONkit
TA0003-Persistence (Enterprise),T1542.005-TFTP Boot - TFTP Boot
TA0003-Persistence (Enterprise),T1543-Create or Modify System Process - Create or Modify System Process
TA0003-Persistence (Enterprise),T1543.001-Launch Agent - Launch Agent
TA0003-Persistence (Enterprise),T1543.002-Systemd Service - Systemd Service
TA0003-Persistence (Enterprise),T1543.003-Windows Service - Windows Service
TA0003-Persistence (Enterprise),T1543.004-Launch Daemon - Launch Daemon
TA0003-Persistence (Enterprise),T1543.005-Container Service - Container Service
TA0003-Persistence (Enterprise),T1546-Event Triggered Execution - Event Triggered Execution
TA0003-Persistence (Enterprise),T1546.001-Change Default File Association - Change Default File Association
TA0003-Persistence (Enterprise),T1546.002-Screensaver - Screensaver
TA0003-Persistence (Enterprise),T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription
TA0003-Persistence (Enterprise),T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification
TA0003-Persistence (Enterprise),T1546.005-Trap - Trap
TA0003-Persistence (Enterprise),T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition
TA0003-Persistence (Enterprise),T1546.007-Netsh Helper DLL - Netsh Helper DLL
TA0003-Persistence (Enterprise),T1546.008-Accessibility Features - Accessibility Features
TA0003-Persistence (Enterprise),T1546.009-AppCert DLLs - AppCert DLLs
TA0003-Persistence (Enterprise),T1546.010-AppInit DLLs - AppInit DLLs
TA0003-Persistence (Enterprise),T1546.011-Application Shimming - Application Shimming
TA0003-Persistence (Enterprise),T1546.012-Image File Execution Options Injection - Image File Execution Options Injection
TA0003-Persistence (Enterprise),T1546.013-PowerShell Profile - PowerShell Profile
TA0003-Persistence (Enterprise),T1546.014-Emond - Emond
TA0003-Persistence (Enterprise),T1546.015-Component Object Model Hijacking - Component Object Model Hijacking
TA0003-Persistence (Enterprise),T1546.016-Installer Packages - Installer Packages
TA0003-Persistence (Enterprise),T1546.017-Udev Rules - Udev Rules
TA0003-Persistence (Enterprise),T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution
TA0003-Persistence (Enterprise),T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder
TA0003-Persistence (Enterprise),T1547.002-Authentication Package - Authentication Package
TA0003-Persistence (Enterprise),T1547.003-Time Providers - Time Providers
TA0003-Persistence (Enterprise),T1547.004-Winlogon Helper DLL - Winlogon Helper DLL
TA0003-Persistence (Enterprise),T1547.005-Security Support Provider - Security Support Provider
TA0003-Persistence (Enterprise),T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions
TA0003-Persistence (Enterprise),T1547.007-Re-opened Applications - Re-opened Applications
TA0003-Persistence (Enterprise),T1547.008-LSASS Driver - LSASS Driver
TA0003-Persistence (Enterprise),T1547.009-Shortcut Modification - Shortcut Modification
TA0003-Persistence (Enterprise),T1547.010-Port Monitors - Port Monitors
TA0003-Persistence (Enterprise),T1547.012-Print Processors - Print Processors
TA0003-Persistence (Enterprise),T1547.013-XDG Autostart Entries - XDG Autostart Entries
TA0003-Persistence (Enterprise),T1547.014-Active Setup - Active Setup
TA0003-Persistence (Enterprise),T1547.015-Login Items - Login Items
TA0003-Persistence (Enterprise),T1554-Compromise Host Software Binary - Compromise Host Software Binary
TA0003-Persistence (Enterprise),T1556-Modify Authentication Process - Modify Authentication Process
TA0003-Persistence (Enterprise),T1556.001-Domain Controller Authentication - Domain Controller Authentication
TA0003-Persistence (Enterprise),T1556.002-Password Filter DLL - Password Filter DLL
TA0003-Persistence (Enterprise),T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules
TA0003-Persistence (Enterprise),T1556.004-Network Device Authentication - Network Device Authentication
TA0003-Persistence (Enterprise),T1556.005-Reversible Encryption - Reversible Encryption
TA0003-Persistence (Enterprise),T1556.006-Multi-Factor Authentication - Multi-Factor Authentication
TA0003-Persistence (Enterprise),T1556.007-Hybrid Identity - Hybrid Identity
TA0003-Persistence (Enterprise),T1556.008-Network Provider DLL - Network Provider DLL
TA0003-Persistence (Enterprise),T1556.009-Conditional Access Policies - Conditional Access Policies
TA0003-Persistence (Enterprise),T1574-Hijack Execution Flow - Hijack Execution Flow
TA0003-Persistence (Enterprise),T1574.001-DLL - DLL
TA0003-Persistence (Enterprise),T1574.004-Dylib Hijacking - Dylib Hijacking
TA0003-Persistence (Enterprise),T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness
TA0003-Persistence (Enterprise),T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking
TA0003-Persistence (Enterprise),T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable
TA0003-Persistence (Enterprise),T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking
TA0003-Persistence (Enterprise),T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path
TA0003-Persistence (Enterprise),T1574.010-Services File Permissions Weakness - Services File Permissions Weakness
TA0003-Persistence (Enterprise),T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness
TA0003-Persistence (Enterprise),T1574.012-COR_PROFILER - COR_PROFILER
TA0003-Persistence (Enterprise),T1574.013-KernelCallbackTable - KernelCallbackTable
TA0003-Persistence (Enterprise),T1574.014-AppDomainManager - AppDomainManager
TA0003-Persistence (Enterprise),T1653-Power Settings - Power Settings
TA0003-Persistence (Enterprise),T1668-Exclusive Control - Exclusive Control
TA0003-Persistence (Enterprise),T1671-Cloud Application Integration - Cloud Application Integration
TA0004-Privilege Escalation (Enterprise),T1037-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts
TA0004-Privilege Escalation (Enterprise),T1037.001-Logon Script (Windows) - Logon Script (Windows)
TA0004-Privilege Escalation (Enterprise),T1037.002-Login Hook - Login Hook
TA0004-Privilege Escalation (Enterprise),T1037.003-Network Logon Script - Network Logon Script
TA0004-Privilege Escalation (Enterprise),T1037.004-RC Scripts - RC Scripts
TA0004-Privilege Escalation (Enterprise),T1037.005-Startup Items - Startup Items
TA0004-Privilege Escalation (Enterprise),T1053-Scheduled Task/Job - Scheduled Task/Job
TA0004-Privilege Escalation (Enterprise),T1053.002-At - At
TA0004-Privilege Escalation (Enterprise),T1053.003-Cron - Cron
TA0004-Privilege Escalation (Enterprise),T1053.005-Scheduled Task - Scheduled Task
TA0004-Privilege Escalation (Enterprise),T1053.006-Systemd Timers - Systemd Timers
TA0004-Privilege Escalation (Enterprise),T1053.007-Container Orchestration Job - Container Orchestration Job
TA0004-Privilege Escalation (Enterprise),T1055-Process Injection - Process Injection
TA0004-Privilege Escalation (Enterprise),T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection
TA0004-Privilege Escalation (Enterprise),T1055.002-Portable Executable Injection - Portable Executable Injection
TA0004-Privilege Escalation (Enterprise),T1055.003-Thread Execution Hijacking - Thread Execution Hijacking
TA0004-Privilege Escalation (Enterprise),T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call
TA0004-Privilege Escalation (Enterprise),T1055.005-Thread Local Storage - Thread Local Storage
TA0004-Privilege Escalation (Enterprise),T1055.008-Ptrace System Calls - Ptrace System Calls
TA0004-Privilege Escalation (Enterprise),T1055.009-Proc Memory - Proc Memory
TA0004-Privilege Escalation (Enterprise),T1055.011-Extra Window Memory Injection - Extra Window Memory Injection
TA0004-Privilege Escalation (Enterprise),T1055.012-Process Hollowing - Process Hollowing
TA0004-Privilege Escalation (Enterprise),T1055.013-Process Doppelgänging - Process Doppelgänging
TA0004-Privilege Escalation (Enterprise),T1055.014-VDSO Hijacking - VDSO Hijacking
TA0004-Privilege Escalation (Enterprise),T1055.015-ListPlanting - ListPlanting
TA0004-Privilege Escalation (Enterprise),T1068-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation
TA0004-Privilege Escalation (Enterprise),T1078-Valid Accounts - Valid Accounts
TA0004-Privilege Escalation (Enterprise),T1078.001-Default Accounts - Default Accounts
TA0004-Privilege Escalation (Enterprise),T1078.002-Domain Accounts - Domain Accounts
TA0004-Privilege Escalation (Enterprise),T1078.003-Local Accounts - Local Accounts
TA0004-Privilege Escalation (Enterprise),T1078.004-Cloud Accounts - Cloud Accounts
TA0004-Privilege Escalation (Enterprise),T1098-Account Manipulation - Account Manipulation
TA0004-Privilege Escalation (Enterprise),T1098.001-Additional Cloud Credentials - Additional Cloud Credentials
TA0004-Privilege Escalation (Enterprise),T1098.002-Additional Email Delegate Permissions - Additional Email Delegate Permissions
TA0004-Privilege Escalation (Enterprise),T1098.003-Additional Cloud Roles - Additional Cloud Roles
TA0004-Privilege Escalation (Enterprise),T1098.004-SSH Authorized Keys - SSH Authorized Keys
TA0004-Privilege Escalation (Enterprise),T1098.005-Device Registration - Device Registration
TA0004-Privilege Escalation (Enterprise),T1098.006-Additional Container Cluster Roles - Additional Container Cluster Roles
TA0004-Privilege Escalation (Enterprise),T1098.007-Additional Local or Domain Groups - Additional Local or Domain Groups
TA0004-Privilege Escalation (Enterprise),T1134-Access Token Manipulation - Access Token Manipulation
TA0004-Privilege Escalation (Enterprise),T1134.001-Token Impersonation/Theft - Token Impersonation/Theft
TA0004-Privilege Escalation (Enterprise),T1134.002-Create Process with Token - Create Process with Token
TA0004-Privilege Escalation (Enterprise),T1134.003-Make and Impersonate Token - Make and Impersonate Token
TA0004-Privilege Escalation (Enterprise),T1134.004-Parent PID Spoofing - Parent PID Spoofing
TA0004-Privilege Escalation (Enterprise),T1134.005-SID-History Injection - SID-History Injection
TA0004-Privilege Escalation (Enterprise),T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification
TA0004-Privilege Escalation (Enterprise),T1484.001-Group Policy Modification - Group Policy Modification
TA0004-Privilege Escalation (Enterprise),T1484.002-Trust Modification - Trust Modification
TA0004-Privilege Escalation (Enterprise),T1543-Create or Modify System Process - Create or Modify System Process
TA0004-Privilege Escalation (Enterprise),T1543.001-Launch Agent - Launch Agent
TA0004-Privilege Escalation (Enterprise),T1543.002-Systemd Service - Systemd Service
TA0004-Privilege Escalation (Enterprise),T1543.003-Windows Service - Windows Service
TA0004-Privilege Escalation (Enterprise),T1543.004-Launch Daemon - Launch Daemon
TA0004-Privilege Escalation (Enterprise),T1543.005-Container Service - Container Service
TA0004-Privilege Escalation (Enterprise),T1546-Event Triggered Execution - Event Triggered Execution
TA0004-Privilege Escalation (Enterprise),T1546.001-Change Default File Association - Change Default File Association
TA0004-Privilege Escalation (Enterprise),T1546.002-Screensaver - Screensaver
TA0004-Privilege Escalation (Enterprise),T1546.003-Windows Management Instrumentation Event Subscription - Windows Management Instrumentation Event Subscription
TA0004-Privilege Escalation (Enterprise),T1546.004-Unix Shell Configuration Modification - Unix Shell Configuration Modification
TA0004-Privilege Escalation (Enterprise),T1546.005-Trap - Trap
TA0004-Privilege Escalation (Enterprise),T1546.006-LC_LOAD_DYLIB Addition - LC_LOAD_DYLIB Addition
TA0004-Privilege Escalation (Enterprise),T1546.007-Netsh Helper DLL - Netsh Helper DLL
TA0004-Privilege Escalation (Enterprise),T1546.008-Accessibility Features - Accessibility Features
TA0004-Privilege Escalation (Enterprise),T1546.009-AppCert DLLs - AppCert DLLs
TA0004-Privilege Escalation (Enterprise),T1546.010-AppInit DLLs - AppInit DLLs
TA0004-Privilege Escalation (Enterprise),T1546.011-Application Shimming - Application Shimming
TA0004-Privilege Escalation (Enterprise),T1546.012-Image File Execution Options Injection - Image File Execution Options Injection
TA0004-Privilege Escalation (Enterprise),T1546.013-PowerShell Profile - PowerShell Profile
TA0004-Privilege Escalation (Enterprise),T1546.014-Emond - Emond
TA0004-Privilege Escalation (Enterprise),T1546.015-Component Object Model Hijacking - Component Object Model Hijacking
TA0004-Privilege Escalation (Enterprise),T1546.016-Installer Packages - Installer Packages
TA0004-Privilege Escalation (Enterprise),T1546.017-Udev Rules - Udev Rules
TA0004-Privilege Escalation (Enterprise),T1547-Boot or Logon Autostart Execution - Boot or Logon Autostart Execution
TA0004-Privilege Escalation (Enterprise),T1547.001-Registry Run Keys / Startup Folder - Registry Run Keys / Startup Folder
TA0004-Privilege Escalation (Enterprise),T1547.002-Authentication Package - Authentication Package
TA0004-Privilege Escalation (Enterprise),T1547.003-Time Providers - Time Providers
TA0004-Privilege Escalation (Enterprise),T1547.004-Winlogon Helper DLL - Winlogon Helper DLL
TA0004-Privilege Escalation (Enterprise),T1547.005-Security Support Provider - Security Support Provider
TA0004-Privilege Escalation (Enterprise),T1547.006-Kernel Modules and Extensions - Kernel Modules and Extensions
TA0004-Privilege Escalation (Enterprise),T1547.007-Re-opened Applications - Re-opened Applications
TA0004-Privilege Escalation (Enterprise),T1547.008-LSASS Driver - LSASS Driver
TA0004-Privilege Escalation (Enterprise),T1547.009-Shortcut Modification - Shortcut Modification
TA0004-Privilege Escalation (Enterprise),T1547.010-Port Monitors - Port Monitors
TA0004-Privilege Escalation (Enterprise),T1547.012-Print Processors - Print Processors
TA0004-Privilege Escalation (Enterprise),T1547.013-XDG Autostart Entries - XDG Autostart Entries
TA0004-Privilege Escalation (Enterprise),T1547.014-Active Setup - Active Setup
TA0004-Privilege Escalation (Enterprise),T1547.015-Login Items - Login Items
TA0004-Privilege Escalation (Enterprise),T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism
TA0004-Privilege Escalation (Enterprise),T1548.001-Setuid and Setgid - Setuid and Setgid
TA0004-Privilege Escalation (Enterprise),T1548.002-Bypass User Account Control - Bypass User Account Control
TA0004-Privilege Escalation (Enterprise),T1548.003-Sudo and Sudo Caching - Sudo and Sudo Caching
TA0004-Privilege Escalation (Enterprise),T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt
TA0004-Privilege Escalation (Enterprise),T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access
TA0004-Privilege Escalation (Enterprise),T1548.006-TCC Manipulation - TCC Manipulation
TA0004-Privilege Escalation (Enterprise),T1574-Hijack Execution Flow - Hijack Execution Flow
TA0004-Privilege Escalation (Enterprise),T1574.001-DLL - DLL
TA0004-Privilege Escalation (Enterprise),T1574.004-Dylib Hijacking - Dylib Hijacking
TA0004-Privilege Escalation (Enterprise),T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness
TA0004-Privilege Escalation (Enterprise),T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking
TA0004-Privilege Escalation (Enterprise),T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable
TA0004-Privilege Escalation (Enterprise),T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking
TA0004-Privilege Escalation (Enterprise),T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path
TA0004-Privilege Escalation (Enterprise),T1574.010-Services File Permissions Weakness - Services File Permissions Weakness
TA0004-Privilege Escalation (Enterprise),T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness
TA0004-Privilege Escalation (Enterprise),T1574.012-COR_PROFILER - COR_PROFILER
TA0004-Privilege Escalation (Enterprise),T1574.013-KernelCallbackTable - KernelCallbackTable
TA0004-Privilege Escalation (Enterprise),T1574.014-AppDomainManager - AppDomainManager
TA0004-Privilege Escalation (Enterprise),T1611-Escape to Host - Escape to Host
TA0005-Defense Evasion (Enterprise),T1006-Direct Volume Access - Direct Volume Access
TA0005-Defense Evasion (Enterprise),T1014-Rootkit - Rootkit
TA0005-Defense Evasion (Enterprise),T1027-Obfuscated Files or Information - Obfuscated Files or Information
TA0005-Defense Evasion (Enterprise),T1027.001-Binary Padding - Binary Padding
TA0005-Defense Evasion (Enterprise),T1027.002-Software Packing - Software Packing
TA0005-Defense Evasion (Enterprise),T1027.003-Steganography - Steganography
TA0005-Defense Evasion (Enterprise),T1027.004-Compile After Delivery - Compile After Delivery
TA0005-Defense Evasion (Enterprise),T1027.005-Indicator Removal from Tools - Indicator Removal from Tools
TA0005-Defense Evasion (Enterprise),T1027.006-HTML Smuggling - HTML Smuggling
TA0005-Defense Evasion (Enterprise),T1027.007-Dynamic API Resolution - Dynamic API Resolution
TA0005-Defense Evasion (Enterprise),T1027.008-Stripped Payloads - Stripped Payloads
TA0005-Defense Evasion (Enterprise),T1027.009-Embedded Payloads - Embedded Payloads
TA0005-Defense Evasion (Enterprise),T1027.010-Command Obfuscation - Command Obfuscation
TA0005-Defense Evasion (Enterprise),T1027.011-Fileless Storage - Fileless Storage
TA0005-Defense Evasion (Enterprise),T1027.012-LNK Icon Smuggling - LNK Icon Smuggling
TA0005-Defense Evasion (Enterprise),T1027.013-Encrypted/Encoded File - Encrypted/Encoded File
TA0005-Defense Evasion (Enterprise),T1027.014-Polymorphic Code - Polymorphic Code
TA0005-Defense Evasion (Enterprise),T1027.015-Compression - Compression
TA0005-Defense Evasion (Enterprise),T1027.016-Junk Code Insertion - Junk Code Insertion
TA0005-Defense Evasion (Enterprise),T1027.017-SVG Smuggling - SVG Smuggling
TA0005-Defense Evasion (Enterprise),T1036-Masquerading - Masquerading
TA0005-Defense Evasion (Enterprise),T1036.001-Invalid Code Signature - Invalid Code Signature
TA0005-Defense Evasion (Enterprise),T1036.002-Right-to-Left Override - Right-to-Left Override
TA0005-Defense Evasion (Enterprise),T1036.003-Rename Legitimate Utilities - Rename Legitimate Utilities
TA0005-Defense Evasion (Enterprise),T1036.004-Masquerade Task or Service - Masquerade Task or Service
TA0005-Defense Evasion (Enterprise),T1036.005-Match Legitimate Resource Name or Location - Match Legitimate Resource Name or Location
TA0005-Defense Evasion (Enterprise),T1036.006-Space after Filename - Space after Filename
TA0005-Defense Evasion (Enterprise),T1036.007-Double File Extension - Double File Extension
TA0005-Defense Evasion (Enterprise),T1036.008-Masquerade File Type - Masquerade File Type
TA0005-Defense Evasion (Enterprise),T1036.009-Break Process Trees - Break Process Trees
TA0005-Defense Evasion (Enterprise),T1036.010-Masquerade Account Name - Masquerade Account Name
TA0005-Defense Evasion (Enterprise),T1036.011-Overwrite Process Arguments - Overwrite Process Arguments
TA0005-Defense Evasion (Enterprise),T1055-Process Injection - Process Injection
TA0005-Defense Evasion (Enterprise),T1055.001-Dynamic-link Library Injection - Dynamic-link Library Injection
TA0005-Defense Evasion (Enterprise),T1055.002-Portable Executable Injection - Portable Executable Injection
TA0005-Defense Evasion (Enterprise),T1055.003-Thread Execution Hijacking - Thread Execution Hijacking
TA0005-Defense Evasion (Enterprise),T1055.004-Asynchronous Procedure Call - Asynchronous Procedure Call
TA0005-Defense Evasion (Enterprise),T1055.005-Thread Local Storage - Thread Local Storage
TA0005-Defense Evasion (Enterprise),T1055.008-Ptrace System Calls - Ptrace System Calls
TA0005-Defense Evasion (Enterprise),T1055.009-Proc Memory - Proc Memory
TA0005-Defense Evasion (Enterprise),T1055.011-Extra Window Memory Injection - Extra Window Memory Injection
TA0005-Defense Evasion (Enterprise),T1055.012-Process Hollowing - Process Hollowing
TA0005-Defense Evasion (Enterprise),T1055.013-Process Doppelgänging - Process Doppelgänging
TA0005-Defense Evasion (Enterprise),T1055.014-VDSO Hijacking - VDSO Hijacking
TA0005-Defense Evasion (Enterprise),T1055.015-ListPlanting - ListPlanting
TA0005-Defense Evasion (Enterprise),T1070-Indicator Removal - Indicator Removal
TA0005-Defense Evasion (Enterprise),T1070.001-Clear Windows Event Logs - Clear Windows Event Logs
TA0005-Defense Evasion (Enterprise),T1070.002-Clear Linux or Mac System Logs - Clear Linux or Mac System Logs
TA0005-Defense Evasion (Enterprise),T1070.003-Clear Command History - Clear Command History
TA0005-Defense Evasion (Enterprise),T1070.004-File Deletion - File Deletion
TA0005-Defense Evasion (Enterprise),T1070.005-Network Share Connection Removal - Network Share Connection Removal
TA0005-Defense Evasion (Enterprise),T1070.006-Timestomp - Timestomp
TA0005-Defense Evasion (Enterprise),T1070.007-Clear Network Connection History and Configurations - Clear Network Connection History and Configurations
TA0005-Defense Evasion (Enterprise),T1070.008-Clear Mailbox Data - Clear Mailbox Data
TA0005-Defense Evasion (Enterprise),T1070.009-Clear Persistence - Clear Persistence
TA0005-Defense Evasion (Enterprise),T1070.010-Relocate Malware - Relocate Malware
TA0005-Defense Evasion (Enterprise),T1078-Valid Accounts - Valid Accounts
TA0005-Defense Evasion (Enterprise),T1078.001-Default Accounts - Default Accounts
TA0005-Defense Evasion (Enterprise),T1078.002-Domain Accounts - Domain Accounts
TA0005-Defense Evasion (Enterprise),T1078.003-Local Accounts - Local Accounts
TA0005-Defense Evasion (Enterprise),T1078.004-Cloud Accounts - Cloud Accounts
TA0005-Defense Evasion (Enterprise),T1112-Modify Registry - Modify Registry
TA0005-Defense Evasion (Enterprise),T1127-Trusted Developer Utilities Proxy Execution - Trusted Developer Utilities Proxy Execution
TA0005-Defense Evasion (Enterprise),T1127.001-MSBuild - MSBuild
TA0005-Defense Evasion (Enterprise),T1127.002-ClickOnce - ClickOnce
TA0005-Defense Evasion (Enterprise),T1127.003-JamPlus - JamPlus
TA0005-Defense Evasion (Enterprise),T1134-Access Token Manipulation - Access Token Manipulation
TA0005-Defense Evasion (Enterprise),T1134.001-Token Impersonation/Theft - Token Impersonation/Theft
TA0005-Defense Evasion (Enterprise),T1134.002-Create Process with Token - Create Process with Token
TA0005-Defense Evasion (Enterprise),T1134.003-Make and Impersonate Token - Make and Impersonate Token
TA0005-Defense Evasion (Enterprise),T1134.004-Parent PID Spoofing - Parent PID Spoofing
TA0005-Defense Evasion (Enterprise),T1134.005-SID-History Injection - SID-History Injection
TA0005-Defense Evasion (Enterprise),T1140-Deobfuscate/Decode Files or Information - Deobfuscate/Decode Files or Information
TA0005-Defense Evasion (Enterprise),T1197-BITS Jobs - BITS Jobs
TA0005-Defense Evasion (Enterprise),T1202-Indirect Command Execution - Indirect Command Execution
TA0005-Defense Evasion (Enterprise),T1205-Traffic Signaling - Traffic Signaling
TA0005-Defense Evasion (Enterprise),T1205.001-Port Knocking - Port Knocking
TA0005-Defense Evasion (Enterprise),T1205.002-Socket Filters - Socket Filters
TA0005-Defense Evasion (Enterprise),T1207-Rogue Domain Controller - Rogue Domain Controller
TA0005-Defense Evasion (Enterprise),T1211-Exploitation for Defense Evasion - Exploitation for Defense Evasion
TA0005-Defense Evasion (Enterprise),T1216-System Script Proxy Execution - System Script Proxy Execution
TA0005-Defense Evasion (Enterprise),T1216.001-PubPrn - PubPrn
TA0005-Defense Evasion (Enterprise),T1216.002-SyncAppvPublishingServer - SyncAppvPublishingServer
TA0005-Defense Evasion (Enterprise),T1218-System Binary Proxy Execution - System Binary Proxy Execution
TA0005-Defense Evasion (Enterprise),T1218.001-Compiled HTML File - Compiled HTML File
TA0005-Defense Evasion (Enterprise),T1218.002-Control Panel - Control Panel
TA0005-Defense Evasion (Enterprise),T1218.003-CMSTP - CMSTP
TA0005-Defense Evasion (Enterprise),T1218.004-InstallUtil - InstallUtil
TA0005-Defense Evasion (Enterprise),T1218.005-Mshta - Mshta
TA0005-Defense Evasion (Enterprise),T1218.007-Msiexec - Msiexec
TA0005-Defense Evasion (Enterprise),T1218.008-Odbcconf - Odbcconf
TA0005-Defense Evasion (Enterprise),T1218.009-Regsvcs/Regasm - Regsvcs/Regasm
TA0005-Defense Evasion (Enterprise),T1218.010-Regsvr32 - Regsvr32
TA0005-Defense Evasion (Enterprise),T1218.011-Rundll32 - Rundll32
TA0005-Defense Evasion (Enterprise),T1218.012-Verclsid - Verclsid
TA0005-Defense Evasion (Enterprise),T1218.013-Mavinject - Mavinject
TA0005-Defense Evasion (Enterprise),T1218.014-MMC - MMC
TA0005-Defense Evasion (Enterprise),T1218.015-Electron Applications - Electron Applications
TA0005-Defense Evasion (Enterprise),T1220-XSL Script Processing - XSL Script Processing
TA0005-Defense Evasion (Enterprise),T1221-Template Injection - Template Injection
TA0005-Defense Evasion (Enterprise),T1222-File and Directory Permissions Modification - File and Directory Permissions Modification
TA0005-Defense Evasion (Enterprise),T1222.001-Windows File and Directory Permissions Modification - Windows File and Directory Permissions Modification
TA0005-Defense Evasion (Enterprise),T1222.002-Linux and Mac File and Directory Permissions Modification - Linux and Mac File and Directory Permissions Modification
TA0005-Defense Evasion (Enterprise),T1480-Execution Guardrails - Execution Guardrails
TA0005-Defense Evasion (Enterprise),T1480.001-Environmental Keying - Environmental Keying
TA0005-Defense Evasion (Enterprise),T1480.002-Mutual Exclusion - Mutual Exclusion
TA0005-Defense Evasion (Enterprise),T1484-Domain or Tenant Policy Modification - Domain or Tenant Policy Modification
TA0005-Defense Evasion (Enterprise),T1484.001-Group Policy Modification - Group Policy Modification
TA0005-Defense Evasion (Enterprise),T1484.002-Trust Modification - Trust Modification
TA0005-Defense Evasion (Enterprise),T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion
TA0005-Defense Evasion (Enterprise),T1497.001-System Checks - System Checks
TA0005-Defense Evasion (Enterprise),T1497.002-User Activity Based Checks - User Activity Based Checks
TA0005-Defense Evasion (Enterprise),T1497.003-Time Based Evasion - Time Based Evasion
TA0005-Defense Evasion (Enterprise),T1535-Unused/Unsupported Cloud Regions - Unused/Unsupported Cloud Regions
TA0005-Defense Evasion (Enterprise),T1542-Pre-OS Boot - Pre-OS Boot
TA0005-Defense Evasion (Enterprise),T1542.001-System Firmware - System Firmware
TA0005-Defense Evasion (Enterprise),T1542.002-Component Firmware - Component Firmware
TA0005-Defense Evasion (Enterprise),T1542.003-Bootkit - Bootkit
TA0005-Defense Evasion (Enterprise),T1542.004-ROMMONkit - ROMMONkit
TA0005-Defense Evasion (Enterprise),T1542.005-TFTP Boot - TFTP Boot
TA0005-Defense Evasion (Enterprise),T1548-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism
TA0005-Defense Evasion (Enterprise),T1548.001-Setuid and Setgid - Setuid and Setgid
TA0005-Defense Evasion (Enterprise),T1548.002-Bypass User Account Control - Bypass User Account Control
TA0005-Defense Evasion (Enterprise),T1548.003-Sudo and Sudo Caching - Sudo and Sudo Caching
TA0005-Defense Evasion (Enterprise),T1548.004-Elevated Execution with Prompt - Elevated Execution with Prompt
TA0005-Defense Evasion (Enterprise),T1548.005-Temporary Elevated Cloud Access - Temporary Elevated Cloud Access
TA0005-Defense Evasion (Enterprise),T1548.006-TCC Manipulation - TCC Manipulation
TA0005-Defense Evasion (Enterprise),T1550-Use Alternate Authentication Material - Use Alternate Authentication Material
TA0005-Defense Evasion (Enterprise),T1550.001-Application Access Token - Application Access Token
TA0005-Defense Evasion (Enterprise),T1550.002-Pass the Hash - Pass the Hash
TA0005-Defense Evasion (Enterprise),T1550.003-Pass the Ticket - Pass the Ticket
TA0005-Defense Evasion (Enterprise),T1550.004-Web Session Cookie - Web Session Cookie
TA0005-Defense Evasion (Enterprise),T1553-Subvert Trust Controls - Subvert Trust Controls
TA0005-Defense Evasion (Enterprise),T1553.001-Gatekeeper Bypass - Gatekeeper Bypass
TA0005-Defense Evasion (Enterprise),T1553.002-Code Signing - Code Signing
TA0005-Defense Evasion (Enterprise),T1553.003-SIP and Trust Provider Hijacking - SIP and Trust Provider Hijacking
TA0005-Defense Evasion (Enterprise),T1553.004-Install Root Certificate - Install Root Certificate
TA0005-Defense Evasion (Enterprise),T1553.005-Mark-of-the-Web Bypass - Mark-of-the-Web Bypass
TA0005-Defense Evasion (Enterprise),T1553.006-Code Signing Policy Modification - Code Signing Policy Modification
TA0005-Defense Evasion (Enterprise),T1556-Modify Authentication Process - Modify Authentication Process
TA0005-Defense Evasion (Enterprise),T1556.001-Domain Controller Authentication - Domain Controller Authentication
TA0005-Defense Evasion (Enterprise),T1556.002-Password Filter DLL - Password Filter DLL
TA0005-Defense Evasion (Enterprise),T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules
TA0005-Defense Evasion (Enterprise),T1556.004-Network Device Authentication - Network Device Authentication
TA0005-Defense Evasion (Enterprise),T1556.005-Reversible Encryption - Reversible Encryption
TA0005-Defense Evasion (Enterprise),T1556.006-Multi-Factor Authentication - Multi-Factor Authentication
TA0005-Defense Evasion (Enterprise),T1556.007-Hybrid Identity - Hybrid Identity
TA0005-Defense Evasion (Enterprise),T1556.008-Network Provider DLL - Network Provider DLL
TA0005-Defense Evasion (Enterprise),T1556.009-Conditional Access Policies - Conditional Access Policies
TA0005-Defense Evasion (Enterprise),T1562-Impair Defenses - Impair Defenses
TA0005-Defense Evasion (Enterprise),T1562.001-Disable or Modify Tools - Disable or Modify Tools
TA0005-Defense Evasion (Enterprise),T1562.002-Disable Windows Event Logging - Disable Windows Event Logging
TA0005-Defense Evasion (Enterprise),T1562.003-Impair Command History Logging - Impair Command History Logging
TA0005-Defense Evasion (Enterprise),T1562.004-Disable or Modify System Firewall - Disable or Modify System Firewall
TA0005-Defense Evasion (Enterprise),T1562.006-Indicator Blocking - Indicator Blocking
TA0005-Defense Evasion (Enterprise),T1562.007-Disable or Modify Cloud Firewall - Disable or Modify Cloud Firewall
TA0005-Defense Evasion (Enterprise),T1562.008-Disable or Modify Cloud Logs - Disable or Modify Cloud Logs
TA0005-Defense Evasion (Enterprise),T1562.009-Safe Mode Boot - Safe Mode Boot
TA0005-Defense Evasion (Enterprise),T1562.010-Downgrade Attack - Downgrade Attack
TA0005-Defense Evasion (Enterprise),T1562.011-Spoof Security Alerting - Spoof Security Alerting
TA0005-Defense Evasion (Enterprise),T1562.012-Disable or Modify Linux Audit System - Disable or Modify Linux Audit System
TA0005-Defense Evasion (Enterprise),T1564-Hide Artifacts - Hide Artifacts
TA0005-Defense Evasion (Enterprise),T1564.001-Hidden Files and Directories - Hidden Files and Directories
TA0005-Defense Evasion (Enterprise),T1564.002-Hidden Users - Hidden Users
TA0005-Defense Evasion (Enterprise),T1564.003-Hidden Window - Hidden Window
TA0005-Defense Evasion (Enterprise),T1564.004-NTFS File Attributes - NTFS File Attributes
TA0005-Defense Evasion (Enterprise),T1564.005-Hidden File System - Hidden File System
TA0005-Defense Evasion (Enterprise),T1564.006-Run Virtual Instance - Run Virtual Instance
TA0005-Defense Evasion (Enterprise),T1564.007-VBA Stomping - VBA Stomping
TA0005-Defense Evasion (Enterprise),T1564.008-Email Hiding Rules - Email Hiding Rules
TA0005-Defense Evasion (Enterprise),T1564.009-Resource Forking - Resource Forking
TA0005-Defense Evasion (Enterprise),T1564.010-Process Argument Spoofing - Process Argument Spoofing
TA0005-Defense Evasion (Enterprise),T1564.011-Ignore Process Interrupts - Ignore Process Interrupts
TA0005-Defense Evasion (Enterprise),T1564.012-File/Path Exclusions - File/Path Exclusions
TA0005-Defense Evasion (Enterprise),T1564.013-Bind Mounts - Bind Mounts
TA0005-Defense Evasion (Enterprise),T1564.014-Extended Attributes - Extended Attributes
TA0005-Defense Evasion (Enterprise),T1574-Hijack Execution Flow - Hijack Execution Flow
TA0005-Defense Evasion (Enterprise),T1574.001-DLL - DLL
TA0005-Defense Evasion (Enterprise),T1574.004-Dylib Hijacking - Dylib Hijacking
TA0005-Defense Evasion (Enterprise),T1574.005-Executable Installer File Permissions Weakness - Executable Installer File Permissions Weakness
TA0005-Defense Evasion (Enterprise),T1574.006-Dynamic Linker Hijacking - Dynamic Linker Hijacking
TA0005-Defense Evasion (Enterprise),T1574.007-Path Interception by PATH Environment Variable - Path Interception by PATH Environment Variable
TA0005-Defense Evasion (Enterprise),T1574.008-Path Interception by Search Order Hijacking - Path Interception by Search Order Hijacking
TA0005-Defense Evasion (Enterprise),T1574.009-Path Interception by Unquoted Path - Path Interception by Unquoted Path
TA0005-Defense Evasion (Enterprise),T1574.010-Services File Permissions Weakness - Services File Permissions Weakness
TA0005-Defense Evasion (Enterprise),T1574.011-Services Registry Permissions Weakness - Services Registry Permissions Weakness
TA0005-Defense Evasion (Enterprise),T1574.012-COR_PROFILER - COR_PROFILER
TA0005-Defense Evasion (Enterprise),T1574.013-KernelCallbackTable - KernelCallbackTable
TA0005-Defense Evasion (Enterprise),T1574.014-AppDomainManager - AppDomainManager
TA0005-Defense Evasion (Enterprise),T1578-Modify Cloud Compute Infrastructure - Modify Cloud Compute Infrastructure
TA0005-Defense Evasion (Enterprise),T1578.001-Create Snapshot - Create Snapshot
TA0005-Defense Evasion (Enterprise),T1578.002-Create Cloud Instance - Create Cloud Instance
TA0005-Defense Evasion (Enterprise),T1578.003-Delete Cloud Instance - Delete Cloud Instance
TA0005-Defense Evasion (Enterprise),T1578.004-Revert Cloud Instance - Revert Cloud Instance
TA0005-Defense Evasion (Enterprise),T1578.005-Modify Cloud Compute Configurations - Modify Cloud Compute Configurations
TA0005-Defense Evasion (Enterprise),T1599-Network Boundary Bridging - Network Boundary Bridging
TA0005-Defense Evasion (Enterprise),T1599.001-Network Address Translation Traversal - Network Address Translation Traversal
TA0005-Defense Evasion (Enterprise),T1600-Weaken Encryption - Weaken Encryption
TA0005-Defense Evasion (Enterprise),T1600.001-Reduce Key Space - Reduce Key Space
TA0005-Defense Evasion (Enterprise),T1600.002-Disable Crypto Hardware - Disable Crypto Hardware
TA0005-Defense Evasion (Enterprise),T1601-Modify System Image - Modify System Image
TA0005-Defense Evasion (Enterprise),T1601.001-Patch System Image - Patch System Image
TA0005-Defense Evasion (Enterprise),T1601.002-Downgrade System Image - Downgrade System Image
TA0005-Defense Evasion (Enterprise),T1610-Deploy Container - Deploy Container
TA0005-Defense Evasion (Enterprise),T1612-Build Image on Host - Build Image on Host
TA0005-Defense Evasion (Enterprise),T1620-Reflective Code Loading - Reflective Code Loading
TA0005-Defense Evasion (Enterprise),T1622-Debugger Evasion - Debugger Evasion
TA0005-Defense Evasion (Enterprise),T1647-Plist File Modification - Plist File Modification
TA0005-Defense Evasion (Enterprise),T1656-Impersonation - Impersonation
TA0005-Defense Evasion (Enterprise),T1666-Modify Cloud Resource Hierarchy - Modify Cloud Resource Hierarchy
TA0005-Defense Evasion (Enterprise),T1672-Email Spoofing - Email Spoofing
TA0006-Credential Access (Enterprise),T1003-OS Credential Dumping - OS Credential Dumping
TA0006-Credential Access (Enterprise),T1003.001-LSASS Memory - LSASS Memory
TA0006-Credential Access (Enterprise),T1003.002-Security Account Manager - Security Account Manager
TA0006-Credential Access (Enterprise),T1003.003-NTDS - NTDS
TA0006-Credential Access (Enterprise),T1003.004-LSA Secrets - LSA Secrets
TA0006-Credential Access (Enterprise),T1003.005-Cached Domain Credentials - Cached Domain Credentials
TA0006-Credential Access (Enterprise),T1003.006-DCSync - DCSync
TA0006-Credential Access (Enterprise),T1003.007-Proc Filesystem - Proc Filesystem
TA0006-Credential Access (Enterprise),T1003.008-/etc/passwd and /etc/shadow - /etc/passwd and /etc/shadow
TA0006-Credential Access (Enterprise),T1040-Network Sniffing - Network Sniffing
TA0006-Credential Access (Enterprise),T1056-Input Capture - Input Capture
TA0006-Credential Access (Enterprise),T1056.001-Keylogging - Keylogging
TA0006-Credential Access (Enterprise),T1056.002-GUI Input Capture - GUI Input Capture
TA0006-Credential Access (Enterprise),T1056.003-Web Portal Capture - Web Portal Capture
TA0006-Credential Access (Enterprise),T1056.004-Credential API Hooking - Credential API Hooking
TA0006-Credential Access (Enterprise),T1110-Brute Force - Brute Force
TA0006-Credential Access (Enterprise),T1110.001-Password Guessing - Password Guessing
TA0006-Credential Access (Enterprise),T1110.002-Password Cracking - Password Cracking
TA0006-Credential Access (Enterprise),T1110.003-Password Spraying - Password Spraying
TA0006-Credential Access (Enterprise),T1110.004-Credential Stuffing - Credential Stuffing
TA0006-Credential Access (Enterprise),T1111-Multi-Factor Authentication Interception - Multi-Factor Authentication Interception
TA0006-Credential Access (Enterprise),T1187-Forced Authentication - Forced Authentication
TA0006-Credential Access (Enterprise),T1212-Exploitation for Credential Access - Exploitation for Credential Access
TA0006-Credential Access (Enterprise),T1528-Steal Application Access Token - Steal Application Access Token
TA0006-Credential Access (Enterprise),T1539-Steal Web Session Cookie - Steal Web Session Cookie
TA0006-Credential Access (Enterprise),T1552-Unsecured Credentials - Unsecured Credentials
TA0006-Credential Access (Enterprise),T1552.001-Credentials In Files - Credentials In Files
TA0006-Credential Access (Enterprise),T1552.002-Credentials in Registry - Credentials in Registry
TA0006-Credential Access (Enterprise),T1552.003-Bash History - Bash History
TA0006-Credential Access (Enterprise),T1552.004-Private Keys - Private Keys
TA0006-Credential Access (Enterprise),T1552.005-Cloud Instance Metadata API - Cloud Instance Metadata API
TA0006-Credential Access (Enterprise),T1552.006-Group Policy Preferences - Group Policy Preferences
TA0006-Credential Access (Enterprise),T1552.007-Container API - Container API
TA0006-Credential Access (Enterprise),T1552.008-Chat Messages - Chat Messages
TA0006-Credential Access (Enterprise),T1555-Credentials from Password Stores - Credentials from Password Stores
TA0006-Credential Access (Enterprise),T1555.001-Keychain - Keychain
TA0006-Credential Access (Enterprise),T1555.002-Securityd Memory - Securityd Memory
TA0006-Credential Access (Enterprise),T1555.003-Credentials from Web Browsers - Credentials from Web Browsers
TA0006-Credential Access (Enterprise),T1555.004-Windows Credential Manager - Windows Credential Manager
TA0006-Credential Access (Enterprise),T1555.005-Password Managers - Password Managers
TA0006-Credential Access (Enterprise),T1555.006-Cloud Secrets Management Stores - Cloud Secrets Management Stores
TA0006-Credential Access (Enterprise),T1556-Modify Authentication Process - Modify Authentication Process
TA0006-Credential Access (Enterprise),T1556.001-Domain Controller Authentication - Domain Controller Authentication
TA0006-Credential Access (Enterprise),T1556.002-Password Filter DLL - Password Filter DLL
TA0006-Credential Access (Enterprise),T1556.003-Pluggable Authentication Modules - Pluggable Authentication Modules
TA0006-Credential Access (Enterprise),T1556.004-Network Device Authentication - Network Device Authentication
TA0006-Credential Access (Enterprise),T1556.005-Reversible Encryption - Reversible Encryption
TA0006-Credential Access (Enterprise),T1556.006-Multi-Factor Authentication - Multi-Factor Authentication
TA0006-Credential Access (Enterprise),T1556.007-Hybrid Identity - Hybrid Identity
TA0006-Credential Access (Enterprise),T1556.008-Network Provider DLL - Network Provider DLL
TA0006-Credential Access (Enterprise),T1556.009-Conditional Access Policies - Conditional Access Policies
TA0006-Credential Access (Enterprise),T1557-Adversary-in-the-Middle - Adversary-in-the-Middle
TA0006-Credential Access (Enterprise),T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay
TA0006-Credential Access (Enterprise),T1557.002-ARP Cache Poisoning - ARP Cache Poisoning
TA0006-Credential Access (Enterprise),T1557.003-DHCP Spoofing - DHCP Spoofing
TA0006-Credential Access (Enterprise),T1557.004-Evil Twin - Evil Twin
TA0006-Credential Access (Enterprise),T1558-Steal or Forge Kerberos Tickets - Steal or Forge Kerberos Tickets
TA0006-Credential Access (Enterprise),T1558.001-Golden Ticket - Golden Ticket
TA0006-Credential Access (Enterprise),T1558.002-Silver Ticket - Silver Ticket
TA0006-Credential Access (Enterprise),T1558.003-Kerberoasting - Kerberoasting
TA0006-Credential Access (Enterprise),T1558.004-AS-REP Roasting - AS-REP Roasting
TA0006-Credential Access (Enterprise),T1558.005-Ccache Files - Ccache Files
TA0006-Credential Access (Enterprise),T1606-Forge Web Credentials - Forge Web Credentials
TA0006-Credential Access (Enterprise),T1606.001-Web Cookies - Web Cookies
TA0006-Credential Access (Enterprise),T1606.002-SAML Tokens - SAML Tokens
TA0006-Credential Access (Enterprise),T1621-Multi-Factor Authentication Request Generation - Multi-Factor Authentication Request Generation
TA0006-Credential Access (Enterprise),T1649-Steal or Forge Authentication Certificates - Steal or Forge Authentication Certificates
TA0007-Discovery (Enterprise),T1007-System Service Discovery - System Service Discovery
TA0007-Discovery (Enterprise),T1010-Application Window Discovery - Application Window Discovery
TA0007-Discovery (Enterprise),T1012-Query Registry - Query Registry
TA0007-Discovery (Enterprise),T1016-System Network Configuration Discovery - System Network Configuration Discovery
TA0007-Discovery (Enterprise),T1016.001-Internet Connection Discovery - Internet Connection Discovery
TA0007-Discovery (Enterprise),T1016.002-Wi-Fi Discovery - Wi-Fi Discovery
TA0007-Discovery (Enterprise),T1018-Remote System Discovery - Remote System Discovery
TA0007-Discovery (Enterprise),T1033-System Owner/User Discovery - System Owner/User Discovery
TA0007-Discovery (Enterprise),T1040-Network Sniffing - Network Sniffing
TA0007-Discovery (Enterprise),T1046-Network Service Discovery - Network Service Discovery
TA0007-Discovery (Enterprise),T1049-System Network Connections Discovery - System Network Connections Discovery
TA0007-Discovery (Enterprise),T1057-Process Discovery - Process Discovery
TA0007-Discovery (Enterprise),T1069-Permission Groups Discovery - Permission Groups Discovery
TA0007-Discovery (Enterprise),T1069.001-Local Groups - Local Groups
TA0007-Discovery (Enterprise),T1069.002-Domain Groups - Domain Groups
TA0007-Discovery (Enterprise),T1069.003-Cloud Groups - Cloud Groups
TA0007-Discovery (Enterprise),T1082-System Information Discovery - System Information Discovery
TA0007-Discovery (Enterprise),T1083-File and Directory Discovery - File and Directory Discovery
TA0007-Discovery (Enterprise),T1087-Account Discovery - Account Discovery
TA0007-Discovery (Enterprise),T1087.001-Local Account - Local Account
TA0007-Discovery (Enterprise),T1087.002-Domain Account - Domain Account
TA0007-Discovery (Enterprise),T1087.003-Email Account - Email Account
TA0007-Discovery (Enterprise),T1087.004-Cloud Account - Cloud Account
TA0007-Discovery (Enterprise),T1120-Peripheral Device Discovery - Peripheral Device Discovery
TA0007-Discovery (Enterprise),T1124-System Time Discovery - System Time Discovery
TA0007-Discovery (Enterprise),T1135-Network Share Discovery - Network Share Discovery
TA0007-Discovery (Enterprise),T1201-Password Policy Discovery - Password Policy Discovery
TA0007-Discovery (Enterprise),T1217-Browser Information Discovery - Browser Information Discovery
TA0007-Discovery (Enterprise),T1482-Domain Trust Discovery - Domain Trust Discovery
TA0007-Discovery (Enterprise),T1497-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion
TA0007-Discovery (Enterprise),T1497.001-System Checks - System Checks
TA0007-Discovery (Enterprise),T1497.002-User Activity Based Checks - User Activity Based Checks
TA0007-Discovery (Enterprise),T1497.003-Time Based Evasion - Time Based Evasion
TA0007-Discovery (Enterprise),T1518-Software Discovery - Software Discovery
TA0007-Discovery (Enterprise),T1518.001-Security Software Discovery - Security Software Discovery
TA0007-Discovery (Enterprise),T1526-Cloud Service Discovery - Cloud Service Discovery
TA0007-Discovery (Enterprise),T1538-Cloud Service Dashboard - Cloud Service Dashboard
TA0007-Discovery (Enterprise),T1580-Cloud Infrastructure Discovery - Cloud Infrastructure Discovery
TA0007-Discovery (Enterprise),T1613-Container and Resource Discovery - Container and Resource Discovery
TA0007-Discovery (Enterprise),T1614-System Location Discovery - System Location Discovery
TA0007-Discovery (Enterprise),T1614.001-System Language Discovery - System Language Discovery
TA0007-Discovery (Enterprise),T1615-Group Policy Discovery - Group Policy Discovery
TA0007-Discovery (Enterprise),T1619-Cloud Storage Object Discovery - Cloud Storage Object Discovery
TA0007-Discovery (Enterprise),T1622-Debugger Evasion - Debugger Evasion
TA0007-Discovery (Enterprise),T1652-Device Driver Discovery - Device Driver Discovery
TA0007-Discovery (Enterprise),T1654-Log Enumeration - Log Enumeration
TA0007-Discovery (Enterprise),T1673-Virtual Machine Discovery - Virtual Machine Discovery
TA0008-Lateral Movement (Enterprise),T1021-Remote Services - Remote Services
TA0008-Lateral Movement (Enterprise),T1021.001-Remote Desktop Protocol - Remote Desktop Protocol
TA0008-Lateral Movement (Enterprise),T1021.002-SMB/Windows Admin Shares - SMB/Windows Admin Shares
TA0008-Lateral Movement (Enterprise),T1021.003-Distributed Component Object Model - Distributed Component Object Model
TA0008-Lateral Movement (Enterprise),T1021.004-SSH - SSH
TA0008-Lateral Movement (Enterprise),T1021.005-VNC - VNC
TA0008-Lateral Movement (Enterprise),T1021.006-Windows Remote Management - Windows Remote Management
TA0008-Lateral Movement (Enterprise),T1021.007-Cloud Services - Cloud Services
TA0008-Lateral Movement (Enterprise),T1021.008-Direct Cloud VM Connections - Direct Cloud VM Connections
TA0008-Lateral Movement (Enterprise),T1072-Software Deployment Tools - Software Deployment Tools
TA0008-Lateral Movement (Enterprise),T1080-Taint Shared Content - Taint Shared Content
TA0008-Lateral Movement (Enterprise),T1091-Replication Through Removable Media - Replication Through Removable Media
TA0008-Lateral Movement (Enterprise),T1210-Exploitation of Remote Services - Exploitation of Remote Services
TA0008-Lateral Movement (Enterprise),T1534-Internal Spearphishing - Internal Spearphishing
TA0008-Lateral Movement (Enterprise),T1550-Use Alternate Authentication Material - Use Alternate Authentication Material
TA0008-Lateral Movement (Enterprise),T1550.001-Application Access Token - Application Access Token
TA0008-Lateral Movement (Enterprise),T1550.002-Pass the Hash - Pass the Hash
TA0008-Lateral Movement (Enterprise),T1550.003-Pass the Ticket - Pass the Ticket
TA0008-Lateral Movement (Enterprise),T1550.004-Web Session Cookie - Web Session Cookie
TA0008-Lateral Movement (Enterprise),T1563-Remote Service Session Hijacking - Remote Service Session Hijacking
TA0008-Lateral Movement (Enterprise),T1563.001-SSH Hijacking - SSH Hijacking
TA0008-Lateral Movement (Enterprise),T1563.002-RDP Hijacking - RDP Hijacking
TA0008-Lateral Movement (Enterprise),T1570-Lateral Tool Transfer - Lateral Tool Transfer
TA0009-Collection (Enterprise),T1005-Data from Local System - Data from Local System
TA0009-Collection (Enterprise),T1025-Data from Removable Media - Data from Removable Media
TA0009-Collection (Enterprise),T1039-Data from Network Shared Drive - Data from Network Shared Drive
TA0009-Collection (Enterprise),T1056-Input Capture - Input Capture
TA0009-Collection (Enterprise),T1056.001-Keylogging - Keylogging
TA0009-Collection (Enterprise),T1056.002-GUI Input Capture - GUI Input Capture
TA0009-Collection (Enterprise),T1056.003-Web Portal Capture - Web Portal Capture
TA0009-Collection (Enterprise),T1056.004-Credential API Hooking - Credential API Hooking
TA0009-Collection (Enterprise),T1074-Data Staged - Data Staged
TA0009-Collection (Enterprise),T1074.001-Local Data Staging - Local Data Staging
TA0009-Collection (Enterprise),T1074.002-Remote Data Staging - Remote Data Staging
TA0009-Collection (Enterprise),T1113-Screen Capture - Screen Capture
TA0009-Collection (Enterprise),T1114-Email Collection - Email Collection
TA0009-Collection (Enterprise),T1114.001-Local Email Collection - Local Email Collection
TA0009-Collection (Enterprise),T1114.002-Remote Email Collection - Remote Email Collection
TA0009-Collection (Enterprise),T1114.003-Email Forwarding Rule - Email Forwarding Rule
TA0009-Collection (Enterprise),T1115-Clipboard Data - Clipboard Data
TA0009-Collection (Enterprise),T1119-Automated Collection - Automated Collection
TA0009-Collection (Enterprise),T1123-Audio Capture - Audio Capture
TA0009-Collection (Enterprise),T1125-Video Capture - Video Capture
TA0009-Collection (Enterprise),T1185-Browser Session Hijacking - Browser Session Hijacking
TA0009-Collection (Enterprise),T1213-Data from Information Repositories - Data from Information Repositories
TA0009-Collection (Enterprise),T1213.001-Confluence - Confluence
TA0009-Collection (Enterprise),T1213.002-Sharepoint - Sharepoint
TA0009-Collection (Enterprise),T1213.003-Code Repositories - Code Repositories
TA0009-Collection (Enterprise),T1213.004-Customer Relationship Management Software - Customer Relationship Management Software
TA0009-Collection (Enterprise),T1213.005-Messaging Applications - Messaging Applications
TA0009-Collection (Enterprise),T1530-Data from Cloud Storage - Data from Cloud Storage
TA0009-Collection (Enterprise),T1557-Adversary-in-the-Middle - Adversary-in-the-Middle
TA0009-Collection (Enterprise),T1557.001-LLMNR/NBT-NS Poisoning and SMB Relay - LLMNR/NBT-NS Poisoning and SMB Relay
TA0009-Collection (Enterprise),T1557.002-ARP Cache Poisoning - ARP Cache Poisoning
TA0009-Collection (Enterprise),T1557.003-DHCP Spoofing - DHCP Spoofing
TA0009-Collection (Enterprise),T1557.004-Evil Twin - Evil Twin
TA0009-Collection (Enterprise),T1560-Archive Collected Data - Archive Collected Data
TA0009-Collection (Enterprise),T1560.001-Archive via Utility - Archive via Utility
TA0009-Collection (Enterprise),T1560.002-Archive via Library - Archive via Library
TA0009-Collection (Enterprise),T1560.003-Archive via Custom Method - Archive via Custom Method
TA0009-Collection (Enterprise),T1602-Data from Configuration Repository - Data from Configuration Repository
TA0009-Collection (Enterprise),T1602.001-SNMP (MIB Dump) - SNMP (MIB Dump)
TA0009-Collection (Enterprise),T1602.002-Network Device Configuration Dump - Network Device Configuration Dump
TA0010-Exfiltration (Enterprise),T1011-Exfiltration Over Other Network Medium - Exfiltration Over Other Network Medium
TA0010-Exfiltration (Enterprise),T1011.001-Exfiltration Over Bluetooth - Exfiltration Over Bluetooth
TA0010-Exfiltration (Enterprise),T1020-Automated Exfiltration - Automated Exfiltration
TA0010-Exfiltration (Enterprise),T1020.001-Traffic Duplication - Traffic Duplication
TA0010-Exfiltration (Enterprise),T1029-Scheduled Transfer - Scheduled Transfer
TA0010-Exfiltration (Enterprise),T1030-Data Transfer Size Limits - Data Transfer Size Limits
TA0010-Exfiltration (Enterprise),T1041-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel
TA0010-Exfiltration (Enterprise),T1048-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol
TA0010-Exfiltration (Enterprise),T1048.001-Exfiltration Over Symmetric Encrypted Non-C2 Protocol - Exfiltration Over Symmetric Encrypted Non-C2 Protocol
TA0010-Exfiltration (Enterprise),T1048.002-Exfiltration Over Asymmetric Encrypted Non-C2 Protocol - Exfiltration Over Asymmetric Encrypted Non-C2 Protocol
TA0010-Exfiltration (Enterprise),T1048.003-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol
TA0010-Exfiltration (Enterprise),T1052-Exfiltration Over Physical Medium - Exfiltration Over Physical Medium
TA0010-Exfiltration (Enterprise),T1052.001-Exfiltration over USB - Exfiltration over USB
TA0010-Exfiltration (Enterprise),T1537-Transfer Data to Cloud Account - Transfer Data to Cloud Account
TA0010-Exfiltration (Enterprise),T1567-Exfiltration Over Web Service - Exfiltration Over Web Service
TA0010-Exfiltration (Enterprise),T1567.001-Exfiltration to Code Repository - Exfiltration to Code Repository
TA0010-Exfiltration (Enterprise),T1567.002-Exfiltration to Cloud Storage - Exfiltration to Cloud Storage
TA0010-Exfiltration (Enterprise),T1567.003-Exfiltration to Text Storage Sites - Exfiltration to Text Storage Sites
TA0010-Exfiltration (Enterprise),T1567.004-Exfiltration Over Webhook - Exfiltration Over Webhook
TA0011-Command and Control (Enterprise),T1001-Data Obfuscation - Data Obfuscation
TA0011-Command and Control (Enterprise),T1001.001-Junk Data - Junk Data
TA0011-Command and Control (Enterprise),T1001.002-Steganography - Steganography
TA0011-Command and Control (Enterprise),T1001.003-Protocol or Service Impersonation - Protocol or Service Impersonation
TA0011-Command and Control (Enterprise),T1008-Fallback Channels - Fallback Channels
TA0011-Command and Control (Enterprise),T1071-Application Layer Protocol - Application Layer Protocol
TA0011-Command and Control (Enterprise),T1071.001-Web Protocols - Web Protocols
TA0011-Command and Control (Enterprise),T1071.002-File Transfer Protocols - File Transfer Protocols
TA0011-Command and Control (Enterprise),T1071.003-Mail Protocols - Mail Protocols
TA0011-Command and Control (Enterprise),T1071.004-DNS - DNS
TA0011-Command and Control (Enterprise),T1071.005-Publish/Subscribe Protocols - Publish/Subscribe Protocols
TA0011-Command and Control (Enterprise),T1090-Proxy - Proxy
TA0011-Command and Control (Enterprise),T1090.001-Internal Proxy - Internal Proxy
TA0011-Command and Control (Enterprise),T1090.002-External Proxy - External Proxy
TA0011-Command and Control (Enterprise),T1090.003-Multi-hop Proxy - Multi-hop Proxy
TA0011-Command and Control (Enterprise),T1090.004-Domain Fronting - Domain Fronting
TA0011-Command and Control (Enterprise),T1092-Communication Through Removable Media - Communication Through Removable Media
TA0011-Command and Control (Enterprise),T1095-Non-Application Layer Protocol - Non-Application Layer Protocol
TA0011-Command and Control (Enterprise),T1102-Web Service - Web Service
TA0011-Command and Control (Enterprise),T1102.001-Dead Drop Resolver - Dead Drop Resolver
TA0011-Command and Control (Enterprise),T1102.002-Bidirectional Communication - Bidirectional Communication
TA0011-Command and Control (Enterprise),T1102.003-One-Way Communication - One-Way Communication
TA0011-Command and Control (Enterprise),T1104-Multi-Stage Channels - Multi-Stage Channels
TA0011-Command and Control (Enterprise),T1105-Ingress Tool Transfer - Ingress Tool Transfer
TA0011-Command and Control (Enterprise),T1132-Data Encoding - Data Encoding
TA0011-Command and Control (Enterprise),T1132.001-Standard Encoding - Standard Encoding
TA0011-Command and Control (Enterprise),T1132.002-Non-Standard Encoding - Non-Standard Encoding
TA0011-Command and Control (Enterprise),T1205-Traffic Signaling - Traffic Signaling
TA0011-Command and Control (Enterprise),T1205.001-Port Knocking - Port Knocking
TA0011-Command and Control (Enterprise),T1205.002-Socket Filters - Socket Filters
TA0011-Command and Control (Enterprise),T1219-Remote Access Tools - Remote Access Tools
TA0011-Command and Control (Enterprise),T1219.001-IDE Tunneling - IDE Tunneling
TA0011-Command and Control (Enterprise),T1219.002-Remote Desktop Software - Remote Desktop Software
TA0011-Command and Control (Enterprise),T1219.003-Remote Access Hardware - Remote Access Hardware
TA0011-Command and Control (Enterprise),T1568-Dynamic Resolution - Dynamic Resolution
TA0011-Command and Control (Enterprise),T1568.001-Fast Flux DNS - Fast Flux DNS
TA0011-Command and Control (Enterprise),T1568.002-Domain Generation Algorithms - Domain Generation Algorithms
TA0011-Command and Control (Enterprise),T1568.003-DNS Calculation - DNS Calculation
TA0011-Command and Control (Enterprise),T1571-Non-Standard Port - Non-Standard Port
TA0011-Command and Control (Enterprise),T1572-Protocol Tunneling - Protocol Tunneling
TA0011-Command and Control (Enterprise),T1573-Encrypted Channel - Encrypted Channel
TA0011-Command and Control (Enterprise),T1573.001-Symmetric Cryptography - Symmetric Cryptography
TA0011-Command and Control (Enterprise),T1573.002-Asymmetric Cryptography - Asymmetric Cryptography
TA0011-Command and Control (Enterprise),T1659-Content Injection - Content Injection
TA0011-Command and Control (Enterprise),T1665-Hide Infrastructure - Hide Infrastructure
TA0027-Initial Access (Mobile),T1451-SIM Card Swap - SIM Card Swap
TA0027-Initial Access (Mobile),T1456-Drive-By Compromise - Drive-By Compromise
TA0027-Initial Access (Mobile),T1458-Replication Through Removable Media - Replication Through Removable Media
TA0027-Initial Access (Mobile),T1461-Lockscreen Bypass - Lockscreen Bypass
TA0027-Initial Access (Mobile),T1474-Supply Chain Compromise - Supply Chain Compromise
TA0027-Initial Access (Mobile),T1474.001-Compromise Software Dependencies and Development Tools - Compromise Software Dependencies and Development Tools
TA0027-Initial Access (Mobile),T1474.002-Compromise Hardware Supply Chain - Compromise Hardware Supply Chain
TA0027-Initial Access (Mobile),T1474.003-Compromise Software Supply Chain - Compromise Software Supply Chain
TA0027-Initial Access (Mobile),T1660-Phishing - Phishing
TA0027-Initial Access (Mobile),T1661-Application Versioning - Application Versioning
TA0027-Initial Access (Mobile),T1664-Exploitation for Initial Access - Exploitation for Initial Access
TA0028-Persistence (Mobile),T1398-Boot or Logon Initialization Scripts - Boot or Logon Initialization Scripts
TA0028-Persistence (Mobile),T1541-Foreground Persistence - Foreground Persistence
TA0028-Persistence (Mobile),T1577-Compromise Application Executable - Compromise Application Executable
TA0028-Persistence (Mobile),T1603-Scheduled Task/Job - Scheduled Task/Job
TA0028-Persistence (Mobile),T1624-Event Triggered Execution - Event Triggered Execution
TA0028-Persistence (Mobile),T1624.001-Broadcast Receivers - Broadcast Receivers
TA0028-Persistence (Mobile),T1625-Hijack Execution Flow - Hijack Execution Flow
TA0028-Persistence (Mobile),T1625.001-System Runtime API Hijacking - System Runtime API Hijacking
TA0028-Persistence (Mobile),T1645-Compromise Client Software Binary - Compromise Client Software Binary
TA0029-Privilege Escalation (Mobile),T1404-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation
TA0029-Privilege Escalation (Mobile),T1626-Abuse Elevation Control Mechanism - Abuse Elevation Control Mechanism
TA0029-Privilege Escalation (Mobile),T1626.001-Device Administrator Permissions - Device Administrator Permissions
TA0029-Privilege Escalation (Mobile),T1631-Process Injection - Process Injection
TA0029-Privilege Escalation (Mobile),T1631.001-Ptrace System Calls - Ptrace System Calls
TA0030-Defense Evasion (Mobile),T1406-Obfuscated Files or Information - Obfuscated Files or Information
TA0030-Defense Evasion (Mobile),T1406.001-Steganography - Steganography
TA0030-Defense Evasion (Mobile),T1406.002-Software Packing - Software Packing
TA0030-Defense Evasion (Mobile),T1407-Download New Code at Runtime - Download New Code at Runtime
TA0030-Defense Evasion (Mobile),T1516-Input Injection - Input Injection
TA0030-Defense Evasion (Mobile),T1541-Foreground Persistence - Foreground Persistence
TA0030-Defense Evasion (Mobile),T1575-Native API - Native API
TA0030-Defense Evasion (Mobile),T1604-Proxy Through Victim - Proxy Through Victim
TA0030-Defense Evasion (Mobile),T1617-Hooking - Hooking
TA0030-Defense Evasion (Mobile),T1627-Execution Guardrails - Execution Guardrails
TA0030-Defense Evasion (Mobile),T1627.001-Geofencing - Geofencing
TA0030-Defense Evasion (Mobile),T1628-Hide Artifacts - Hide Artifacts
TA0030-Defense Evasion (Mobile),T1628.001-Suppress Application Icon - Suppress Application Icon
TA0030-Defense Evasion (Mobile),T1628.002-User Evasion - User Evasion
TA0030-Defense Evasion (Mobile),T1628.003-Conceal Multimedia Files - Conceal Multimedia Files
TA0030-Defense Evasion (Mobile),T1629-Impair Defenses - Impair Defenses
TA0030-Defense Evasion (Mobile),T1629.001-Prevent Application Removal - Prevent Application Removal
TA0030-Defense Evasion (Mobile),T1629.002-Device Lockout - Device Lockout
TA0030-Defense Evasion (Mobile),T1629.003-Disable or Modify Tools - Disable or Modify Tools
TA0030-Defense Evasion (Mobile),T1630-Indicator Removal on Host - Indicator Removal on Host
TA0030-Defense Evasion (Mobile),T1630.001-Uninstall Malicious Application - Uninstall Malicious Application
TA0030-Defense Evasion (Mobile),T1630.002-File Deletion - File Deletion
TA0030-Defense Evasion (Mobile),T1630.003-Disguise Root/Jailbreak Indicators - Disguise Root/Jailbreak Indicators
TA0030-Defense Evasion (Mobile),T1631-Process Injection - Process Injection
TA0030-Defense Evasion (Mobile),T1631.001-Ptrace System Calls - Ptrace System Calls
TA0030-Defense Evasion (Mobile),T1632-Subvert Trust Controls - Subvert Trust Controls
TA0030-Defense Evasion (Mobile),T1632.001-Code Signing Policy Modification - Code Signing Policy Modification
TA0030-Defense Evasion (Mobile),T1633-Virtualization/Sandbox Evasion - Virtualization/Sandbox Evasion
TA0030-Defense Evasion (Mobile),T1633.001-System Checks - System Checks
TA0030-Defense Evasion (Mobile),T1655-Masquerading - Masquerading
TA0030-Defense Evasion (Mobile),T1655.001-Match Legitimate Name or Location - Match Legitimate Name or Location
TA0030-Defense Evasion (Mobile),T1661-Application Versioning - Application Versioning
TA0030-Defense Evasion (Mobile),T1670-Virtualization Solution - Virtualization Solution
TA0031-Credential Access (Mobile),T1414-Clipboard Data - Clipboard Data
TA0031-Credential Access (Mobile),T1417-Input Capture - Input Capture
TA0031-Credential Access (Mobile),T1417.001-Keylogging - Keylogging
TA0031-Credential Access (Mobile),T1417.002-GUI Input Capture - GUI Input Capture
TA0031-Credential Access (Mobile),T1517-Access Notifications - Access Notifications
TA0031-Credential Access (Mobile),T1634-Credentials from Password Store - Credentials from Password Store
TA0031-Credential Access (Mobile),T1634.001-Keychain - Keychain
TA0031-Credential Access (Mobile),T1635-Steal Application Access Token - Steal Application Access Token
TA0031-Credential Access (Mobile),T1635.001-URI Hijacking - URI Hijacking
TA0032-Discovery (Mobile),T1418-Software Discovery - Software Discovery
TA0032-Discovery (Mobile),T1418.001-Security Software Discovery - Security Software Discovery
TA0032-Discovery (Mobile),T1420-File and Directory Discovery - File and Directory Discovery
TA0032-Discovery (Mobile),T1421-System Network Connections Discovery - System Network Connections Discovery
TA0032-Discovery (Mobile),T1422-System Network Configuration Discovery - System Network Configuration Discovery
TA0032-Discovery (Mobile),T1422.001-Internet Connection Discovery - Internet Connection Discovery
TA0032-Discovery (Mobile),T1422.002-Wi-Fi Discovery - Wi-Fi Discovery
TA0032-Discovery (Mobile),T1423-Network Service Scanning - Network Service Scanning
TA0032-Discovery (Mobile),T1424-Process Discovery - Process Discovery
TA0032-Discovery (Mobile),T1426-System Information Discovery - System Information Discovery
TA0032-Discovery (Mobile),T1430-Location Tracking - Location Tracking
TA0032-Discovery (Mobile),T1430.001-Remote Device Management Services - Remote Device Management Services
TA0032-Discovery (Mobile),T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes
TA0033-Lateral Movement (Mobile),T1428-Exploitation of Remote Services - Exploitation of Remote Services
TA0033-Lateral Movement (Mobile),T1458-Replication Through Removable Media - Replication Through Removable Media
TA0034-Impact (Mobile),T1464-Network Denial of Service - Network Denial of Service
TA0034-Impact (Mobile),T1471-Data Encrypted for Impact - Data Encrypted for Impact
TA0034-Impact (Mobile),T1516-Input Injection - Input Injection
TA0034-Impact (Mobile),T1582-SMS Control - SMS Control
TA0034-Impact (Mobile),T1616-Call Control - Call Control
TA0034-Impact (Mobile),T1640-Account Access Removal - Account Access Removal
TA0034-Impact (Mobile),T1641-Data Manipulation - Data Manipulation
TA0034-Impact (Mobile),T1641.001-Transmitted Data Manipulation - Transmitted Data Manipulation
TA0034-Impact (Mobile),T1642-Endpoint Denial of Service - Endpoint Denial of Service
TA0034-Impact (Mobile),T1643-Generate Traffic from Victim - Generate Traffic from Victim
TA0034-Impact (Mobile),T1662-Data Destruction - Data Destruction
TA0035-Collection (Mobile),T1409-Stored Application Data - Stored Application Data
TA0035-Collection (Mobile),T1414-Clipboard Data - Clipboard Data
TA0035-Collection (Mobile),T1417-Input Capture - Input Capture
TA0035-Collection (Mobile),T1417.001-Keylogging - Keylogging
TA0035-Collection (Mobile),T1417.002-GUI Input Capture - GUI Input Capture
TA0035-Collection (Mobile),T1429-Audio Capture - Audio Capture
TA0035-Collection (Mobile),T1430-Location Tracking - Location Tracking
TA0035-Collection (Mobile),T1430.001-Remote Device Management Services - Remote Device Management Services
TA0035-Collection (Mobile),T1430.002-Impersonate SS7 Nodes - Impersonate SS7 Nodes
TA0035-Collection (Mobile),T1512-Video Capture - Video Capture
TA0035-Collection (Mobile),T1513-Screen Capture - Screen Capture
TA0035-Collection (Mobile),T1517-Access Notifications - Access Notifications
TA0035-Collection (Mobile),T1532-Archive Collected Data - Archive Collected Data
TA0035-Collection (Mobile),T1533-Data from Local System - Data from Local System
TA0035-Collection (Mobile),T1616-Call Control - Call Control
TA0035-Collection (Mobile),T1636-Protected User Data - Protected User Data
TA0035-Collection (Mobile),T1636.001-Calendar Entries - Calendar Entries
TA0035-Collection (Mobile),T1636.002-Call Log - Call Log
TA0035-Collection (Mobile),T1636.003-Contact List - Contact List
TA0035-Collection (Mobile),T1636.004-SMS Messages - SMS Messages
TA0035-Collection (Mobile),T1638-Adversary-in-the-Middle - Adversary-in-the-Middle
TA0036-Exfiltration (Mobile),T1639-Exfiltration Over Alternative Protocol - Exfiltration Over Alternative Protocol
TA0036-Exfiltration (Mobile),T1639.001-Exfiltration Over Unencrypted Non-C2 Protocol - Exfiltration Over Unencrypted Non-C2 Protocol
TA0036-Exfiltration (Mobile),T1646-Exfiltration Over C2 Channel - Exfiltration Over C2 Channel
TA0037-Command and Control (Mobile),T1437-Application Layer Protocol - Application Layer Protocol
TA0037-Command and Control (Mobile),T1437.001-Web Protocols - Web Protocols
TA0037-Command and Control (Mobile),T1481-Web Service - Web Service
TA0037-Command and Control (Mobile),T1481.001-Dead Drop Resolver - Dead Drop Resolver
TA0037-Command and Control (Mobile),T1481.002-Bidirectional Communication - Bidirectional Communication
TA0037-Command and Control (Mobile),T1481.003-One-Way Communication - One-Way Communication
TA0037-Command and Control (Mobile),T1509-Non-Standard Port - Non-Standard Port
TA0037-Command and Control (Mobile),T1521-Encrypted Channel - Encrypted Channel
TA0037-Command and Control (Mobile),T1521.001-Symmetric Cryptography - Symmetric Cryptography
TA0037-Command and Control (Mobile),T1521.002-Asymmetric Cryptography - Asymmetric Cryptography
TA0037-Command and Control (Mobile),T1521.003-SSL Pinning - SSL Pinning
TA0037-Command and Control (Mobile),T1544-Ingress Tool Transfer - Ingress Tool Transfer
TA0037-Command and Control (Mobile),T1616-Call Control - Call Control
TA0037-Command and Control (Mobile),T1637-Dynamic Resolution - Dynamic Resolution
TA0037-Command and Control (Mobile),T1637.001-Domain Generation Algorithms - Domain Generation Algorithms
TA0037-Command and Control (Mobile),T1644-Out of Band Data - Out of Band Data
TA0037-Command and Control (Mobile),T1663-Remote Access Software - Remote Access Software
TA0040-Impact (Enterprise),T1485-Data Destruction - Data Destruction
TA0040-Impact (Enterprise),T1485.001-Lifecycle-Triggered Deletion - Lifecycle-Triggered Deletion
TA0040-Impact (Enterprise),T1486-Data Encrypted for Impact - Data Encrypted for Impact
TA0040-Impact (Enterprise),T1489-Service Stop - Service Stop
TA0040-Impact (Enterprise),T1490-Inhibit System Recovery - Inhibit System Recovery
TA0040-Impact (Enterprise),T1491-Defacement - Defacement
TA0040-Impact (Enterprise),T1491.001-Internal Defacement - Internal Defacement
TA0040-Impact (Enterprise),T1491.002-External Defacement - External Defacement
TA0040-Impact (Enterprise),T1495-Firmware Corruption - Firmware Corruption
TA0040-Impact (Enterprise),T1496-Resource Hijacking - Resource Hijacking
TA0040-Impact (Enterprise),T1496.001-Compute Hijacking - Compute Hijacking
TA0040-Impact (Enterprise),T1496.002-Bandwidth Hijacking - Bandwidth Hijacking
TA0040-Impact (Enterprise),T1496.003-SMS Pumping - SMS Pumping
TA0040-Impact (Enterprise),T1496.004-Cloud Service Hijacking - Cloud Service Hijacking
TA0040-Impact (Enterprise),T1498-Network Denial of Service - Network Denial of Service
TA0040-Impact (Enterprise),T1498.001-Direct Network Flood - Direct Network Flood
TA0040-Impact (Enterprise),T1498.002-Reflection Amplification - Reflection Amplification
TA0040-Impact (Enterprise),T1499-Endpoint Denial of Service - Endpoint Denial of Service
TA0040-Impact (Enterprise),T1499.001-OS Exhaustion Flood - OS Exhaustion Flood
TA0040-Impact (Enterprise),T1499.002-Service Exhaustion Flood - Service Exhaustion Flood
TA0040-Impact (Enterprise),T1499.003-Application Exhaustion Flood - Application Exhaustion Flood
TA0040-Impact (Enterprise),T1499.004-Application or System Exploitation - Application or System Exploitation
TA0040-Impact (Enterprise),T1529-System Shutdown/Reboot - System Shutdown/Reboot
TA0040-Impact (Enterprise),T1531-Account Access Removal - Account Access Removal
TA0040-Impact (Enterprise),T1561-Disk Wipe - Disk Wipe
TA0040-Impact (Enterprise),T1561.001-Disk Content Wipe - Disk Content Wipe
TA0040-Impact (Enterprise),T1561.002-Disk Structure Wipe - Disk Structure Wipe
TA0040-Impact (Enterprise),T1565-Data Manipulation - Data Manipulation
TA0040-Impact (Enterprise),T1565.001-Stored Data Manipulation - Stored Data Manipulation
TA0040-Impact (Enterprise),T1565.002-Transmitted Data Manipulation - Transmitted Data Manipulation
TA0040-Impact (Enterprise),T1565.003-Runtime Data Manipulation - Runtime Data Manipulation
TA0040-Impact (Enterprise),T1657-Financial Theft - Financial Theft
TA0040-Impact (Enterprise),T1667-Email Bombing - Email Bombing
TA0041-Execution (Mobile),T1575-Native API - Native API
TA0041-Execution (Mobile),T1603-Scheduled Task/Job - Scheduled Task/Job
TA0041-Execution (Mobile),T1623-Command and Scripting Interpreter - Command and Scripting Interpreter
TA0041-Execution (Mobile),T1623.001-Unix Shell - Unix Shell
TA0041-Execution (Mobile),T1658-Exploitation for Client Execution - Exploitation for Client Execution
TA0042-Resource Development (Enterprise),T1583-Acquire Infrastructure - Acquire Infrastructure
TA0042-Resource Development (Enterprise),T1583.001-Domains - Domains
TA0042-Resource Development (Enterprise),T1583.002-DNS Server - DNS Server
TA0042-Resource Development (Enterprise),T1583.003-Virtual Private Server - Virtual Private Server
TA0042-Resource Development (Enterprise),T1583.004-Server - Server
TA0042-Resource Development (Enterprise),T1583.005-Botnet - Botnet
TA0042-Resource Development (Enterprise),T1583.006-Web Services - Web Services
TA0042-Resource Development (Enterprise),T1583.007-Serverless - Serverless
TA0042-Resource Development (Enterprise),T1583.008-Malvertising - Malvertising
TA0042-Resource Development (Enterprise),T1584-Compromise Infrastructure - Compromise Infrastructure
TA0042-Resource Development (Enterprise),T1584.001-Domains - Domains
TA0042-Resource Development (Enterprise),T1584.002-DNS Server - DNS Server
TA0042-Resource Development (Enterprise),T1584.003-Virtual Private Server - Virtual Private Server
TA0042-Resource Development (Enterprise),T1584.004-Server - Server
TA0042-Resource Development (Enterprise),T1584.005-Botnet - Botnet
TA0042-Resource Development (Enterprise),T1584.006-Web Services - Web Services
TA0042-Resource Development (Enterprise),T1584.007-Serverless - Serverless
TA0042-Resource Development (Enterprise),T1584.008-Network Devices - Network Devices
TA0042-Resource Development (Enterprise),T1585-Establish Accounts - Establish Accounts
TA0042-Resource Development (Enterprise),T1585.001-Social Media Accounts - Social Media Accounts
TA0042-Resource Development (Enterprise),T1585.002-Email Accounts - Email Accounts
TA0042-Resource Development (Enterprise),T1585.003-Cloud Accounts - Cloud Accounts
TA0042-Resource Development (Enterprise),T1586-Compromise Accounts - Compromise Accounts
TA0042-Resource Development (Enterprise),T1586.001-Social Media Accounts - Social Media Accounts
TA0042-Resource Development (Enterprise),T1586.002-Email Accounts - Email Accounts
TA0042-Resource Development (Enterprise),T1586.003-Cloud Accounts - Cloud Accounts
TA0042-Resource Development (Enterprise),T1587-Develop Capabilities - Develop Capabilities
TA0042-Resource Development (Enterprise),T1587.001-Malware - Malware
TA0042-Resource Development (Enterprise),T1587.002-Code Signing Certificates - Code Signing Certificates
TA0042-Resource Development (Enterprise),T1587.003-Digital Certificates - Digital Certificates
TA0042-Resource Development (Enterprise),T1587.004-Exploits - Exploits
TA0042-Resource Development (Enterprise),T1588-Obtain Capabilities - Obtain Capabilities
TA0042-Resource Development (Enterprise),T1588.001-Malware - Malware
TA0042-Resource Development (Enterprise),T1588.002-Tool - Tool
TA0042-Resource Development (Enterprise),T1588.003-Code Signing Certificates - Code Signing Certificates
TA0042-Resource Development (Enterprise),T1588.004-Digital Certificates - Digital Certificates
TA0042-Resource Development (Enterprise),T1588.005-Exploits - Exploits
TA0042-Resource Development (Enterprise),T1588.006-Vulnerabilities - Vulnerabilities
TA0042-Resource Development (Enterprise),T1588.007-Artificial Intelligence - Artificial Intelligence
TA0042-Resource Development (Enterprise),T1608-Stage Capabilities - Stage Capabilities
TA0042-Resource Development (Enterprise),T1608.001-Upload Malware - Upload Malware
TA0042-Resource Development (Enterprise),T1608.002-Upload Tool - Upload Tool
TA0042-Resource Development (Enterprise),T1608.003-Install Digital Certificate - Install Digital Certificate
TA0042-Resource Development (Enterprise),T1608.004-Drive-by Target - Drive-by Target
TA0042-Resource Development (Enterprise),T1608.005-Link Target - Link Target
TA0042-Resource Development (Enterprise),T1608.006-SEO Poisoning - SEO Poisoning
TA0042-Resource Development (Enterprise),T1650-Acquire Access - Acquire Access
TA0043-Reconnaissance (Enterprise),T1589-Gather Victim Identity Information - Gather Victim Identity Information
TA0043-Reconnaissance (Enterprise),T1589.001-Credentials - Credentials
TA0043-Reconnaissance (Enterprise),T1589.002-Email Addresses - Email Addresses
TA0043-Reconnaissance (Enterprise),T1589.003-Employee Names - Employee Names
TA0043-Reconnaissance (Enterprise),T1590-Gather Victim Network Information - Gather Victim Network Information
TA0043-Reconnaissance (Enterprise),T1590.001-Domain Properties - Domain Properties
TA0043-Reconnaissance (Enterprise),T1590.002-DNS - DNS
TA0043-Reconnaissance (Enterprise),T1590.003-Network Trust Dependencies - Network Trust Dependencies
TA0043-Reconnaissance (Enterprise),T1590.004-Network Topology - Network Topology
TA0043-Reconnaissance (Enterprise),T1590.005-IP Addresses - IP Addresses
TA0043-Reconnaissance (Enterprise),T1590.006-Network Security Appliances - Network Security Appliances
TA0043-Reconnaissance (Enterprise),T1591-Gather Victim Org Information - Gather Victim Org Information
TA0043-Reconnaissance (Enterprise),T1591.001-Determine Physical Locations - Determine Physical Locations
TA0043-Reconnaissance (Enterprise),T1591.002-Business Relationships - Business Relationships
TA0043-Reconnaissance (Enterprise),T1591.003-Identify Business Tempo - Identify Business Tempo
TA0043-Reconnaissance (Enterprise),T1591.004-Identify Roles - Identify Roles
TA0043-Reconnaissance (Enterprise),T1592-Gather Victim Host Information - Gather Victim Host Information
TA0043-Reconnaissance (Enterprise),T1592.001-Hardware - Hardware
TA0043-Reconnaissance (Enterprise),T1592.002-Software - Software
TA0043-Reconnaissance (Enterprise),T1592.003-Firmware - Firmware
TA0043-Reconnaissance (Enterprise),T1592.004-Client Configurations - Client Configurations
TA0043-Reconnaissance (Enterprise),T1593-Search Open Websites/Domains - Search Open Websites/Domains
TA0043-Reconnaissance (Enterprise),T1593.001-Social Media - Social Media
TA0043-Reconnaissance (Enterprise),T1593.002-Search Engines - Search Engines
TA0043-Reconnaissance (Enterprise),T1593.003-Code Repositories - Code Repositories
TA0043-Reconnaissance (Enterprise),T1594-Search Victim-Owned Websites - Search Victim-Owned Websites
TA0043-Reconnaissance (Enterprise),T1595-Active Scanning - Active Scanning
TA0043-Reconnaissance (Enterprise),T1595.001-Scanning IP Blocks - Scanning IP Blocks
TA0043-Reconnaissance (Enterprise),T1595.002-Vulnerability Scanning - Vulnerability Scanning
TA0043-Reconnaissance (Enterprise),T1595.003-Wordlist Scanning - Wordlist Scanning
TA0043-Reconnaissance (Enterprise),T1596-Search Open Technical Databases - Search Open Technical Databases
TA0043-Reconnaissance (Enterprise),T1596.001-DNS/Passive DNS - DNS/Passive DNS
TA0043-Reconnaissance (Enterprise),T1596.002-WHOIS - WHOIS
TA0043-Reconnaissance (Enterprise),T1596.003-Digital Certificates - Digital Certificates
TA0043-Reconnaissance (Enterprise),T1596.004-CDNs - CDNs
TA0043-Reconnaissance (Enterprise),T1596.005-Scan Databases - Scan Databases
TA0043-Reconnaissance (Enterprise),T1597-Search Closed Sources - Search Closed Sources
TA0043-Reconnaissance (Enterprise),T1597.001-Threat Intel Vendors - Threat Intel Vendors
TA0043-Reconnaissance (Enterprise),T1597.002-Purchase Technical Data - Purchase Technical Data
TA0043-Reconnaissance (Enterprise),T1598-Phishing for Information - Phishing for Information
TA0043-Reconnaissance (Enterprise),T1598.001-Spearphishing Service - Spearphishing Service
TA0043-Reconnaissance (Enterprise),T1598.002-Spearphishing Attachment - Spearphishing Attachment
TA0043-Reconnaissance (Enterprise),T1598.003-Spearphishing Link - Spearphishing Link
TA0043-Reconnaissance (Enterprise),T1598.004-Spearphishing Voice - Spearphishing Voice
TA0100-Collection (ICS),T0801-Monitor Process State - Monitor Process State
TA0100-Collection (ICS),T0802-Automated Collection - Automated Collection
TA0100-Collection (ICS),T0811-Data from Information Repositories - Data from Information Repositories
TA0100-Collection (ICS),T0830-Adversary-in-the-Middle - Adversary-in-the-Middle
TA0100-Collection (ICS),T0845-Program Upload - Program Upload
TA0100-Collection (ICS),T0852-Screen Capture - Screen Capture
TA0100-Collection (ICS),T0861-Point & Tag Identification - Point & Tag Identification
TA0100-Collection (ICS),T0868-Detect Operating Mode - Detect Operating Mode
TA0100-Collection (ICS),T0877-I/O Image - I/O Image
TA0100-Collection (ICS),T0887-Wireless Sniffing - Wireless Sniffing
TA0100-Collection (ICS),T0893-Data from Local System - Data from Local System
TA0101-Command and Control (ICS),T0869-Standard Application Layer Protocol - Standard Application Layer Protocol
TA0101-Command and Control (ICS),T0884-Connection Proxy - Connection Proxy
TA0101-Command and Control (ICS),T0885-Commonly Used Port - Commonly Used Port
TA0102-Discovery (ICS),T0840-Network Connection Enumeration - Network Connection Enumeration
TA0102-Discovery (ICS),T0842-Network Sniffing - Network Sniffing
TA0102-Discovery (ICS),T0846-Remote System Discovery - Remote System Discovery
TA0102-Discovery (ICS),T0887-Wireless Sniffing - Wireless Sniffing
TA0102-Discovery (ICS),T0888-Remote System Information Discovery - Remote System Information Discovery
TA0103-Evasion (ICS),T0820-Exploitation for Evasion - Exploitation for Evasion
TA0103-Evasion (ICS),T0849-Masquerading - Masquerading
TA0103-Evasion (ICS),T0851-Rootkit - Rootkit
TA0103-Evasion (ICS),T0856-Spoof Reporting Message - Spoof Reporting Message
TA0103-Evasion (ICS),T0858-Change Operating Mode - Change Operating Mode
TA0103-Evasion (ICS),T0872-Indicator Removal on Host - Indicator Removal on Host
TA0103-Evasion (ICS),T0894-System Binary Proxy Execution - System Binary Proxy Execution
TA0104-Execution (ICS),T0807-Command-Line Interface - Command-Line Interface
TA0104-Execution (ICS),T0821-Modify Controller Tasking - Modify Controller Tasking
TA0104-Execution (ICS),T0823-Graphical User Interface - Graphical User Interface
TA0104-Execution (ICS),T0834-Native API - Native API
TA0104-Execution (ICS),T0853-Scripting - Scripting
TA0104-Execution (ICS),T0858-Change Operating Mode - Change Operating Mode
TA0104-Execution (ICS),T0863-User Execution - User Execution
TA0104-Execution (ICS),T0871-Execution through API - Execution through API
TA0104-Execution (ICS),T0874-Hooking - Hooking
TA0104-Execution (ICS),T0895-Autorun Image - Autorun Image
TA0105-Impact (ICS),T0813-Denial of Control - Denial of Control
TA0105-Impact (ICS),T0815-Denial of View - Denial of View
TA0105-Impact (ICS),T0826-Loss of Availability - Loss of Availability
TA0105-Impact (ICS),T0827-Loss of Control - Loss of Control
TA0105-Impact (ICS),T0828-Loss of Productivity and Revenue - Loss of Productivity and Revenue
TA0105-Impact (ICS),T0829-Loss of View - Loss of View
TA0105-Impact (ICS),T0831-Manipulation of Control - Manipulation of Control
TA0105-Impact (ICS),T0832-Manipulation of View - Manipulation of View
TA0105-Impact (ICS),T0837-Loss of Protection - Loss of Protection
TA0105-Impact (ICS),T0879-Damage to Property - Damage to Property
TA0105-Impact (ICS),T0880-Loss of Safety - Loss of Safety
TA0105-Impact (ICS),T0882-Theft of Operational Information - Theft of Operational Information
TA0106-Impair Process Control (ICS),T0806-Brute Force I/O - Brute Force I/O
TA0106-Impair Process Control (ICS),T0836-Modify Parameter - Modify Parameter
TA0106-Impair Process Control (ICS),T0839-Module Firmware - Module Firmware
TA0106-Impair Process Control (ICS),T0855-Unauthorized Command Message - Unauthorized Command Message
TA0106-Impair Process Control (ICS),T0856-Spoof Reporting Message - Spoof Reporting Message
TA0107-Inhibit Response Function (ICS),T0803-Block Command Message - Block Command Message
TA0107-Inhibit Response Function (ICS),T0804-Block Reporting Message - Block Reporting Message
TA0107-Inhibit Response Function (ICS),T0805-Block Serial COM - Block Serial COM
TA0107-Inhibit Response Function (ICS),T0809-Data Destruction - Data Destruction
TA0107-Inhibit Response Function (ICS),T0814-Denial of Service - Denial of Service
TA0107-Inhibit Response Function (ICS),T0816-Device Restart/Shutdown - Device Restart/Shutdown
TA0107-Inhibit Response Function (ICS),T0835-Manipulate I/O Image - Manipulate I/O Image
TA0107-Inhibit Response Function (ICS),T0838-Modify Alarm Settings - Modify Alarm Settings
TA0107-Inhibit Response Function (ICS),T0851-Rootkit - Rootkit
TA0107-Inhibit Response Function (ICS),T0857-System Firmware - System Firmware
TA0107-Inhibit Response Function (ICS),T0878-Alarm Suppression - Alarm Suppression
TA0107-Inhibit Response Function (ICS),T0881-Service Stop - Service Stop
TA0107-Inhibit Response Function (ICS),T0892-Change Credential - Change Credential
TA0108-Initial Access (ICS),T0817-Drive-by Compromise - Drive-by Compromise
TA0108-Initial Access (ICS),T0819-Exploit Public-Facing Application - Exploit Public-Facing Application
TA0108-Initial Access (ICS),T0822-External Remote Services - External Remote Services
TA0108-Initial Access (ICS),T0847-Replication Through Removable Media - Replication Through Removable Media
TA0108-Initial Access (ICS),T0848-Rogue Master - Rogue Master
TA0108-Initial Access (ICS),T0860-Wireless Compromise - Wireless Compromise
TA0108-Initial Access (ICS),T0862-Supply Chain Compromise - Supply Chain Compromise
TA0108-Initial Access (ICS),T0864-Transient Cyber Asset - Transient Cyber Asset
TA0108-Initial Access (ICS),T0865-Spearphishing Attachment - Spearphishing Attachment
TA0108-Initial Access (ICS),T0866-Exploitation of Remote Services - Exploitation of Remote Services
TA0108-Initial Access (ICS),T0883-Internet Accessible Device - Internet Accessible Device
TA0108-Initial Access (ICS),T0886-Remote Services - Remote Services
TA0109-Lateral Movement (ICS),T0812-Default Credentials - Default Credentials
TA0109-Lateral Movement (ICS),T0843-Program Download - Program Download
TA0109-Lateral Movement (ICS),T0859-Valid Accounts - Valid Accounts
TA0109-Lateral Movement (ICS),T0866-Exploitation of Remote Services - Exploitation of Remote Services
TA0109-Lateral Movement (ICS),T0867-Lateral Tool Transfer - Lateral Tool Transfer
TA0109-Lateral Movement (ICS),T0886-Remote Services - Remote Services
TA0109-Lateral Movement (ICS),T0891-Hardcoded Credentials - Hardcoded Credentials
TA0110-Persistence (ICS),T0839-Module Firmware - Module Firmware
TA0110-Persistence (ICS),T0857-System Firmware - System Firmware
TA0110-Persistence (ICS),T0859-Valid Accounts - Valid Accounts
TA0110-Persistence (ICS),T0873-Project File Infection - Project File Infection
TA0110-Persistence (ICS),T0889-Modify Program - Modify Program
TA0110-Persistence (ICS),T0891-Hardcoded Credentials - Hardcoded Credentials
TA0111-Privilege Escalation (ICS),T0874-Hooking - Hooking
TA0111-Privilege Escalation (ICS),T0890-Exploitation for Privilege Escalation - Exploitation for Privilege Escalation
