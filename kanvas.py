import sys
import os
import sqlite3
import shutil
import re
import traceback
import logging
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import openpyxl
from filelock import FileLock
from PySide6 import __version__

# Disable xlwings to prevent Excel file conflicts and corruption
XLWINGS_AVAILABLE = False
# xlwings can cause file corruption and conflicts with openpyxl when handling .xlsm files
from PySide6.QtWidgets import (
    QApplication, QMessageBox, QPushButton, QMainWindow, QFileDialog, QTreeView, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QComboBox, QGridLayout, QTextEdit, QSizePolicy, QWidget, QDateEdit, QProgressBar, QScrollArea, QHeaderView, QSplashScreen, QMenu
)
from PySide6.QtGui import QStandardItemModel, QStandardItem, QColor, QFont, QPixmap
from PySide6.QtCore import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QFileSystemWatcher
from viz_network import visualize_network
from viz_timeline import open_timeline_window
from helper.database_utils import create_all_tables
from helper.download_updates import download_updates
from helper.mapping_defend import open_defend_window
from helper.mapping_attack import mitre_mapping
from helper.mapping_veris import open_veris_window
from helper.bookmarks import display_bookmarks_kb
from helper.lookup_eventid import display_event_id_kb
from helper.lookup_entraid import open_entra_lookup_window
from helper.lookup_domain import open_domain_lookup_window
from helper.lookup_cve import open_cve_window
from helper.lookup_ip import open_ip_lookup_window
from helper.lookup_file import open_hash_lookup_window
from helper.lookup_ransomware import open_ransomware_kb_window
from markdown_editor import handle_markdown_editor
from filelock import FileLock, Timeout
from PySide6.QtGui import QIcon
from shiboken6 import isValid 
from helper.windowsui import Ui_KanvasMainWindow

class MainApp:
    def __init__(self):
        # Handle QApplication creation - use existing one if available
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', filename='kanvas.log')
        self.logger = logging.getLogger(__name__)

        # Sheet configuration for different sheet types
        self.sheet_configs = {
            "Evidence Management": {
                "header_row": 4,
                "dropdown_row": 5,
                "data_start_row": 6
            },
            "Timeline (Master)": {
                "header_row": 12,
                "dropdown_row": 13,
                "data_start_row": 14
            },
            "Timeline (Analyst)": {
                "header_row": 12,
                "dropdown_row": 13,
                "data_start_row": 14
            },
            "Timeline": {  # Fallback for generic Timeline sheet
                "header_row": 12,
                "dropdown_row": 13,
                "data_start_row": 14
            },
            # Default configuration for other sheets
            "default": {
                "header_row": 1,
                "dropdown_row": 2,
                "data_start_row": 2
            }
        }

        image_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "images")
        logo_path = os.path.join(image_dir, "logo.png")
        app_icon = QIcon(logo_path)
        self.app.setWindowIcon(app_icon)
        splash_pixmap = QPixmap(logo_path)
        if not splash_pixmap.isNull():
            splash_pixmap = splash_pixmap.scaled(300, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.splash = QSplashScreen(splash_pixmap)
        else:
            splash_pixmap = QPixmap(300, 150)
            splash_pixmap.fill(QColor(40, 44, 52))
            self.splash = QSplashScreen(splash_pixmap)
            self.splash.showMessage("KANVAS\n \nLoading...", Qt.AlignCenter, QColor(255, 255, 255))
        self.splash.show()
        self.app.processEvents()
        self.child_windows = []
        self.db_path = "kanvas.db"
        self.file_lock = None 
        self.read_only_mode = False  
        self.splash.showMessage("Initializing database...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        self.app.processEvents()
        create_all_tables(self.db_path)
        self.splash.showMessage("Loading user interface...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        self.app.processEvents()
        self.window = self.load_ui()
        self.window.closeEvent = self.closeEvent
        self.current_workbook = None
        self.current_file_path = None
        self.current_sheet_name = None
        self.current_dropdown_values = {}

        # File synchronization and multi-user collaboration
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.fileChanged.connect(self.on_excel_file_changed)
        self.last_modified_time = None
        self.sync_in_progress = False
        self.auto_sync_enabled = True
        self.sync_timer = QTimer()
        self.sync_timer.timeout.connect(self.check_for_external_changes)
        self.sync_timer.start(2000)  # Check every 2 seconds for changes

        # File locking for multi-KANVAS coordination
        self.file_lock = None
        self.lock_timeout = 10  # seconds
        self.splash.showMessage("Connecting UI elements...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        self.app.processEvents()
        self.connect_ui_elements()
        self.add_sync_controls()
        QTimer.singleShot(1000, self.finish_loading)

    def get_sheet_config(self, sheet_name):
        """Get configuration for a specific sheet type"""
        if sheet_name in self.sheet_configs:
            return self.sheet_configs[sheet_name]
        else:
            self.logger.info(f"Using default configuration for sheet: {sheet_name}")
            return self.sheet_configs["default"]

    def setup_file_watcher(self, file_path):
        """Setup file system watcher for Excel file synchronization"""
        try:
            # Remove any existing watched files
            watched_files = self.file_watcher.files()
            if watched_files:
                self.file_watcher.removePaths(watched_files)

            # Add the new file to watch
            if file_path and os.path.exists(file_path):
                self.file_watcher.addPath(file_path)
                self.last_modified_time = os.path.getmtime(file_path)
                self.logger.info(f"File watcher setup for: {file_path}")
                return True
            else:
                self.logger.warning(f"Cannot watch file - does not exist: {file_path}")
                return False

        except Exception as e:
            self.logger.error(f"Error setting up file watcher: {e}")
            return False

    def on_excel_file_changed(self, file_path):
        """Handle Excel file changes - reload KANVAS data"""
        try:
            self.logger.info(f"File change detected: {file_path}")

            if self.sync_in_progress:
                self.logger.debug("Sync already in progress, skipping file change event")
                return

            if not os.path.exists(file_path):
                self.logger.warning(f"File no longer exists: {file_path}")
                return

            # Check if file was actually modified (not just accessed)
            current_modified_time = os.path.getmtime(file_path)
            self.logger.info(f"File timestamps - Current: {current_modified_time}, Last: {self.last_modified_time}")

            if self.last_modified_time and current_modified_time <= self.last_modified_time:
                self.logger.debug("File timestamp unchanged, skipping reload")
                return

            self.last_modified_time = current_modified_time
            self.logger.info(f"Excel file changed, reloading KANVAS data: {file_path}")

            # Show notification to user
            QMessageBox.information(self.window, "File Changed",
                "Excel file has been modified externally.\nReloading data...")

            # Reload the workbook and refresh the view
            QTimer.singleShot(500, self.reload_excel_data)  # Small delay to ensure file is fully written

        except Exception as e:
            self.logger.error(f"Error handling file change: {e}")

    def check_for_external_changes(self):
        """Periodically check for external changes to Excel file (multi-user support)"""
        try:
            if not self.auto_sync_enabled or self.sync_in_progress:
                return

            if not self.current_file_path or not os.path.exists(self.current_file_path):
                return

            # Check if file was modified externally
            current_modified_time = os.path.getmtime(self.current_file_path)
            if self.last_modified_time and current_modified_time > self.last_modified_time:
                self.logger.info(f"External change detected - file modified at {current_modified_time}")

                # Show notification and ask user what to do
                reply = QMessageBox.question(self.window, "External Changes Detected",
                    "The Excel file has been modified by another user or application.\n\n"
                    "Would you like to reload the data to see the latest changes?\n\n"
                    "⚠️ Any unsaved changes in KANVAS will be lost.",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

                if reply == QMessageBox.Yes:
                    self.last_modified_time = current_modified_time
                    self.reload_excel_data()
                else:
                    # User chose not to reload - update timestamp to avoid repeated prompts
                    self.last_modified_time = current_modified_time

        except Exception as e:
            self.logger.error(f"Error checking for external changes: {e}")

    def reload_excel_data(self):
        """Reload Excel data into KANVAS after file change"""
        try:
            if not self.current_file_path or not os.path.exists(self.current_file_path):
                return

            self.logger.info("Reloading Excel data due to file change...")

            # Temporarily disable file watcher to prevent recursive updates
            self.sync_in_progress = True

            # Reload the workbook
            self.current_workbook = openpyxl.load_workbook(self.current_file_path)

            # Refresh the current sheet view
            if self.current_sheet_name:
                self.load_sheet()
                self.logger.info("Excel data reloaded successfully")

                # Show notification to user
                if hasattr(self, 'window') and hasattr(self.window, 'statusBar'):
                    self.window.statusBar().showMessage("Excel file updated - data reloaded", 3000)

        except Exception as e:
            self.logger.error(f"Error reloading Excel data: {e}")
        finally:
            # Re-enable file watcher
            self.sync_in_progress = False

    def read_dropdown_values(self, sheet, dropdown_row, max_column):
        """Read dropdown values from Excel Data Validation and cell values"""
        dropdown_values = {}
        try:
            self.logger.info(f"Reading dropdowns from sheet '{sheet.title}', row {dropdown_row}, max_column {max_column}")

            # Use openpyxl only - xlwings disabled to prevent file corruption
            data_validation_dropdowns = self.read_data_validation_dropdowns(sheet)
            if data_validation_dropdowns:
                dropdown_values.update(data_validation_dropdowns)
                self.logger.info(f"Found {len(data_validation_dropdowns)} openpyxl data validation dropdowns")

            # Try to read from cell values (fallback)
            if not dropdown_values:
                # Get sheet configuration to find header row
                sheet_config = self.get_sheet_config(sheet.title)
                header_row = sheet_config["header_row"]

                for col in range(1, max_column + 1):
                    cell = sheet.cell(row=dropdown_row, column=col)
                    header_cell = sheet.cell(row=header_row, column=col)
                    header = header_cell.value if header_cell.value else f"Column {col}"

                    self.logger.info(f"Column {col}: Header='{header}', Dropdown cell value='{cell.value}'")

                    if cell.value:
                        # Parse dropdown values (supporting multiple formats)
                        dropdown_text = str(cell.value).strip()
                        if dropdown_text:
                            values = self.parse_dropdown_text(dropdown_text)
                            if values:
                                dropdown_values[header] = values
                                self.logger.info(f"✅ Found cell dropdown for '{header}' (row {dropdown_row}): {values}")
                            else:
                                self.logger.warning(f"❌ No values parsed from '{dropdown_text}' for column '{header}'")

            # Add hardcoded dropdowns for common columns
            self.add_hardcoded_dropdowns(dropdown_values, sheet.title)

            # If still no dropdowns found, add them based on actual column headers
            if not dropdown_values:
                self.logger.warning("No dropdown values found from Excel or hardcoded - adding based on headers")
                self.add_dropdowns_by_header_analysis(dropdown_values, sheet, dropdown_row, max_column)

            # Debug: Log all final dropdown values
            self.logger.info(f"Final dropdown values for sheet '{sheet.title}': {list(dropdown_values.keys())}")
            for header, values in dropdown_values.items():
                self.logger.info(f"  '{header}': {values[:3]}{'...' if len(values) > 3 else ''}")

            return dropdown_values
        except Exception as e:
            self.logger.error(f"Error reading dropdown values: {e}")
            return {}

    def read_data_validation_dropdowns(self, sheet):
        """Read dropdown values from Excel Data Validation rules"""
        dropdown_values = {}
        try:
            # Get sheet configuration to determine header row
            sheet_name = sheet.title
            sheet_config = self.get_sheet_config(sheet_name)
            header_row = sheet_config["header_row"]

            # Check if the sheet has data validation
            if not hasattr(sheet, 'data_validations') or not sheet.data_validations:
                self.logger.info(f"No data validation found in sheet '{sheet_name}'")
                return dropdown_values

            self.logger.info(f"Found {len(sheet.data_validations)} data validation rules in sheet '{sheet_name}'")

            # Iterate through all data validation rules
            for dv in sheet.data_validations:
                if dv.type == "list" and dv.formula1:
                    # Get the range of cells this validation applies to
                    for cell_range in dv.cells:
                        for cell in cell_range:
                            # Get the column letter and convert to number
                            col_letter = cell.column_letter
                            col_num = cell.column

                            # Get the header for this column
                            header_cell = sheet.cell(row=header_row, column=col_num)
                            header = header_cell.value if header_cell.value else f"Column {col_num}"

                            # Parse the formula to get dropdown values
                            formula = dv.formula1
                            values = self.parse_data_validation_formula(formula, sheet)

                            if values:
                                dropdown_values[header] = values
                                self.logger.info(f"Found data validation dropdown for '{header}' (col {col_letter}): {values}")

            return dropdown_values

        except Exception as e:
            self.logger.error(f"Error reading data validation dropdowns: {e}")
            return {}

    def parse_data_validation_formula(self, formula, sheet):
        """Parse Excel data validation formula to extract dropdown values"""
        try:
            values = []

            # Remove quotes and whitespace
            formula = formula.strip('"\'')

            # Handle different formula types
            if formula.startswith('$') or '!' in formula:
                # Reference to another range (e.g., $A$1:$A$10 or Sheet1!$A$1:$A$10)
                self.logger.info(f"Data validation references range: {formula}")
                # For now, we'll skip range references as they're complex to parse
                # In a full implementation, you'd need to resolve the range and read those cells
                return []
            elif ',' in formula:
                # Comma-separated list (e.g., "Option1,Option2,Option3")
                values = [v.strip() for v in formula.split(',') if v.strip()]
            elif ';' in formula:
                # Semicolon-separated list
                values = [v.strip() for v in formula.split(';') if v.strip()]
            else:
                # Single value or other format
                if formula:
                    values = [formula]

            # Clean up values
            cleaned_values = []
            for value in values:
                cleaned_value = value.strip('"\'')
                if cleaned_value:
                    cleaned_values.append(cleaned_value)

            return cleaned_values

        except Exception as e:
            self.logger.error(f"Error parsing data validation formula '{formula}': {e}")
            return []

    def add_hardcoded_dropdowns(self, dropdown_values, sheet_name):
        """Add hardcoded dropdown values for common columns"""
        try:
            self.logger.info(f"Adding hardcoded dropdowns for sheet '{sheet_name}'")
            self.logger.info(f"Current dropdown keys: {list(dropdown_values.keys())}")

            # Common dropdown values for Timeline sheets
            if "Timeline" in sheet_name:
                # Artifact dropdown - ALWAYS add for Timeline sheets
                # First, check if we already have an artifact dropdown
                artifact_keys = [key for key in dropdown_values.keys() if "artifact" in key.lower()]
                if not artifact_keys:
                    # Add hardcoded artifact dropdown - use exact column name "Artifact"
                    dropdown_values["Artifact"] = [
                        "AmCache", "Browser History", "Event Logs", "File System", "Memory Dump",
                        "Network Logs", "Prefetch", "Registry", "System Logs", "User Activity"
                    ]
                    self.logger.info(f"Added hardcoded Artifact dropdown for Timeline sheet")
                else:
                    # If we found an artifact key but it has limited values, enhance it
                    for key in artifact_keys:
                        if len(dropdown_values[key]) < 5:  # If less than 5 values, it's probably incomplete
                            dropdown_values[key] = [
                                "AmCache", "Browser History", "Event Logs", "File System", "Memory Dump",
                                "Network Logs", "Prefetch", "Registry", "System Logs", "User Activity"
                            ]
                            self.logger.info(f"Enhanced Artifact dropdown for '{key}' with full list")

                # MITRE Technique dropdown (sample)
                mitre_keys = [key for key in dropdown_values.keys() if "mitre" in key.lower() or "technique" in key.lower()]
                if not mitre_keys:
                    possible_mitre_names = ["Mitre Technique", "MITRE Technique", "Technique", "Attack Technique"]
                    for possible_name in possible_mitre_names:
                        if possible_name not in dropdown_values:
                            dropdown_values[possible_name] = [
                                "T1003 - OS Credential Dumping", "T1005 - Data from Local System",
                                "T1012 - Query Registry", "T1016 - System Network Configuration Discovery",
                                "T1018 - Remote System Discovery", "T1021 - Remote Services",
                                "T1027 - Obfuscated Files or Information", "T1033 - System Owner/User Discovery",
                                "T1036 - Masquerading", "T1040 - Network Sniffing"
                            ]
                            self.logger.info(f"Added hardcoded MITRE dropdown as '{possible_name}'")
                            break

                # Account Name dropdown (sample)
                if "Account Name" not in dropdown_values:
                    dropdown_values["Account Name"] = [
                        "Administrator", "Guest", "System", "Local Service", "Network Service"
                    ]

            # Common dropdown values for Evidence Management
            if "Evidence Management" in sheet_name:
                # Collection Type dropdown
                if "Collection Type" not in dropdown_values:
                    dropdown_values["Collection Type"] = [
                        "Full Image", "Triage", "Memory Dump", "Network Capture", "Log Files"
                    ]

                # Operating System dropdown
                if "Operating System" not in dropdown_values:
                    dropdown_values["Operating System"] = [
                        "Windows 10", "Windows 11", "Windows Server 2019", "Windows Server 2022",
                        "Linux", "macOS", "Android", "iOS"
                    ]

                # System Category dropdown
                if "System Category" not in dropdown_values:
                    dropdown_values["System Category"] = [
                        "Entrypoint/Patient 0", "Lateral Movement", "Data Exfiltration",
                        "Command & Control", "Persistence", "Defense Evasion"
                    ]

            # Universal dropdowns
            if "Visualize" not in dropdown_values:
                dropdown_values["Visualize"] = ["Yes", "No"]

            self.logger.info(f"Added hardcoded dropdowns for {sheet_name}: {len(dropdown_values)} total columns")

        except Exception as e:
            self.logger.error(f"Error adding hardcoded dropdowns: {e}")

    def add_dropdowns_by_header_analysis(self, dropdown_values, sheet, dropdown_row, max_column):
        """Add dropdowns by analyzing actual column headers in the Excel file"""
        try:
            sheet_config = self.get_sheet_config(sheet.title)
            header_row = sheet_config["header_row"]

            for col in range(1, max_column + 1):
                header_cell = sheet.cell(row=header_row, column=col)
                if header_cell.value:
                    header = str(header_cell.value).strip()

                    # Match common patterns and add appropriate dropdowns
                    header_lower = header.lower()

                    if "artifact" in header_lower or "evidence" in header_lower or "source" in header_lower:
                        dropdown_values[header] = [
                            "AmCache", "Browser History", "Event Logs", "File System", "Memory Dump",
                            "Network Logs", "Prefetch", "Registry", "System Logs", "User Activity"
                        ]
                        self.logger.info(f"Added Artifact dropdown for header '{header}'")

                    elif "mitre" in header_lower or "technique" in header_lower or "attack" in header_lower:
                        dropdown_values[header] = [
                            "T1003 - OS Credential Dumping", "T1005 - Data from Local System",
                            "T1012 - Query Registry", "T1016 - System Network Configuration Discovery",
                            "T1018 - Remote System Discovery", "T1021 - Remote Services"
                        ]
                        self.logger.info(f"Added MITRE dropdown for header '{header}'")

                    elif "visualize" in header_lower or "chart" in header_lower or "graph" in header_lower:
                        dropdown_values[header] = ["Yes", "No"]
                        self.logger.info(f"Added Yes/No dropdown for header '{header}'")

                    elif "system" in header_lower and ("name" in header_lower or "host" in header_lower):
                        dropdown_values[header] = ["DC01", "WS001", "SRV001", "LAPTOP01", "Unknown"]
                        self.logger.info(f"Added System dropdown for header '{header}'")

        except Exception as e:
            self.logger.error(f"Error analyzing headers for dropdowns: {e}")

    def parse_dropdown_text(self, dropdown_text):
        """Parse dropdown text supporting various formats"""
        try:
            # Remove any surrounding quotes
            dropdown_text = dropdown_text.strip('"\'')

            # Support different separators
            separators = [',', ';', '|', '\n']
            values = []

            for separator in separators:
                if separator in dropdown_text:
                    values = [v.strip() for v in dropdown_text.split(separator) if v.strip()]
                    break

            # If no separator found, treat as single value
            if not values and dropdown_text:
                values = [dropdown_text]

            # Clean up values and remove duplicates while preserving order
            cleaned_values = []
            seen = set()
            for value in values:
                cleaned_value = value.strip()
                if cleaned_value and cleaned_value not in seen:
                    cleaned_values.append(cleaned_value)
                    seen.add(cleaned_value)

            return cleaned_values
        except Exception as e:
            self.logger.error(f"Error parsing dropdown text '{dropdown_text}': {e}")
            return []

    def get_dropdown_values_for_column(self, column_name):
        """Get dropdown values for a specific column"""
        if hasattr(self, 'current_dropdown_values') and self.current_dropdown_values:
            return self.current_dropdown_values.get(column_name, [])
        return []

    # xlwings functions removed - using openpyxl only for better file stability

    def is_file_locked(self, file_path):
        """Check if a file is locked/open by another process"""
        try:
            # Try to open the file in write mode
            with open(file_path, 'r+b'):
                return False
        except (IOError, OSError, PermissionError):
            return True

    def acquire_file_lock(self, file_path):
        """Acquire exclusive lock for KANVAS-to-KANVAS coordination"""
        try:
            lock_file = f"{file_path}.kanvas.lock"
            self.file_lock = FileLock(lock_file, timeout=self.lock_timeout)
            self.file_lock.acquire()
            self.logger.info(f"Acquired file lock: {lock_file}")
            return True
        except Exception as e:
            self.logger.warning(f"Could not acquire file lock: {e}")
            return False

    def release_file_lock(self):
        """Release file lock"""
        try:
            if self.file_lock:
                self.file_lock.release()
                self.logger.info("Released file lock")
                self.file_lock = None
        except Exception as e:
            self.logger.error(f"Error releasing file lock: {e}")

    def add_sync_controls(self):
        """Add sync controls to the UI for multi-user collaboration"""
        try:
            # Add sync status and controls to the status bar or toolbar
            if hasattr(self.window, 'statusBar'):
                status_bar = self.window.statusBar()

                # Sync status label
                self.sync_status_label = QLabel("🔄 Auto-sync: ON")
                status_bar.addPermanentWidget(self.sync_status_label)

                # Manual sync button
                sync_button = QPushButton("🔄 Sync Now")
                sync_button.setToolTip("Manually check for external changes and reload")
                sync_button.clicked.connect(self.manual_sync)
                status_bar.addPermanentWidget(sync_button)

                # Toggle auto-sync button
                toggle_sync_button = QPushButton("⏸️ Pause Auto-sync")
                toggle_sync_button.setToolTip("Toggle automatic synchronization")
                toggle_sync_button.clicked.connect(self.toggle_auto_sync)
                status_bar.addPermanentWidget(toggle_sync_button)
                self.toggle_sync_button = toggle_sync_button

        except Exception as e:
            self.logger.error(f"Error adding sync controls: {e}")

    def manual_sync(self):
        """Manually trigger sync check"""
        self.logger.info("Manual sync triggered")
        self.check_for_external_changes()

    def toggle_auto_sync(self):
        """Toggle automatic synchronization on/off"""
        self.auto_sync_enabled = not self.auto_sync_enabled

        if self.auto_sync_enabled:
            self.sync_status_label.setText("🔄 Auto-sync: ON")
            self.toggle_sync_button.setText("⏸️ Pause Auto-sync")
            self.sync_timer.start(2000)
        else:
            self.sync_status_label.setText("⏸️ Auto-sync: OFF")
            self.toggle_sync_button.setText("▶️ Resume Auto-sync")
            self.sync_timer.stop()

        self.logger.info(f"Auto-sync {'enabled' if self.auto_sync_enabled else 'disabled'}")

    def finish_loading(self):
        self.window.showMaximized()
        self.splash.finish(self.window)
    
    def get_lock_path(self, excel_path):
        return f"{excel_path}.lock"

    def acquire_file_lock(self, file_path):
        try:
            if self.file_lock:
                self.release_file_lock()  
            lock_path = self.get_lock_path(file_path)
            self.file_lock = FileLock(lock_path, timeout=1) 
            try:
                self.file_lock.acquire(timeout=1)
                self.read_only_mode = False
                return True
            except Timeout:
                self.logger.info(f"File {file_path} is locked by another process")
                result = QMessageBox.question( self.window, "File is in use", f"The file is currently being edited by another user.\nDo you want to open it in read-only mode?", QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
                if result == QMessageBox.Yes:
                    self.read_only_mode = True
                    return False
                else:
                    return None  
        except Exception as e:
            self.logger.error(f"Error acquiring lock: {e}")
            self.file_lock = None
            return None  
    
    def release_file_lock(self):
        if self.file_lock:
            try:
                if hasattr(self.file_lock, 'is_locked') and self.file_lock.is_locked:
                    self.file_lock.release()
                    self.logger.info("File lock released")
            except Exception as e:
                self.logger.error(f"Error releasing file lock: {e}")
            finally:
                self.file_lock = None

    def closeEvent(self, event):
        self.release_file_lock()
        for window in self.child_windows:
            if window and hasattr(window, 'setEnabled'):
                window.setEnabled(False)
        windows_to_close = self.child_windows.copy()  
        for i, window in enumerate(windows_to_close):
            try:
                if window and hasattr(window, 'close'):
                    #print(f"Closing child window {i+1}/{len(windows_to_close)}: {window.windowTitle() if hasattr(window, 'windowTitle') else 'Unknown'}")
                    window.close()
                    QApplication.processEvents()  
            except Exception as e:
                self.logger.error(f"Error during first close attempt: {e}")
        remaining = [w for w in self.child_windows if hasattr(w, 'isVisible') and w.isVisible()]
        if remaining:
            self.logger.info(f"Found {len(remaining)} windows still open, forcing deletion...")
            for window in remaining:
                try:
                    self.logger.info(f"Force closing: {window.windowTitle() if hasattr(window, 'windowTitle') else 'Unknown'}")
                    window.setAttribute(Qt.WA_DeleteOnClose, True) 
                    window.setParent(None)  
                    window.close()
                    window.deleteLater()
                    QApplication.processEvents() 
                except Exception as e:
                    self.logger.error(f"Error during forced close: {e}")
        self.child_windows.clear()
        event.accept()

    def load_ui(self):
        window = QMainWindow()
        ui = Ui_KanvasMainWindow()
        ui.setupUi(window)
        window.ui = ui
        return window
    
    def connect_ui_elements(self):
        self.tree_view = self.window.ui.treeViewMain
        if self.tree_view:
            self.tree_view.doubleClicked.connect(self.edit_row)
        else:
            self.logger.warning("treeViewMain not found!")
        self.connect_button("left_button_2", self.handle_veris_window)
        self.connect_button("left_button_3", self.handle_defend_mapping)  
        self.connect_button("left_button_4", self.handle_mitre_mapping)
        self.connect_button("left_button_5", self.handle_visualize_network)
        self.connect_button("left_button_6", self.handle_timeline_window)
        self.connect_button("left_button_7", self.open_new_case_window)
        self.connect_button("left_button_8", self.load_data_into_treeview)
        self.connect_button("left_button_9", self.handle_ip_lookup)  
        self.connect_button("left_button_10", self.handle_ransomware_kb)
        self.connect_button("left_button_11", self.handle_domain_lookup) 
        self.connect_button("left_button_12", self.handle_hash_lookup) 
        self.connect_button("left_button_15", self.handle_markdown_editor)
        self.connect_button("left_button_16", self.display_bookmarks_kb)
        self.connect_button("left_button_18", self.open_api_settings)
        self.connect_button("left_button_19", self.handle_download_updates)
        self.connect_button("left_button_17", self.display_event_id_kb)
        self.connect_button("left_button_13", self.entra_appid)
        self.connect_button("left_button_14", self.handle_cve_lookup)
        self.connect_button("down_button_1", self.add_new_row)  
        self.connect_button("down_button_2", self.delete_row)   
        self.connect_button("down_button_3", self.load_sheet)   
        self.connect_button("down_button_4", self.list_systems)  
        self.connect_button("down_button_5", self.list_users)    
        self.connect_button("down_button_6", self.sanitize)     
        
        # Connect sheet dropdown
        # Note: Sheet dropdown connection is handled in load_data_into_treeview method
        # where load_selected_sheet is defined as a nested function
        sheet_dropdown = self.window.ui.comboBoxSheet
        if not sheet_dropdown:
            self.logger.warning("comboBoxSheet not found!")

    def connect_button(self, button_name, handler):
        button = getattr(self.window.ui, button_name, None)
        if button:
            button.clicked.connect(handler)
        else:
            self.logger.warning(f"{button_name} not found!")
            
    def check_excel_loaded(self):
        if not self.current_workbook or not self.current_file_path:
            QMessageBox.warning(self.window, "Warning", "No Excel file loaded. Please load a file first.")
            return False
        return True

    def handle_veris_window(self):
        if self.check_excel_loaded():
            window = open_veris_window(self.window)
            self.track_child_window(window)

    def handle_mitre_mapping(self):
        if self.check_excel_loaded():
            window = mitre_mapping(self.window)
            self.track_child_window(window)

    def handle_visualize_network(self):
        if self.check_excel_loaded():
            window = visualize_network(self.window)
            self.track_child_window(window)

    def handle_timeline_window(self):
        if self.check_excel_loaded():
            window = open_timeline_window(self.window)
            self.track_child_window(window)
            
    def handle_download_updates(self):
        window = download_updates(self.window)
        self.track_child_window(window)

    def open_custom_window(self):
        custom_window = QWidget(self.window) 
        custom_window.setWindowTitle("New Case Details")
        custom_window.setMinimumSize(600, 800)
        layout = QVBoxLayout()
        text_box = QTextEdit()
        layout.addWidget(text_box)
        submit_button = QPushButton("Submit")
        layout.addWidget(submit_button)

        def submit_data():
            data = text_box.toPlainText()
            custom_window.close()

        submit_button.clicked.connect(submit_data)
        custom_window.setLayout(layout)
        self.track_child_window(custom_window)
        custom_window.show()
    
    def open_api_settings(self):
        custom_window = QWidget(self.window)  
        custom_window.setWindowTitle("API Settings")
        custom_window.setMinimumSize(800, 400)
        custom_window.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        title_label = QLabel("API Keys Configuration")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        main_layout.addSpacing(10)
        
        current_settings = {}
        
        def mask_api_key(api_key):
            if not api_key or api_key.strip() == "":
                return ""
            if len(api_key) <= 8:
                return "****"
            return api_key[:4] + "*" * (len(api_key) - 4)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT VT_API_KEY, SHODEN_API_KEY, OTX_API_KEY, MISP_API_KEY, OpenCTI_API_KEY, IPQS_API_KEY FROM user_settings WHERE id = 1")
            row = cursor.fetchone()
            if row:
                field_names = ['VT_API_KEY', 'SHODEN_API_KEY', 'OTX_API_KEY', 'MISP_API_KEY', 'OpenCTI_API_KEY', 'IPQS_API_KEY']
                for i, name in enumerate(field_names):
                    current_settings[name] = {
                        'original': row[i] if row[i] is not None else "",
                        'masked': mask_api_key(row[i] if row[i] is not None else "")
                    }
            else:
                self.logger.info("No settings found in database")
                for field in [
                    'VT_API_KEY', 'SHODEN_API_KEY', 'OTX_API_KEY', 'MISP_API_KEY',
                    'OpenCTI_API_KEY', 'IPQS_API_KEY'
                ]:
                    current_settings[field] = {'original': "", 'masked': ""}
        except sqlite3.Error as e:
            self.logger.error(f"Database error: {e}")
            QMessageBox.critical(custom_window, "Error", f"Failed to fetch settings: {e}")
            for field in [
                'VT_API_KEY', 'SHODEN_API_KEY', 'OTX_API_KEY', 'MISP_API_KEY',
                'OpenCTI_API_KEY', 'IPQS_API_KEY'
            ]:
                current_settings[field] = {'original': "", 'masked': ""}
        finally:
            if 'conn' in locals():
                conn.close()
        
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        scroll_layout.setColumnStretch(1, 1)  
        api_fields = [
            ("VirusTotal :", "VT_API_KEY"),
            ("Shodan.io :", "SHODEN_API_KEY"),
            ("AlienVault OTX :", "OTX_API_KEY"),
           # ("MISP :", "MISP_API_KEY"),
           # ("OpenCTI :", "OpenCTI_API_KEY"),
            ("IP Quality Score :", "IPQS_API_KEY")
        ]
        input_fields = {}
        show_buttons = {}
        for row, (label_text, field_name) in enumerate(api_fields):
            label = QLabel(label_text)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            scroll_layout.addWidget(label, row, 0)
            input_field = QLineEdit()
            input_field.setMinimumWidth(350)
            input_field.setEchoMode(QLineEdit.Password) 
            original_value = current_settings.get(field_name, {}).get('original', '')
            if original_value:
                input_field.setText(original_value)
                input_field.setPlaceholderText(current_settings[field_name]['masked'])
            scroll_layout.addWidget(input_field, row, 1)
            show_button = QPushButton("Show")
            show_button.setFixedWidth(60)
            show_button.setStyleSheet("padding: 4px 8px;")
            
            def create_toggle_function(field, button):
                def toggle_visibility():
                    if field.echoMode() == QLineEdit.Password:
                        field.setEchoMode(QLineEdit.Normal)
                        button.setText("Hide")
                    else:
                        field.setEchoMode(QLineEdit.Password)
                        button.setText("Show")
                return toggle_visibility
            
            show_button.clicked.connect(create_toggle_function(input_field, show_button))
            scroll_layout.addWidget(show_button, row, 2)
            input_fields[field_name] = input_field
            show_buttons[field_name] = show_button
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)
        #info_label = QLabel("Note: API keys are masked for security. Click 'Show' to reveal values when editing.")
        #info_label.setStyleSheet("color: #666; font-style: italic; margin: 10px 0;")
        #main_layout.addWidget(info_label)
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)
        update_button = QPushButton("Update")
        update_button.setStyleSheet("background-color: #4CAF50; color: white; padding: 6px 20px;")
        button_layout.addWidget(update_button)
        cancel_button = QPushButton("Cancel")
        cancel_button.setStyleSheet("padding: 6px 20px;")
        button_layout.addWidget(cancel_button)
        main_layout.addSpacing(15)
        main_layout.addLayout(button_layout)
        custom_window.setLayout(main_layout)

        def save_settings():
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                update_fields = []
                values = []
                for field_name, input_field in input_fields.items():
                    update_fields.append(f"{field_name} = ?")
                    api_key_value = input_field.text().strip()
                    values.append(api_key_value)
                query = f"UPDATE user_settings SET {', '.join(update_fields)} WHERE id = 1"
                cursor.execute(query, values)
                if cursor.rowcount == 0:
                    field_names = list(input_fields.keys())
                    placeholders = ", ".join(["?"] * len(field_names))
                    field_str = ", ".join(field_names)
                    query = f"INSERT INTO user_settings (id, {field_str}) VALUES (1, {placeholders})"
                    cursor.execute(query, values)
                conn.commit()
                QMessageBox.information(custom_window, "Success", "Settings saved successfully!")
                custom_window.close()
            except sqlite3.Error as e:
                self.logger.error(f"Error saving settings: {e}")
                QMessageBox.critical(custom_window, "Error", f"Failed to save settings: {e}")
            finally:
                if 'conn' in locals():
                    conn.close()
    
        update_button.clicked.connect(save_settings)
        cancel_button.clicked.connect(custom_window.close)
        self.child_windows.append(custom_window)
        custom_window.show()

    def open_new_case_window(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self.window, 
            "Save New Case File", 
            "New_Case.xlsm", 
            "Excel files (*.xlsx *.xlsm)"
        )
        if not file_path:
            self.logger.info("No file selected")
            return
        
        # Ensure file has proper extension
        if not file_path.lower().endswith(('.xlsx', '.xlsm')):
            file_path += '.xlsm'  # Default to .xlsm if no extension
        
        lock_acquired = False
        try:
            lock_status = self.acquire_file_lock(file_path)
            if lock_status is None:  
                return
            lock_acquired = True
            
            # Update template file to use your .xlsm template
            template_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
            if not os.path.exists(template_file):
                # Fall back to original template if your template isn't found
                template_file = "sod.xlsx"
                if not os.path.exists(template_file):
                    QMessageBox.critical(self.window, "Error", f"Template files not found. Please ensure either 'ABZ_CompanyName_IR_Project_Workbook_Template.xlsm' or 'sod.xlsx' exists in the application directory.")
                    return
                
            shutil.copy(template_file, file_path)
            QMessageBox.information(self.window, "Success", f"New case created successfully and saved to {file_path}!")
            workbook = openpyxl.load_workbook(file_path)
            self.current_file_path = file_path
            self.current_workbook = workbook
            self.read_only_mode = False  
            file_status_label = self.window.ui.labelFileStatus
            if file_status_label:
                file_name = os.path.basename(file_path)
                file_status_label.setText(f"Loaded: {file_name} ({file_path})")
            else:
                self.logger.warning("labelFileStatus not found!")
            if self.tree_view:
                self.load_data_into_treeview(file_path=file_path, workbook=workbook)
            else:
                self.logger.warning("treeViewMain not found!")
        except Exception as e:
            self.logger.error(f"Error creating new case: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to create new case: {e}")
        finally:
            if lock_acquired:
                self.release_file_lock()

    def load_data_into_treeview(self, file_path=None, workbook=None):
        lock_acquired = False
        progress = None
        try:
            # If no file_path provided, prompt user to select a file
            if not file_path:
                file_path, _ = QFileDialog.getOpenFileName(
                    self.window,
                    "Open Excel File",
                    "",
                    "Excel files (*.xlsx *.xlsm *.xls)"
                )
                if not file_path:
                    self.logger.info("No file selected")
                    return

            if file_path:
                if not workbook:
                    if not file_path:
                        self.logger.info("No file selected")
                        return
                    progress = QProgressBar()
                    progress.setWindowTitle("Loading Excel File")
                    progress.setGeometry(QRect(300, 300, 400, 30))
                    progress.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
                    progress.setRange(0, 0)  
                    progress.show()
                    progress.setFormat("Loading file...")
                    QApplication.processEvents()
                    try:
                        lock_status = self.acquire_file_lock(file_path)
                        if lock_status is None:  
                            if progress:
                                progress.close()
                            return
                        lock_acquired = True
                        progress.setFormat("Opening workbook...")
                        QApplication.processEvents()
                        workbook = openpyxl.load_workbook(file_path, read_only=self.read_only_mode)
                    except Exception as e:
                        if progress:
                            progress.close()
                        if lock_acquired:
                            self.release_file_lock()
                            lock_acquired = False
                        self.logger.error(f"Error loading workbook: {e}")
                        QMessageBox.critical(self.window, "Error", f"Failed to load Excel file: {e}")
                        return

                # Update workbook references in both MainApp and window objects
                self.current_workbook = workbook
                self.current_file_path = file_path
                self.current_sheet_name = None  # Reset sheet name when loading new file
                self.window.current_workbook = workbook
                self.window.current_file_path = file_path
                self.window.current_sheet_name = None  # Reset sheet name when loading new file

                self.logger.info(f"Updated workbook references. File: {file_path}")
                self.logger.info(f"Available sheets: {workbook.sheetnames}")

                # Setup file watcher for synchronization
                if progress:
                    progress.setFormat("Setting up file synchronization...")
                    QApplication.processEvents()
                self.setup_file_watcher(file_path)

                if progress:
                    progress.setFormat("Updating file status...")
                    QApplication.processEvents()
                file_status_label = self.window.ui.labelFileStatus
                if file_status_label:
                    file_name = os.path.basename(file_path)
                    read_only_text = " [READ-ONLY]" if self.read_only_mode else ""
                    file_status_label.setText(f"Loaded: {file_name} ({file_path}){read_only_text}")
                else:
                    self.logger.warning("labelFileStatus not found!")
                sheet_dropdown = self.window.ui.comboBoxSheet
                if not sheet_dropdown:
                    if progress:
                        progress.close()
                    self.logger.warning("Sheet dropdown (comboBoxSheet) not found!")
                    return
                if progress:
                    progress.setFormat("Loading sheet names...")
                    QApplication.processEvents()

                # Properly disconnect existing connections
                try:
                    # Check if there are any connections before trying to disconnect
                    if sheet_dropdown.receivers(sheet_dropdown.currentIndexChanged) > 0:
                        sheet_dropdown.currentIndexChanged.disconnect()
                        self.logger.info("Disconnected existing sheet dropdown connections")
                    else:
                        self.logger.info("No existing sheet dropdown connections to disconnect")
                except (TypeError, RuntimeError) as e:
                    # No connections to disconnect or other signal-related error
                    self.logger.info(f"No sheet dropdown connections to disconnect: {e}")

                # Clear and update sheet dropdown
                sheet_dropdown.clear()
                self.logger.info(f"Cleared sheet dropdown. Adding sheet names: {workbook.sheetnames}")
                sheet_dropdown.addItems(workbook.sheetnames)

                # Force UI update to ensure dropdown is refreshed
                QApplication.processEvents()
                self.logger.info(f"Sheet dropdown updated. Current count: {sheet_dropdown.count()}")
                
                # Set default sheet based on priority
                default_sheets = ["Timeline (Master)", "Timeline (Analyst)", "Timeline", "Evidence Management"]
                default_sheet_index = 0
                
                for sheet_name in default_sheets:
                    if sheet_name in workbook.sheetnames:
                        default_sheet_index = workbook.sheetnames.index(sheet_name)
                        break
                
                def load_selected_sheet():
                    selected_sheet_name = sheet_dropdown.currentText()
                    if not selected_sheet_name:
                        self.logger.info("No sheet selected")
                        return

                    self.logger.info(f"Loading sheet: {selected_sheet_name}")
                    sheet_progress = QProgressBar()
                    sheet_progress.setWindowTitle(f"Loading Sheet: {selected_sheet_name}")
                    sheet_progress.setGeometry(QRect(300, 300, 400, 30))
                    sheet_progress.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
                    sheet_progress.show()
                    try:
                        current_workbook = self.current_workbook
                        if not current_workbook:
                            sheet_progress.close()
                            self.logger.error("No current workbook available")
                            return

                        # Update current sheet name in both objects
                        self.current_sheet_name = selected_sheet_name
                        self.window.current_sheet_name = selected_sheet_name
                        self.logger.info(f"Updated current sheet name to: {selected_sheet_name}")

                        # Get sheet configuration
                        sheet_config = self.get_sheet_config(selected_sheet_name)
                        header_row = sheet_config["header_row"]
                        dropdown_row = sheet_config["dropdown_row"]
                        data_start_row = sheet_config["data_start_row"]

                        self.logger.info(f"Using config for '{selected_sheet_name}': headers={header_row}, dropdowns={dropdown_row}, data_start={data_start_row}")

                        sheet = current_workbook[selected_sheet_name]
                        max_row = sheet.max_row
                        sheet_progress.setRange(0, max_row + 2)
                        sheet_progress.setValue(0)
                        sheet_progress.setFormat("Reading headers...")
                        QApplication.processEvents()

                        model = QStandardItemModel()

                        # Read headers from the configured header row
                        headers = []
                        for col in range(1, sheet.max_column + 1):
                            cell = sheet.cell(row=header_row, column=col)
                            header_value = cell.value if cell.value else f"Column {col}"
                            headers.append(str(header_value))

                        model.setHorizontalHeaderLabels(headers)
                        self.logger.info(f"Read headers from row {header_row}: {headers}")

                        # Read dropdown values from the configured dropdown row
                        dropdown_values = self.read_dropdown_values(sheet, dropdown_row, sheet.max_column)
                        if dropdown_values:
                            self.logger.info(f"Found dropdown values: {dropdown_values}")
                            # Store dropdown values for later use
                            self.current_dropdown_values = dropdown_values
                            self.window.current_dropdown_values = dropdown_values

                        sheet_progress.setValue(1)
                        sheet_progress.setFormat("Loading data...")
                        QApplication.processEvents()

                        # Load data starting from the configured data start row
                        for row_index in range(data_start_row, max_row + 1):
                            row_data = []
                            for col in range(1, sheet.max_column + 1):
                                cell = sheet.cell(row=row_index, column=col)
                                cell_value = cell.value if cell.value is not None else ""
                                row_data.append(str(cell_value))

                            items = [QStandardItem(value) for value in row_data]
                            model.appendRow(items)

                            if row_index % 100 == 0 or row_index == max_row:
                                sheet_progress.setValue(row_index)
                                sheet_progress.setFormat(f"Loading row {row_index}/{max_row}...")
                                QApplication.processEvents()
                        sheet_progress.setFormat("Finalizing...")
                        QApplication.processEvents()
                        self.tree_view.setModel(model)

                        # Ensure headers are visible (like Excel spreadsheet)
                        self.tree_view.header().setVisible(True)
                        self.tree_view.header().setStretchLastSection(False)
                        self.tree_view.header().setSectionResizeMode(QHeaderView.Interactive)

                        # Enable editing, selection, and copy/paste
                        self.tree_view.setEditTriggers(QTreeView.DoubleClicked | QTreeView.EditKeyPressed)
                        self.tree_view.setSelectionMode(QTreeView.ExtendedSelection)
                        self.tree_view.setSelectionBehavior(QTreeView.SelectItems)

                        # Enable context menu for copy/paste
                        self.tree_view.setContextMenuPolicy(Qt.CustomContextMenu)
                        self.tree_view.customContextMenuRequested.connect(self.show_context_menu)

                        # Add keyboard shortcuts
                        self.setup_tree_view_shortcuts()
                        for col_idx, header in enumerate(headers):
                            self.tree_view.setColumnWidth(col_idx, 100)
                        sheet_progress.setValue(max_row + 2)
                        sheet_progress.close()
                    except Exception as e:
                        sheet_progress.close()
                        self.logger.error(f"Error loading sheet '{selected_sheet_name}': {e}")
                        self.tree_view.setModel(QStandardItemModel())
                sheet_dropdown.currentIndexChanged.connect(load_selected_sheet)
                if progress:
                    progress.setFormat("Initializing sheet view...")
                    QApplication.processEvents()
                sheet_dropdown.setCurrentIndex(default_sheet_index)
                load_selected_sheet()
                if progress:
                    progress.close()
        except Exception as e:
            if progress:
                progress.close()
            self.logger.error(f"Error in load_data_into_treeview: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to load data into tree view: {e}")
            if lock_acquired and not workbook:
                self.release_file_lock()
                
    def get_evidence_types_from_db(self):
        evidence_types = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT evidencetype FROM EvidenceType ORDER BY evidencetype")
            evidence_types = [row[0] for row in cursor.fetchall()]
            conn.close()
            if not evidence_types:
                evidence_types = []
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching evidence types from database: {e}")
            evidence_types = []
        return evidence_types

    def edit_row(self, index):
        if self.read_only_mode:
            QMessageBox.information(self.window, "Read-Only Mode", "This file is open in read-only mode. You cannot edit its contents.")
            return
        if not index.isValid():
            QMessageBox.warning(self.window, "Warning", "No row selected. Please select a row to edit.")
            return
        row_index = index.row()
        model = self.tree_view.model()
        if not model:
            QMessageBox.critical(self.window, "Error", "No model found for the tree view.")
            return
        mitre_tactic_options = []
        mitre_techniques_by_tactic = {}  
        current_tactic = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT PID FROM mitre_techniques")
            mitre_tactic_options = [row[0] for row in cursor.fetchall()]
            for tactic in mitre_tactic_options:
                cursor.execute("SELECT ID FROM mitre_techniques WHERE PID = ?", (tactic,))
                mitre_techniques_by_tactic[tactic] = [row[0] for row in cursor.fetchall()]
            conn.close()
        except sqlite3.Error as e:
            QMessageBox.critical(self.window, "Error", f"Failed to fetch MITRE values: {e}")
        evidence_types = self.get_evidence_types_from_db()
        editor_widget = QWidget(self.window)
        editor_widget.setWindowTitle("Edit Row - Widget Editor")
        editor_widget.setMinimumSize(500, 400)
        editor_widget.setWindowFlags(Qt.Window)  
        grid_layout = QGridLayout()
        grid_layout.setContentsMargins(15, 15, 15, 15)
        grid_layout.setHorizontalSpacing(20)
        grid_layout.setVerticalSpacing(10)
        input_fields = []
        mitre_tactic_combo = None
        mitre_technique_combo = None
        existing_tactic_value = None
        existing_technique_value = None
        for col in range(model.columnCount()):
            header_text = model.headerData(col, Qt.Horizontal)
            cell_data = model.index(row_index, col).data()
            header_label = QLabel(f"{header_text}:")
            header_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # Check if we have dropdown values for this column from the sheet
            dropdown_values = self.get_dropdown_values_for_column(header_text)

            if dropdown_values:
                # Use dropdown values from the sheet
                combo_box = QComboBox()
                combo_box.addItems(dropdown_values)
                combo_box.setEditable(True)  # Allow custom values
                if cell_data and str(cell_data) in dropdown_values:
                    combo_box.setCurrentText(str(cell_data))
                elif cell_data:
                    combo_box.setCurrentText(str(cell_data))  # Set custom value
                input_field = combo_box
                self.logger.info(f"Using sheet dropdown for '{header_text}': {dropdown_values}")
            elif header_text and header_text.strip().lower() == "visualize":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No"])
                if cell_data in ["Yes", "No"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "evidencetype":
                combo_box = QComboBox()
                combo_box.addItems(evidence_types)
                if cell_data in evidence_types:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "indicatortype":
                combo_box = QComboBox()
                combo_box.addItems(["IPAddress","UserName","FileName","FilePath","UserAgent","DomainName","JA3-JA3S","URL","Mutex","Other-Strings","EmailAddress","RegistryPath","GPO"])
                if cell_data in ["IPAddress","UserName","FileName","FilePath","UserAgent","DomainName","JA3-JA3S","URL","Mutex","Other-Strings","EmailAddress","RegistryPath","GPO"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "location":
                combo_box = QComboBox()
                combo_box.addItems(["On-Prem", "Unknown", "Cloud-Generic", "Cloud-Azure", "Cloud-AWS", "Clous-GCP"])
                if cell_data in ["On-Prem", "Unknown", "Cloud-Generic", "Cloud-Azure", "Cloud-AWS", "Clous-GCP"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "currentstatus":
                combo_box = QComboBox()
                combo_box.addItems(["Completed" , "In Progress", "On Hold", "Not Started"  ])
                if cell_data in ["Completed" , "In Progress", "On Hold", "Not Started"  ]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "priority":
                combo_box = QComboBox()
                combo_box.addItems(["High" ,"Medium" ,"Low" ])
                if cell_data in ["High" ,"Medium" ,"Low" ]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box                
            elif header_text and header_text.strip().lower() == "evidencecollected":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No" ])
                if cell_data in ["Yes", "No" ]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box                
            elif header_text and header_text.strip().lower() == "targettype":
                combo_box = QComboBox()
                combo_box.addItems(["Machine", "Identity", "Others"])
                if cell_data in ["Machine", "Identity", "Others"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box               
            elif header_text and header_text.strip().lower() == "systemtype":
                combo_box = QComboBox()
                combo_box.addItems(["Attacker-Machine","Server-Generic","Server-Application","Server-Web","Server-DC","Server-Terminal SRV","Server-Database","Gateway-Generic", "Gateway-Firewall","Gateway-VPN","Gateway-Router","Gateway-Switch","Gateway-Email","Gateway-Web Proxy","Gateway-DNS","Desktop","Mobile","OT Device","UnKnown"])
                if cell_data in ["Attacker-Machine","Server-Generic","Server-Application","Server-Web","Server-DC","Server-Terminal SRV","Server-Database","Gateway-Generic", "Gateway-Firewall","Gateway-VPN","Gateway-Router","Gateway-Switch","Gateway-Email","Gateway-Web Proxy","Gateway-DNS","Desktop","Mobile","OT Device","UnKnown"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "accounttype":
                combo_box = QComboBox()
                combo_box.addItems(["Normal User Account - Local","Normal User Account - On-Prem AD","Normal User Account - Azure","Service Account", "Domain Admin", "Global Admin - Azure", "Service Principle - Azure", "Computer Account", "Local Administrator"])
                if cell_data in ["Normal User Account - Local","Normal User Account - On-Prem AD","Normal User Account - Azure","Service Account", "Domain Admin", "Global Admin - Azure", "Service Principle - Azure", "Computer Account", "Local Administrator"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text.strip().lower() == "entrypoint":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No" ])
                if cell_data in ["Yes", "No" ]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box               
            elif header_text and (header_text.strip().lower() == "notes" or header_text.strip().lower() == "activity"):
                text_edit = QTextEdit()
                text_edit.setPlainText(cell_data if cell_data else "")
                input_field = text_edit
            elif header_text and header_text.strip().lower() in ["date added", "date updated", "date completed", "date requested", "date received"]:
                date_edit = QDateEdit()
                date_edit.setCalendarPopup(True)  
                if cell_data:
                    try:
                        date_edit.setDate(QDate.fromString(cell_data, "yyyy-MM-dd"))  
                    except Exception as e:
                        self.logger.error(f"Error parsing date: {e}")
                input_field = date_edit
            elif header_text and header_text == "TLP":
                combo_box = QComboBox()
                combo_box.addItems(["TLP-Red", "TLP-Amber_Strict", "TLP-Amber", "TLP-Green", "TLP-Clear"])
                if cell_data in ["TLP-Red", "TLP-Amber_Strict", "TLP-Amber", "TLP-Green", "TLP-Clear"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            elif header_text and header_text == "MITRE Tactic":
                existing_tactic_value = cell_data if cell_data else ""
                combo_box = QComboBox()
                combo_box.addItem("")  
                combo_box.addItems(mitre_tactic_options)
                if existing_tactic_value and existing_tactic_value in mitre_tactic_options:
                    combo_box.setCurrentText(existing_tactic_value)
                mitre_tactic_combo = combo_box
                input_field = combo_box
            elif header_text and header_text == "MITRE Techniques":
                existing_technique_value = cell_data if cell_data else ""
                combo_box = QComboBox()
                combo_box.addItem("")
                mitre_technique_combo = combo_box
                input_field = combo_box
            elif header_text and header_text == "<->":
                combo_box = QComboBox()
                combo_box.addItems([" ","->", "<-", "<->"])
                if cell_data in [" ","->", "<-", "<->"]:
                    combo_box.setCurrentText(cell_data)
                input_field = combo_box
            else:
                line_edit = QLineEdit()
                line_edit.setText(cell_data if cell_data else "")
                line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
                input_field = line_edit
            grid_layout.addWidget(header_label, col, 0)
            grid_layout.addWidget(input_field, col, 1)
            input_fields.append(input_field)
        if mitre_tactic_combo and mitre_technique_combo:
            current_tactic = mitre_tactic_combo.currentText()
            if current_tactic and current_tactic in mitre_techniques_by_tactic:
                available_techniques = mitre_techniques_by_tactic[current_tactic]
                mitre_technique_combo.addItems(available_techniques)
                if existing_technique_value:
                    if existing_technique_value in available_techniques:
                        mitre_technique_combo.setCurrentText(existing_technique_value)
                    else:
                        mitre_technique_combo.addItem(existing_technique_value)
                        mitre_technique_combo.setCurrentText(existing_technique_value)

            def update_techniques(index):
                selected_tactic = mitre_tactic_combo.currentText()
                current_technique = mitre_technique_combo.currentText()
                mitre_technique_combo.clear()
                mitre_technique_combo.addItem("")
                if selected_tactic and selected_tactic in mitre_techniques_by_tactic:
                    techniques = mitre_techniques_by_tactic[selected_tactic]
                    mitre_technique_combo.addItems(techniques)
                    if current_technique and current_technique in techniques:
                        mitre_technique_combo.setCurrentText(current_technique)
            mitre_tactic_combo.currentIndexChanged.connect(update_techniques)
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        save_button = QPushButton("Save")
        cancel_button = QPushButton("Cancel")
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        button_layout.addStretch()
        main_layout = QVBoxLayout(editor_widget)
        main_layout.addLayout(grid_layout)
        main_layout.addLayout(button_layout)
        editor_widget.setLayout(main_layout)

        def save_changes():
            try:
                if not self.current_workbook or not self.current_sheet_name:
                    QMessageBox.critical(self.window, "Error", "No workbook or sheet loaded to save changes.")
                    return
                workbook = self.current_workbook
                sheet = workbook[self.current_sheet_name]
                for col, field in enumerate(input_fields):
                    if isinstance(field, QComboBox):
                        new_text = field.currentText()
                    elif isinstance(field, QTextEdit):
                        new_text = field.toPlainText()
                    elif isinstance(field, QDateEdit):
                        new_text = field.date().toString("yyyy-MM-dd")  
                    else:
                        new_text = field.text()
                    sheet.cell(row=row_index + 2, column=col + 1, value=new_text)
                    model.setData(model.index(row_index, col), new_text)
                # Save to Excel file with proper error handling
                try:
                    # Temporarily disable file watcher to prevent recursive updates
                    self.sync_in_progress = True
                    lock_acquired = False

                    try:
                        # Acquire KANVAS file lock to prevent other KANVAS instances from saving
                        if not self.acquire_file_lock(self.current_file_path):
                            QMessageBox.warning(self.window, "File Busy",
                                "Another KANVAS user is currently saving to this file.\n"
                                "Please try again in a moment.")
                            return
                        lock_acquired = True

                        # Check for external changes before saving (conflict detection)
                        current_file_time = os.path.getmtime(self.current_file_path)
                        if self.last_modified_time and current_file_time > self.last_modified_time:
                            reply = QMessageBox.question(self.window, "File Conflict Detected",
                                "⚠️ The Excel file has been modified by another user since you last loaded it.\n\n"
                                "Your options:\n"
                                "• YES: Save anyway (may overwrite other user's changes)\n"
                                "• NO: Cancel and reload to see latest changes first\n\n"
                                "Do you want to save anyway?",
                                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

                            if reply == QMessageBox.No:
                                QMessageBox.information(self.window, "Save Cancelled",
                                    "Save cancelled. Please reload the file to see latest changes.")
                                return

                        # Check if file is locked/open in Excel
                        if self.is_file_locked(self.current_file_path):
                            QMessageBox.warning(self.window, "File Locked",
                                "The Excel file appears to be open in Excel or another application.\n"
                                "Please close the file in Excel and try again.")
                            return

                        # Save to Excel
                        workbook.save(self.current_file_path)

                    finally:
                        # Always release the lock
                        if lock_acquired:
                            self.release_file_lock()

                    # Update the last modified time
                    self.last_modified_time = os.path.getmtime(self.current_file_path)

                    self.logger.info(f"Edited row and saved to Excel file")
                    QMessageBox.information(self.window, "Edit Complete", "Row edited and saved to Excel successfully!")

                except PermissionError as e:
                    QMessageBox.critical(self.window, "Permission Error",
                        f"Cannot save to Excel file. The file may be:\n"
                        f"• Open in Excel\n"
                        f"• Read-only\n"
                        f"• Locked by another process\n\n"
                        f"Error: {e}")
                except Exception as e:
                    self.logger.error(f"Error saving edited row to Excel: {e}")
                    QMessageBox.critical(self.window, "Error", f"Failed to save changes to Excel: {e}")
                finally:
                    # Re-enable file watcher
                    self.sync_in_progress = False
                editor_widget.close()
            except Exception as e:
                self.logger.error(f"Error in edit_row save_changes: {e}")
                QMessageBox.critical(self.window, "Error", f"Failed to save changes: {e}")
        save_button.clicked.connect(save_changes)
        cancel_button.clicked.connect(editor_widget.close)
        editor_widget.show()
        self.track_child_window(editor_widget)

    def display_bookmarks_kb(self):
        window = display_bookmarks_kb(self, self.db_path)
        self.track_child_window(window)

    def display_event_id_kb(self):
        try:
            window = display_event_id_kb(self, self.db_path)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error in display_event_id_kb: {e}")
            traceback.print_exc()
            QMessageBox.critical(self.window, "Error", f"Failed to open Event ID Knowledge Base: {str(e)}")

    def entra_appid(self):
        window = open_entra_lookup_window(self.window, self.db_path)
        self.track_child_window(window)

    def handle_cve_lookup(self):
        try:
            window = open_cve_window(self.window, self.db_path)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening CVE lookup window: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open CVE lookup window: {e}")

    def handle_ransomware_kb(self):
        try:
            window = open_ransomware_kb_window(self.window)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening Ransomware Knowledge Base window: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open Ransomware Knowledge Base window: {e}")

    def handle_defend_mapping(self):
        if self.check_excel_loaded():
            try:
                if 'Timeline' in self.current_workbook.sheetnames:
                    window = open_defend_window(self.window, self.current_file_path)
                    self.track_child_window(window)
                else:
                    QMessageBox.warning(self.window, "Missing Sheet", "The required 'Timeline' sheet was not found in this workbook.")
            except Exception as e:
                self.logger.error(f"Error in D3FEND mapping: {e}")
                QMessageBox.critical(self.window, "D3FEND Mapping Error", f"An error occurred while opening the D3FEND mapping window:\n\n{str(e)}")

    def handle_ip_lookup(self):
        try:
            window = open_ip_lookup_window(self.window, self.db_path)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening IP lookup window: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open IP lookup window: {e}")
            
    def handle_domain_lookup(self):
        try:
            window = open_domain_lookup_window(self.window, self.db_path)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening Domain lookup window: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open Domain lookup window: {e}")

    def handle_hash_lookup(self):
        try:
            window = open_hash_lookup_window(self.window, self.db_path)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening Hash lookup window: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open Hash lookup window: {e}")

    def handle_markdown_editor(self):
        try:
            window = handle_markdown_editor(self.window)
            self.track_child_window(window)
        except Exception as e:
            self.logger.error(f"Error opening Markdown Editor interface: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to open Markdown Editor interface: {e}")

    def add_new_row(self):
        if self.read_only_mode:
            QMessageBox.information(self.window, "Read-Only Mode", "This file is open in read-only mode. You cannot add new rows.")
            return
        if not self.current_sheet_name or not self.current_workbook:
            QMessageBox.warning(self.window, "Warning", "Please select a sheet first.")
            return
        mitre_tactic_options = []
        mitre_techniques_by_tactic = {}  
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT PID FROM mitre_techniques")
            mitre_tactic_options = [row[0] for row in cursor.fetchall()]
            for tactic in mitre_tactic_options:
                cursor.execute("SELECT ID FROM mitre_techniques WHERE PID = ?", (tactic,))
                mitre_techniques_by_tactic[tactic] = [row[0] for row in cursor.fetchall()]
            conn.close()
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching MITRE values: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to fetch MITRE values: {e}")
            return
        evidence_types = self.get_evidence_types_from_db()
        sheet = self.current_workbook[self.current_sheet_name]

        # Get sheet configuration and read headers from correct row
        sheet_config = self.get_sheet_config(self.current_sheet_name)
        header_row = sheet_config["header_row"]
        dropdown_row = sheet_config["dropdown_row"]

        self.logger.info(f"Add new row for '{self.current_sheet_name}': using header row {header_row}, dropdown row {dropdown_row}")

        # Read headers from the configured header row
        headers = []
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=header_row, column=col)
            header_value = cell.value if cell.value else f"Column {col}"
            headers.append(str(header_value))

        self.logger.info(f"Headers for add new row: {headers}")

        # Read dropdown values from the configured dropdown row
        dropdown_values = self.read_dropdown_values(sheet, dropdown_row, sheet.max_column)
        if dropdown_values:
            self.logger.info(f"Dropdown values for add new row: {dropdown_values}")

        add_row_window = QWidget(self.window)
        add_row_window.setWindowTitle("Add New Row - Widget Editor")
        add_row_window.setMinimumSize(500, 400)
        add_row_window.setWindowFlags(Qt.Window)  
        main_layout = QVBoxLayout(add_row_window)
        grid_layout = QGridLayout()
        grid_layout.setContentsMargins(15, 15, 15, 15)
        grid_layout.setHorizontalSpacing(20)
        grid_layout.setVerticalSpacing(10)
        input_fields = []
        mitre_tactic_combo = None
        mitre_technique_combo = None
        current_tactic = None
        for col, header in enumerate(headers):
            header_label = QLabel(f"{header}:")
            header_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            grid_layout.addWidget(header_label, col, 0)

            # Check if we have dropdown values for this column from the sheet
            column_dropdown_values = dropdown_values.get(header, []) if dropdown_values else []

            self.logger.info(f"Checking dropdown for header '{header}': found {len(column_dropdown_values)} values")

            if column_dropdown_values:
                # Use dropdown values from the sheet
                combo_box = QComboBox()
                combo_box.addItems(column_dropdown_values)
                combo_box.setEditable(True)  # Allow custom values
                input_field = combo_box
                self.logger.info(f"✅ Using sheet dropdown for '{header}': {column_dropdown_values}")
            elif header and header.strip().lower() == "visualize":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No"])
                input_field = combo_box
            elif header and header.strip().lower() == "evidencetype":
                combo_box = QComboBox()
                combo_box.addItems(evidence_types)
                input_field = combo_box
            elif header and header.strip().lower() == "indicatortype":
                combo_box = QComboBox()
                combo_box.addItems(["IPAddress","UserName","FileName","FilePath","UserAgent","DomainName","JA3-JA3S","URL","Mutex","Other-Strings","EmailAddress","RegistryPath","GPO"])
                input_field = combo_box
            elif header and header.strip().lower() == "accounttype":
                combo_box = QComboBox()
                combo_box.addItems(["Normal User Account - Local","Normal User Account - On-Prem AD","Normal User Account - Azure","Service Account", "Domain Admin", "Global Admin - Azure", "Service Principle - Azure", "Computer Account", "Local Administrator"])
                input_field = combo_box
            elif header and header.strip().lower() == "systemtype":
                combo_box = QComboBox()
                combo_box.addItems(["Attacker-Machine","Server-Generic","Server-Application","Server-Web","Server-DC","Server-Terminal SRV","Server-Database","Gateway-Generic", "Gateway-Firewall","Gateway-VPN","Gateway-Router","Gateway-Switch","Gateway-Email","Gateway-Web Proxy","Gateway-DNS","Desktop","Mobile","OT Device","UnKnown"])
                input_field = combo_box
            elif header and header.strip().lower() == "location":
                combo_box = QComboBox()
                combo_box.addItems(["On-Prem", "Unknown", "Cloud-Generic", "Cloud-Azure", "Cloud-AWS", "Clous-GCP"])
                input_field = combo_box
            elif header and header.strip().lower() == "currentstatus":
                combo_box = QComboBox()
                combo_box.addItems(["Completed" , "In Progress", "On Hold", "Not Started"  ])
                input_field = combo_box                
            elif header and header.strip().lower() == "priority":
                combo_box = QComboBox()
                combo_box.addItems(["High" ,"Medium" ,"Low" ])
                input_field = combo_box                
            elif header and header.strip().lower() == "evidencecollected":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No" ])
                input_field = combo_box                
            elif header and header.strip().lower() == "entrypoint":
                combo_box = QComboBox()
                combo_box.addItems(["Yes", "No" ])
                input_field = combo_box                
            elif header and header.strip().lower() == "targettype":
                combo_box = QComboBox()
                combo_box.addItems(["Machine", "Identity", "Others"])
                input_field = combo_box                
            elif header and (header.strip().lower() == "notes" or header.strip().lower() == "activity"):
                text_edit = QTextEdit()
                input_field = text_edit
            elif header and "timestamp" in header.strip().lower() and "utc" in header.strip().lower():
                line_edit = QLineEdit()
                line_edit.setPlaceholderText("YYYY-MM-DD HH:MM:SS")
                line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
                input_field = line_edit
            elif header and header.strip().lower() in ["date added", "date updated", "date completed", "date requested", "date received"]:
                date_edit = QDateEdit()
                date_edit.setCalendarPopup(True)  
                date_edit.setDate(QDate.currentDate())  
                input_field = date_edit
            elif header == "MITRE Tactic":
                combo_box = QComboBox()
                combo_box.addItem("")  
                combo_box.addItems(mitre_tactic_options)
                mitre_tactic_combo = combo_box
                input_field = combo_box
            elif header == "MITRE Techniques":
                combo_box = QComboBox()
                combo_box.addItem("")  
                mitre_technique_combo = combo_box
                input_field = combo_box
            elif header == "TLP":
                combo_box = QComboBox()
                combo_box.addItems(["TLP-Red", "TLP-Amber_Strict", "TLP-Amber", "TLP-Green", "TLP-Clear"])
                input_field = combo_box
            elif header == "<->":
                combo_box = QComboBox()
                combo_box.addItems([" ","->", "<-", "<->"])
                input_field = combo_box
            else:
                line_edit = QLineEdit()
                line_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
                input_field = line_edit
            grid_layout.addWidget(header_label, col, 0)
            grid_layout.addWidget(input_field, col, 1)
            input_fields.append(input_field)
        if mitre_tactic_combo and mitre_technique_combo:
            current_technique = mitre_technique_combo.currentText()
            
            def update_techniques(tactic_index):
                current_selection = mitre_technique_combo.currentText()
                selected_tactic = mitre_tactic_combo.currentText()
                mitre_technique_combo.clear()
                mitre_technique_combo.addItem("")  
                if selected_tactic:
                    techniques = mitre_techniques_by_tactic.get(selected_tactic, [])
                    mitre_technique_combo.addItems(techniques)
                    if current_selection and current_selection in techniques:
                        mitre_technique_combo.setCurrentText(current_selection)
                else:
                    if current_selection:
                        mitre_technique_combo.addItem(current_selection)
                        mitre_technique_combo.setCurrentText(current_selection)
            mitre_tactic_combo.currentIndexChanged.connect(update_techniques)
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        save_button = QPushButton("Save")
        cancel_button = QPushButton("Cancel")
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        button_layout.addStretch()
        main_layout.addLayout(grid_layout)
        main_layout.addLayout(button_layout)
    
        def save_new_row():
            try:
                if not self.current_workbook or not self.current_sheet_name:
                    QMessageBox.critical(self.window, "Error", "No workbook or sheet loaded to save changes.")
                    return
                sheet = self.current_workbook[self.current_sheet_name]
                new_row_data = []
                for field in input_fields:
                    if isinstance(field, QComboBox):
                        new_text = field.currentText()
                    elif isinstance(field, QTextEdit):
                        new_text = field.toPlainText()
                    elif isinstance(field, QDateEdit):
                        new_text = field.date().toString("yyyy-MM-dd")  
                    else:
                        new_text = field.text()
                    new_row_data.append(new_text)
                # Save to Excel file with synchronization handling
                try:
                    # Temporarily disable file watcher to prevent recursive updates
                    self.sync_in_progress = True

                    # Add row to Excel at the correct position (same pattern as Edit Row)
                    workbook = self.current_workbook
                    sheet = workbook[self.current_sheet_name]

                    # Get sheet configuration to find data start row
                    sheet_config = self.get_sheet_config(self.current_sheet_name)
                    data_start_row = sheet_config["data_start_row"]

                    # Find the last row with data (starting from data_start_row)
                    last_data_row = data_start_row - 1  # Start before data area
                    for row in range(data_start_row, sheet.max_row + 1):
                        has_data = False
                        for col in range(1, sheet.max_column + 1):
                            if sheet.cell(row=row, column=col).value is not None:
                                has_data = True
                                break
                        if has_data:
                            last_data_row = row

                    # Insert new row after the last data row
                    new_row_number = last_data_row + 1
                    for col, value in enumerate(new_row_data, 1):
                        sheet.cell(row=new_row_number, column=col, value=value)

                    self.logger.info(f"Added new row at row {new_row_number} in sheet '{self.current_sheet_name}'")

                    # Save to Excel with proper error handling
                    try:
                        # Temporarily disable file watcher to prevent recursive updates
                        self.sync_in_progress = True
                        lock_acquired = False

                        try:
                            # Acquire KANVAS file lock to prevent other KANVAS instances from saving
                            if not self.acquire_file_lock(self.current_file_path):
                                QMessageBox.warning(self.window, "File Busy",
                                    "Another KANVAS user is currently saving to this file.\n"
                                    "Please try again in a moment.")
                                return
                            lock_acquired = True

                            # Check for external changes before saving (conflict detection)
                            current_file_time = os.path.getmtime(self.current_file_path)
                            if self.last_modified_time and current_file_time > self.last_modified_time:
                                reply = QMessageBox.question(self.window, "File Conflict Detected",
                                    "⚠️ The Excel file has been modified by another user since you last loaded it.\n\n"
                                    "Your options:\n"
                                    "• YES: Save anyway (may overwrite other user's changes)\n"
                                    "• NO: Cancel and reload to see latest changes first\n\n"
                                    "Do you want to save anyway?",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

                                if reply == QMessageBox.No:
                                    QMessageBox.information(self.window, "Save Cancelled",
                                        "Save cancelled. Please reload the file to see latest changes.")
                                    return

                            # Check if file is locked/open in Excel
                            if self.is_file_locked(self.current_file_path):
                                QMessageBox.warning(self.window, "File Locked",
                                    "The Excel file appears to be open in Excel or another application.\n"
                                    "Please close the file in Excel and try again.")
                                return

                            workbook.save(self.current_file_path)

                        finally:
                            # Always release the lock
                            if lock_acquired:
                                self.release_file_lock()

                        # Update the last modified time
                        self.last_modified_time = os.path.getmtime(self.current_file_path)

                    except PermissionError as e:
                        QMessageBox.critical(self.window, "Permission Error",
                            f"Cannot save to Excel file. The file may be:\n"
                            f"• Open in Excel\n"
                            f"• Read-only\n"
                            f"• Locked by another process\n\n"
                            f"Error: {e}")
                        return
                    except Exception as e:
                        QMessageBox.critical(self.window, "Save Error", f"Failed to save to Excel: {e}")
                        return
                    finally:
                        # Re-enable file watcher
                        self.sync_in_progress = False

                    # Reload the sheet view
                    self.load_sheet()

                    self.logger.info(f"Added new row and saved to Excel file")
                    QMessageBox.information(self.window, "Row Added", "Row added and saved to Excel successfully!")

                except Exception as e:
                    self.logger.error(f"Error saving new row to Excel: {e}")
                    QMessageBox.critical(self.window, "Error", f"Failed to save row to Excel: {e}")
                finally:
                    # Re-enable file watcher
                    self.sync_in_progress = False

                add_row_window.close()
            except Exception as e:
                self.logger.error(f"Failed to save changes: {e}")
                QMessageBox.critical(add_row_window, "Error", f"Failed to save changes: {e}")
        save_button.clicked.connect(save_new_row)
        cancel_button.clicked.connect(add_row_window.close)
        add_row_window.show()
        self.track_child_window(add_row_window)

    def delete_row(self):
        if self.read_only_mode:
            QMessageBox.information(self.window, "Read-Only Mode", "This file is open in read-only mode. You cannot delete rows.")
            return
        if not self.current_sheet_name or not self.current_workbook:
            QMessageBox.warning(self.window, "Warning", "Please select a sheet.")
            return
        if not self.tree_view or not self.tree_view.model():
            QMessageBox.warning(self.window, "Warning", "Tree view not available.")
            return
        selected_indices = self.tree_view.selectionModel().selectedRows()
        if not selected_indices:
            QMessageBox.warning(self.window, "Warning", "Please select rows to delete.")
            return
        row_indices = sorted([index.row() + 2 for index in selected_indices], reverse=True)
        confirm = QMessageBox.question(self.window, "Confirm Deletion", f"Are you sure you want to delete {len(row_indices)} row(s)?", QMessageBox.Yes | QMessageBox.No)
        if confirm == QMessageBox.No:
            return
        sheet = self.current_workbook[self.current_sheet_name]
        try:
            # TEMPORARILY DISABLE Excel file writing to prevent corruption
            # for row_idx in row_indices:
            #     sheet.delete_rows(row_idx, 1)
            # self.current_workbook.save(self.current_file_path)

            # Instead, remove from tree view only
            model = self.tree_view.model()
            if model:
                # Remove rows from model (in reverse order to maintain indices)
                for row_idx in sorted(row_indices, reverse=True):
                    if row_idx < model.rowCount():
                        model.removeRow(row_idx)
                QMessageBox.information(self.window, "Rows Removed",
                    f"Removed {len(row_indices)} rows from view.\nNote: Changes are not saved to Excel file to prevent corruption.")
            else:
                QMessageBox.warning(self.window, "Error", "Could not delete rows - no data model available")
        except PermissionError:
            self.logger.error("Permission error: Cannot write to file")
            QMessageBox.critical(self.window, "Error", "Cannot save file. Ensure it is not open in another program.")
        except Exception as e:
            self.logger.error(f"Error deleting rows: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to delete rows: {e}")

    def load_sheet(self):
        if not self.current_workbook or not self.current_sheet_name:
            selected_sheet = self.window.ui.comboBoxSheet.currentText()
            if selected_sheet and self.current_workbook:
                self.current_sheet_name = selected_sheet
            else:
                self.logger.error("Cannot load sheet: Missing workbook or sheet name")
                return

        try:
            sheet = self.current_workbook[self.current_sheet_name]
            model = QStandardItemModel()

            # Get sheet configuration
            sheet_config = self.get_sheet_config(self.current_sheet_name)
            header_row = sheet_config["header_row"]
            dropdown_row = sheet_config["dropdown_row"]
            data_start_row = sheet_config["data_start_row"]

            self.logger.info(f"Loading sheet '{self.current_sheet_name}' with config: headers={header_row}, dropdowns={dropdown_row}, data_start={data_start_row}")

            # Get headers from configured header row
            headers = []
            for col in range(1, sheet.max_column + 1):
                header = sheet.cell(row=header_row, column=col).value
                if header:
                    headers.append(str(header))
                else:
                    headers.append(f"Column {col}")

            model.setHorizontalHeaderLabels(headers)

            # Read dropdown values from configured dropdown row
            dropdown_values = self.read_dropdown_values(sheet, dropdown_row, sheet.max_column)
            if dropdown_values:
                self.current_dropdown_values = dropdown_values
                self.window.current_dropdown_values = dropdown_values
                self.logger.info(f"Loaded dropdown values: {dropdown_values}")

            # Load data rows starting from configured data start row
            for row_idx in range(data_start_row, sheet.max_row + 1):
                row_data = []
                row_colors = []
                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=row_idx, column=col)
                    cell_value = cell.value
                    row_data.append(str(cell_value) if cell_value is not None else "")

                    # Get cell background color if it exists
                    cell_color = None
                    if cell.fill and cell.fill.start_color and cell.fill.start_color.rgb:
                        try:
                            # Convert Excel color to QColor
                            rgb_hex = cell.fill.start_color.rgb
                            if rgb_hex and rgb_hex != "00000000":  # Not default/no color
                                # Remove alpha channel and convert
                                rgb_hex = rgb_hex[-6:]  # Take last 6 characters (RGB)
                                r = int(rgb_hex[0:2], 16)
                                g = int(rgb_hex[2:4], 16)
                                b = int(rgb_hex[4:6], 16)
                                cell_color = QColor(r, g, b, 150)  # Semi-transparent
                        except:
                            cell_color = None

                    row_colors.append(cell_color)

                # Create items with colors preserved
                items = []
                for value, color in zip(row_data, row_colors):
                    item = QStandardItem(value)
                    if color:
                        item.setData(color, Qt.BackgroundRole)
                    items.append(item)

                model.appendRow(items)
            
            # Set the model to the tree view
            self.tree_view.setModel(model)

            # Ensure headers are visible (like Excel spreadsheet)
            self.tree_view.header().setVisible(True)
            self.tree_view.header().setStretchLastSection(False)
            self.tree_view.header().setSectionResizeMode(QHeaderView.Interactive)

            self.tree_view.setEditTriggers(QTreeView.NoEditTriggers)

            # Adjust column widths
            for col_idx, header in enumerate(headers):
                self.tree_view.setColumnWidth(col_idx, 100)
            
        except Exception as e:
            self.logger.error(f"Error loading sheet: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to load sheet: {e}")

    def run(self):
        self.app.aboutToQuit.connect(self.application_cleanup)
        return self.app.exec()

    def list_systems(self):
        if not self.current_workbook:
            QMessageBox.warning(self.window, "Warning", "No workbook loaded. Please load a workbook first.")
            return

        # List Systems should come from Evidence Management sheet
        if "Evidence Management" not in self.current_workbook.sheetnames:
            QMessageBox.warning(self.window, "Missing Sheet",
                f"Evidence Management sheet not found. Available sheets: {', '.join(self.current_workbook.sheetnames)}")
            return

        try:
            sheet = self.current_workbook["Evidence Management"]

            # Get sheet configuration for Evidence Management
            sheet_config = self.get_sheet_config("Evidence Management")
            header_row = sheet_config["header_row"]
            data_start_row = sheet_config["data_start_row"]

            self.logger.info(f"List Systems using Evidence Management sheet: header row {header_row}, data start row {data_start_row}")

            # Look for "System/Collection Name" column
            system_col = None
            for col in range(1, sheet.max_column + 1):
                header = sheet.cell(row=header_row, column=col).value
                if header and "System" in str(header) and "Collection" in str(header):
                    system_col = col
                    self.logger.info(f"Found System/Collection Name column at position {col}: '{header}'")
                    break

            if system_col is None:
                QMessageBox.warning(self.window, "Missing Column",
                    "System/Collection Name column not found in Evidence Management sheet.")
                return

            # Collect unique systems from System/Collection Name column
            systems = set()
            for row in range(data_start_row, sheet.max_row + 1):
                system = sheet.cell(row=row, column=system_col).value
                if system and str(system).strip():
                    systems.add(str(system).strip())

            sorted_systems = sorted(list(systems))
            self.logger.info(f"Found {len(sorted_systems)} unique systems in Evidence Management sheet")

        except Exception as e:
            self.logger.error(f"Error listing systems: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to list systems: {e}")
            return

        # Create and show the systems window
        systems_window = QWidget(self.window)
        systems_window.setWindowTitle("Unique Systems")
        systems_window.resize(400, 500)
        systems_window.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        layout = QVBoxLayout(systems_window)
        label = QLabel(f"Found {len(sorted_systems)} unique systems:")
        label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(label)
        list_widget = QTreeView()
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(["System Name"])
        for system in sorted_systems:
            item = QStandardItem(system)
            model.appendRow(item)
        list_widget.setModel(model)
        list_widget.setAlternatingRowColors(True)
        list_widget.setEditTriggers(QTreeView.NoEditTriggers)
        header = list_widget.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        layout.addWidget(list_widget, 1) 
        button_layout = QHBoxLayout()
        close_button = QPushButton("Close")
        close_button.clicked.connect(systems_window.close)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        self.child_windows.append(systems_window)
        systems_window.show()

    def close_systems_window(self):
        if hasattr(self, 'systems_window'):
            self.systems_window.close()
            self.systems_window = None

    def list_users(self):
        if not self.current_workbook:
            QMessageBox.warning(self.window, "Warning", "No workbook loaded. Please load a workbook first.")
            return

        # List Users should come from Timeline (Master) sheet
        if "Timeline (Master)" not in self.current_workbook.sheetnames:
            QMessageBox.warning(self.window, "Missing Sheet",
                f"Timeline (Master) sheet not found. Available sheets: {', '.join(self.current_workbook.sheetnames)}")
            return

        try:
            sheet = self.current_workbook["Timeline (Master)"]

            # Get sheet configuration for Timeline (Master)
            sheet_config = self.get_sheet_config("Timeline (Master)")
            header_row = sheet_config["header_row"]
            data_start_row = sheet_config["data_start_row"]

            self.logger.info(f"List Users using Timeline (Master) sheet: header row {header_row}, data start row {data_start_row}")

            # Look for "Account Name" column
            account_col = None
            for col in range(1, sheet.max_column + 1):
                header = sheet.cell(row=header_row, column=col).value
                if header and "Account" in str(header) and "Name" in str(header):
                    account_col = col
                    self.logger.info(f"Found Account Name column at position {col}: '{header}'")
                    break

            if account_col is None:
                QMessageBox.warning(self.window, "Missing Column",
                    "Account Name column not found in Timeline (Master) sheet.")
                return

            # Collect unique users from Account Name column
            users = set()
            for row in range(data_start_row, sheet.max_row + 1):
                user = sheet.cell(row=row, column=account_col).value
                if user and str(user).strip():
                    users.add(str(user).strip())

            sorted_users = sorted(list(users))
            self.logger.info(f"Found {len(sorted_users)} unique users in Timeline (Master) sheet")

        except Exception as e:
            self.logger.error(f"Error listing users: {e}")
            QMessageBox.critical(self.window, "Error", f"Failed to list users: {e}")
            return

        # Create and show the users window
        users_window = QWidget(self.window)
        users_window.setWindowTitle("Unique Users")
        users_window.resize(400, 500)
        users_window.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        layout = QVBoxLayout(users_window)
        label = QLabel(f"Found {len(sorted_users)} unique users:")
        label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(label)
        list_widget = QTreeView()
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(["Username"])
        for user in sorted_users:
            item = QStandardItem(user)
            model.appendRow(item)
        list_widget.setModel(model)
        list_widget.setAlternatingRowColors(True)
        list_widget.setEditTriggers(QTreeView.NoEditTriggers)
        header = list_widget.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        layout.addWidget(list_widget, 1)  
        button_layout = QHBoxLayout()
        close_button = QPushButton("Close")
        close_button.clicked.connect(users_window.close)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        self.child_windows.append(users_window)
        users_window.show()

    def close_users_window(self):
        if hasattr(self, 'users_window'):
            self.users_window.close()
            self.users_window = None

    def show_context_menu(self, position):
        """Show context menu for tree view with copy/paste/edit options"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        # Get the item at the clicked position
        index = self.tree_view.indexAt(position)

        # Create context menu
        context_menu = QMenu(self.tree_view)

        # Copy action
        copy_action = context_menu.addAction("📋 Copy")
        copy_action.triggered.connect(self.copy_selected_cells)

        # Paste action
        paste_action = context_menu.addAction("📄 Paste")
        paste_action.triggered.connect(self.paste_to_selected_cells)

        context_menu.addSeparator()

        # Edit action
        if index.isValid():
            edit_action = context_menu.addAction("✏️ Edit Cell")
            edit_action.triggered.connect(lambda: self.tree_view.edit(index))

        # Highlight actions
        context_menu.addSeparator()
        highlight_menu = context_menu.addMenu("🎨 Highlight")

        # Excel-like colors
        highlight_yellow = highlight_menu.addAction("🟡 Yellow")
        highlight_yellow.triggered.connect(lambda: self.highlight_selected_cells(QColor(255, 255, 0, 150)))

        highlight_green = highlight_menu.addAction("🟢 Light Green")
        highlight_green.triggered.connect(lambda: self.highlight_selected_cells(QColor(144, 238, 144, 150)))

        highlight_red = highlight_menu.addAction("🔴 Light Red")
        highlight_red.triggered.connect(lambda: self.highlight_selected_cells(QColor(255, 182, 193, 150)))

        highlight_blue = highlight_menu.addAction("🔵 Light Blue")
        highlight_blue.triggered.connect(lambda: self.highlight_selected_cells(QColor(173, 216, 230, 150)))

        highlight_orange = highlight_menu.addAction("🟠 Light Orange")
        highlight_orange.triggered.connect(lambda: self.highlight_selected_cells(QColor(255, 218, 185, 150)))

        highlight_purple = highlight_menu.addAction("🟣 Light Purple")
        highlight_purple.triggered.connect(lambda: self.highlight_selected_cells(QColor(221, 160, 221, 150)))

        highlight_gray = highlight_menu.addAction("⚪ Light Gray")
        highlight_gray.triggered.connect(lambda: self.highlight_selected_cells(QColor(211, 211, 211, 150)))

        highlight_menu.addSeparator()

        # Darker colors
        highlight_dark_green = highlight_menu.addAction("🟢 Dark Green")
        highlight_dark_green.triggered.connect(lambda: self.highlight_selected_cells(QColor(0, 128, 0, 200)))

        highlight_dark_red = highlight_menu.addAction("🔴 Dark Red")
        highlight_dark_red.triggered.connect(lambda: self.highlight_selected_cells(QColor(139, 0, 0, 200)))

        highlight_dark_blue = highlight_menu.addAction("🔵 Dark Blue")
        highlight_dark_blue.triggered.connect(lambda: self.highlight_selected_cells(QColor(0, 0, 139, 200)))

        highlight_menu.addSeparator()

        # Custom color picker
        highlight_custom = highlight_menu.addAction("🎨 Custom Color...")
        highlight_custom.triggered.connect(self.choose_custom_highlight_color)

        highlight_clear = highlight_menu.addAction("🚫 Clear Highlight")
        highlight_clear.triggered.connect(lambda: self.highlight_selected_cells(None))

        context_menu.addSeparator()

        # Row operations
        if index.isValid():
            row_menu = context_menu.addMenu("📊 Row Operations")

            add_row_action = row_menu.addAction("➕ Add Row")
            add_row_action.triggered.connect(self.add_new_row)

            delete_row_action = row_menu.addAction("🗑️ Delete Row")
            delete_row_action.triggered.connect(lambda: self.delete_selected_row(index))

        # Show the menu
        context_menu.exec(self.tree_view.mapToGlobal(position))

    def copy_selected_cells(self):
        """Copy selected cells to clipboard"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        selection = self.tree_view.selectionModel().selectedIndexes()
        if not selection:
            return

        # Sort selection by row and column
        selection.sort(key=lambda x: (x.row(), x.column()))

        # Group by rows
        rows = {}
        for index in selection:
            row = index.row()
            if row not in rows:
                rows[row] = []
            rows[row].append(index)

        # Build clipboard text
        clipboard_text = []
        for row in sorted(rows.keys()):
            row_data = []
            for index in sorted(rows[row], key=lambda x: x.column()):
                data = index.data() or ""
                row_data.append(str(data))
            clipboard_text.append("\t".join(row_data))

        # Copy to clipboard
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(clipboard_text))

        self.logger.info(f"Copied {len(selection)} cells to clipboard")

    def paste_to_selected_cells(self):
        """Paste from clipboard to selected cells"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        clipboard = QApplication.clipboard()
        text = clipboard.text()

        if not text:
            return

        selection = self.tree_view.selectionModel().selectedIndexes()
        if not selection:
            return

        # Get the top-left cell of selection
        top_left = min(selection, key=lambda x: (x.row(), x.column()))

        # Parse clipboard data
        lines = text.split('\n')
        model = self.tree_view.model()

        for row_offset, line in enumerate(lines):
            if not line.strip():
                continue

            cells = line.split('\t')
            for col_offset, cell_data in enumerate(cells):
                target_row = top_left.row() + row_offset
                target_col = top_left.column() + col_offset

                # Check bounds
                if (target_row < model.rowCount() and
                    target_col < model.columnCount()):

                    index = model.index(target_row, target_col)
                    model.setData(index, cell_data)

        self.logger.info(f"Pasted data to {len(lines)} rows")

    def highlight_selected_cells(self, color):
        """Highlight selected cells with the given color"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        selection = self.tree_view.selectionModel().selectedIndexes()
        if not selection:
            return

        model = self.tree_view.model()

        for index in selection:
            if color:
                # Set background color
                model.setData(index, color, Qt.BackgroundRole)
            else:
                # Clear background color
                model.setData(index, None, Qt.BackgroundRole)

        color_name = "cleared" if not color else f"RGB({color.red()}, {color.green()}, {color.blue()})"
        self.logger.info(f"Highlighted {len(selection)} cells with color: {color_name}")

    def choose_custom_highlight_color(self):
        """Open color picker for custom highlight color"""
        from PySide6.QtWidgets import QColorDialog

        color = QColorDialog.getColor(QColor(255, 255, 0), self.window, "Choose Highlight Color")

        if color.isValid():
            # Make it semi-transparent for highlighting
            color.setAlpha(150)
            self.highlight_selected_cells(color)

    def delete_selected_row(self, index):
        """Delete the selected row immediately"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        model = self.tree_view.model()
        row_number = index.row() + 1
        model.removeRow(index.row())
        self.logger.info(f"Deleted row {row_number}")

        # Show brief status message
        if hasattr(self, 'window') and hasattr(self.window, 'statusBar'):
            self.window.statusBar().showMessage(f"Deleted row {row_number}", 2000)

    def setup_tree_view_shortcuts(self):
        """Setup keyboard shortcuts for the tree view"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        from PySide6.QtGui import QShortcut, QKeySequence

        # Ctrl+C for copy
        copy_shortcut = QShortcut(QKeySequence.Copy, self.tree_view)
        copy_shortcut.activated.connect(self.copy_selected_cells)

        # Ctrl+V for paste
        paste_shortcut = QShortcut(QKeySequence.Paste, self.tree_view)
        paste_shortcut.activated.connect(self.paste_to_selected_cells)

        # F2 for edit
        edit_shortcut = QShortcut(QKeySequence("F2"), self.tree_view)
        edit_shortcut.activated.connect(self.edit_current_cell)

        # Delete key for delete row
        delete_shortcut = QShortcut(QKeySequence.Delete, self.tree_view)
        delete_shortcut.activated.connect(self.delete_current_row)

        # Ctrl+A for select all
        select_all_shortcut = QShortcut(QKeySequence.SelectAll, self.tree_view)
        select_all_shortcut.activated.connect(self.tree_view.selectAll)

        self.logger.info("Tree view keyboard shortcuts enabled: Ctrl+C (copy), Ctrl+V (paste), F2 (edit), Delete (delete row), Ctrl+A (select all)")

    def edit_current_cell(self):
        """Edit the currently selected cell"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        current_index = self.tree_view.currentIndex()
        if current_index.isValid():
            self.tree_view.edit(current_index)

    def delete_current_row(self):
        """Delete the currently selected row"""
        if not hasattr(self, 'tree_view') or not self.tree_view:
            return

        current_index = self.tree_view.currentIndex()
        if current_index.isValid():
            self.delete_selected_row(current_index)

    def sanitize(self):
        if not self.check_excel_loaded():
            return
        try:
            sanitized_file_path, _ = QFileDialog.getSaveFileName(self.window, "Save Sanitized Excel File", os.path.splitext(self.current_file_path)[0] + "_sanitized.xlsx", "Excel files (*.xlsx)")
            if not sanitized_file_path:
                return
            lock_acquired = False
            try:
                lock_status = self.acquire_file_lock(sanitized_file_path)
                if lock_status is None:  
                    return
                lock_acquired = True
                progress = QProgressBar()
                progress.setWindowTitle("Sanitizing File")
                progress.setGeometry(QRect(300, 300, 400, 30))
                progress.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
                progress.setRange(0, len(self.current_workbook.sheetnames))
                progress.setValue(0)
                progress.show()
                ip_regex = re.compile(r'(\d{1,3}\.\d{1,3}\.\d{1,3})\.(\d{1,3})')
                http_regex = re.compile(r'(https?)(://)', re.IGNORECASE)
                domain_regex = re.compile(r'([a-zA-Z0-9][-a-zA-Z0-9]*\.[a-zA-Z0-9][-a-zA-Z0-9]*)\.([a-zA-Z]{2,})')
                for sheet_idx, sheet_name in enumerate(self.current_workbook.sheetnames):
                    progress.setValue(sheet_idx)
                    progress.setWindowTitle(f"Sanitizing: {sheet_name}")
                    QApplication.processEvents()
                    sheet = self.current_workbook[sheet_name]
                    for row in range(1, sheet.max_row + 1):
                        for col in range(1, sheet.max_column + 1):
                            cell = sheet.cell(row=row, column=col)
                            if cell.value and isinstance(cell.value, str):
                                cell.value = ip_regex.sub(r'\1[.]\2', cell.value)
                                cell.value = http_regex.sub(r'hxxp\2', cell.value)
                                cell.value = domain_regex.sub(r'\1[.]\2', cell.value)
                self.current_workbook.save(sanitized_file_path)
                progress.close()
                QMessageBox.information(self.window, "Sanitization Complete", f"File has been sanitized and saved to:\n{sanitized_file_path}"
                )
                reply = QMessageBox.question(self.window, "Load Sanitized File", "Do you want to load the sanitized file?", QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
                if reply == QMessageBox.Yes:
                    self.release_file_lock()
                    lock_acquired = False
                    self.load_data_into_treeview(file_path=sanitized_file_path)
            finally:
                if lock_acquired:
                    self.release_file_lock()
        except Exception as e:
            self.logger.error(f"Error during sanitization: {e}")
            traceback.print_exc()
            QMessageBox.critical(self.window, "Sanitization Error", f"An error occurred while sanitizing the file:\n{str(e)}")

    def __del__(self):
        self.logger.info("MainApp destructor called, cleaning up resources...")
        self.close_all_windows()
        
    def close_all_windows(self):
        if hasattr(self, 'child_windows'):
            for window in self.child_windows[:]:  
                try:
                    if (window and 
                        hasattr(window, 'isVisible') and 
                        isValid(window) and  
                        window.isVisible()):
                        window.close()
                except RuntimeError as e:
                    if "already deleted" in str(e):
                        self.logger.info(f"Window already deleted: {e}")
                    else:
                        self.logger.error(f"Error closing window: {e}")
                except Exception as e:
                    self.logger.error(f"Error closing window: {e}")
            self.child_windows.clear()

    def exit_application(self):
        self.close_all_windows()
        self.window.close()
        self.app.quit()

    def track_child_window(self, window):
        if window:
            if hasattr(window, 'setParent') and window.parent() is None:
                window.setParent(self.window, Qt.Window)
            self.child_windows.append(window)
            window.setAttribute(Qt.WA_DeleteOnClose, False)
            original_close = window.closeEvent if hasattr(window, 'closeEvent') else None
            
            def new_close_event(event):
                if window in self.child_windows:
                    self.child_windows.remove(window)
                if original_close:
                    original_close(event)
                else:
                    event.accept()
            window.closeEvent = new_close_event
            return window
        return None

    def application_cleanup(self):
        self.release_file_lock()
        for window in self.child_windows[:]:
            try:
                if window and isValid(window):  
                    self.logger.info(f"Final cleanup of window: {window.windowTitle() if hasattr(window, 'windowTitle') else 'Unknown'}")
                    window.setAttribute(Qt.WA_DeleteOnClose, True)
                    window.close()
            except RuntimeError as e:
                if "already deleted" in str(e):
                    self.logger.info(f"Window already deleted: {e}")
                else:
                    self.logger.error(f"Error in final cleanup: {e}")
            except Exception as e:
                self.logger.error(f"Error in final cleanup: {e}")


if __name__ == "__main__":
    main_app = MainApp()
    sys.exit(main_app.run())
