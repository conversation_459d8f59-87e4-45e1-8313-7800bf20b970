#!/usr/bin/env python3
"""Safe Excel reader that won't corrupt .xlsm files"""

import openpyxl
import os
import shutil
from datetime import datetime

def safe_read_excel():
    print("🔧 Safe Excel File Analysis")
    print("=" * 40)
    
    original_file = "ABZ_CompanyName_IR_Project_Workbook_Template.xlsm"
    
    if not os.path.exists(original_file):
        print(f"❌ File not found: {original_file}")
        return
    
    try:
        # Create a temporary copy to work with
        temp_file = f"temp_analysis_{datetime.now().strftime('%H%M%S')}.xlsm"
        shutil.copy2(original_file, temp_file)
        print(f"📋 Created temporary copy: {temp_file}")
        
        # Try to read with minimal operations
        print(f"🔍 Attempting safe read...")
        workbook = openpyxl.load_workbook(temp_file, read_only=True, keep_vba=True, data_only=True)
        
        print(f"✅ Successfully opened file!")
        print(f"📋 Sheets: {workbook.sheetnames}")
        
        # Check DataValidation sheet safely
        if "DataValidation" in workbook.sheetnames:
            print(f"\n📊 DataValidation sheet preview:")
            validation_sheet = workbook["DataValidation"]
            
            # Read first few rows safely
            for row in range(1, 6):
                row_data = []
                for col in range(1, 7):  # Columns A-F
                    try:
                        cell = validation_sheet.cell(row=row, column=col)
                        value = cell.value if cell.value else ""
                        row_data.append(str(value)[:20])  # Limit length
                    except:
                        row_data.append("ERROR")
                print(f"  Row {row}: {row_data}")
        
        workbook.close()
        
        # Clean up temp file
        os.remove(temp_file)
        print(f"🧹 Cleaned up temporary file")
        
        print(f"\n✅ File appears to be readable!")
        print(f"💡 The corruption might be in Excel's cache or file associations")
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        # Clean up temp file if it exists
        if os.path.exists(temp_file):
            os.remove(temp_file)

if __name__ == "__main__":
    safe_read_excel()
